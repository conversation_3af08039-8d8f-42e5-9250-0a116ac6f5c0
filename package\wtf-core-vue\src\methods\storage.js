import Cookies from 'js-cookie';
import { encrypt, decrypt } from '../utils/crypto.js';

/**
 * @param {string} name
 * @returns {string}
 * 获取cookie
 */
const getCookies = name => {
    let value = Cookies.get(name) || '';
    value = decrypt(value, 'BASE64');
    return value;
};

/**
 * @param {string} name
 * @param {string} value
 * @param {object} options
 * 设置 cookie
 */
const setCookies = (name, value, options) => {
    value = encrypt(value, 'BASE64');
    Cookies.set(name, value, options);
};

/**
 * 移除Cookies
 * @param {*} name
 */
const removeCookies = name => {
    Cookies.remove(name);
};

/**
 * 获取localStorage数据
 * @param {*} name 参数key
 * @returns {string}
 */
const getLocalStorage = name => {
    const data = decrypt(window.localStorage.getItem(name), 'BASE64');
    // const data = window.localStorage.getItem(name);
    try {
        // data为json格式字符串，转为js对象返回
        return JSON.parse(data);
    } catch (err) {
        // data不是json格式字符串，原样返回
        return data;
    }
};

/**
 * 设置localStorage
 * @param {*} name -- key
 * @param {*} value
 */
const setLocalStorage = (name, value) => {
    // 判断value是引用类型，将其转为json格式字符串存储 encrypt(value,'BASE64')
    if (typeof value === 'object') {
        value = JSON.stringify(value);
    }
    value = encrypt(value, 'BASE64');
    window.localStorage.setItem(name, value);
};

/**
 * 移除LocalStorage
 * @param {*} name -- key
 */
const removeLocalStorage = (name) => {
    window.localStorage.removeItem(name);
};

/**
 * 获取sessionstorage数据
 * @param {*} name 参数key
 * @returns {string}
 */
const getSessionStorage = name => {
    const data = decrypt(window.sessionStorage.getItem(name), 'BASE64');
    try {
        // data为json格式字符串，转为js对象返回
        return JSON.parse(data);
    } catch (err) {
        // data不是json格式字符串，原样返回
        return data;
    }
};

/**
 * 设置sessionstorage
 * @param {*} name -- key
 * @param {*} value
 */
const setSessionStorage = (name, value) => {
    // 判断value是引用类型，将其转为json格式字符串存储
    if (typeof value === 'object') {
        value = JSON.stringify(value);
    }
    value = encrypt(value, 'BASE64');
    window.sessionStorage.setItem(name, value);
};

/**
 * 移除LocalStorage
 * @param {*} name -- key
 */
const removeSessionStorage = (name) => {
    window.sessionStorage.removeItem(name);
};

/**
 * 全部清除storage
 */
const clearStorageAll = () => {
    window.localStorage.clear();
    window.sessionStorage.clear();
};

export default {
    getCookies,
    setCookies,
    removeCookies,
    getLocalStorage,
    setLocalStorage,
    removeLocalStorage,
    getSessionStorage,
    setSessionStorage,
    removeSessionStorage,
    clearStorageAll
};
