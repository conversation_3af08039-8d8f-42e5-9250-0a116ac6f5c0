<template>
    <el-form-item v-bind="elFormItemAttrs">
        <el-cascader
            v-model="config.modelObj[config.modelKey]"
            v-bind="elCascaderAttrs"
            :props="props"
        />
    </el-form-item>
</template>
<script>
export default {
    name: 'SnbcFormRegion',
    props: {
        /**
         * SnbcFormRegion组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    // 值示例: ['北京市', '北京市', '东城区']
                    modelKey: '',
                    // province|city|area
                    type: 'area',
                    elFormItemAttrs: {},
                    elCascaderAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-cascader组件默认属性设置
            defaultElCascaderAttrs: {
                clearable: true
            },
            // 配置选项
            props: {
                lazy: true,
                lazyLoad: this.lazyLoad
            }
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-cascader组件应用属性
        elCascaderAttrs() {
            return {
                ...this.defaultElCascaderAttrs,
                placeholder: `请选择${this.config.name}`,
                ...(this.config.elCascaderAttrs || {})
            };
        },
        // 区域选择类型 province|city|area
        type() {
            return this.config.type;
        }
    },
    methods: {
        // 加载动态数据
        lazyLoad(node, resolve) {
            const { level } = node;
            if (level === 0) {
                this.getProvince(node, resolve);
            } else if (level === 1) {
                this.getCityByProvinceName(node, resolve);
            } else if (level === 2) {
                this.getAreaByCityName(node, resolve);
            }
        },
        // 获取省份数据
        async getProvince(node, resolve) {
            try {
                const res = await this.$service.warehouse.region.getProvince();
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                const nodes = result.map((item) => {
                    return {
                        ...item,
                        value: item.name,
                        label: item.name,
                        leaf: this.type === 'province'
                    };
                });
                resolve(nodes);
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error(error);
                this.$tools.message.err('系统异常');
                this.$set(node, 'loading', false);
            }
        },
        // 获取城市数据
        async getCityByProvinceName(node, resolve) {
            const provinceName = node.value;
            try {
                const res =
                    await this.$service.warehouse.region.getCityByProvinceName(
                        provinceName
                    );
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                const nodes = result.map((item) => {
                    return {
                        ...item,
                        value: item.name,
                        label: item.name,
                        leaf: this.type === 'city'
                    };
                });
                resolve(nodes);
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error(error);
                this.$tools.message.err('系统异常');
                this.$set(node, 'loading', false);
            }
        },
        // 获取区域数据
        async getAreaByCityName(node, resolve) {
            const cityName = node.value;
            try {
                const res =
                    await this.$service.warehouse.region.getAreaByCityName(
                        cityName
                    );
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                const nodes = result.map((item) => {
                    return {
                        ...item,
                        value: item.name,
                        label: item.name,
                        leaf: this.type === 'area'
                    };
                });
                resolve(nodes);
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error(error);
                this.$tools.message.err('系统异常');
                this.$set(node, 'loading', false);
            }
        }
    }
};
</script>
