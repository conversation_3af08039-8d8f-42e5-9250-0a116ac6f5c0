<template>
    <div>
        <snbc-card title="物流寻源结果" v-if="isSoureceShow" class="first-card">
            <template #card-body>
                <logistics-source-form
                    :isDetails="true"
                    :form="logisticsSourceForm"
                    ref="logisticsSourceFormRef"
                />
            </template>
        </snbc-card>
        <snbc-card title="实际物流信息" v-if="isActualShow" class="second-card">
            <template #card-body>
                <actual-logistics-info-form
                    :isDetails="isDetails"
                    :form="actualLogisticsForm"
                    ref="actualLogisticsFormRef"
                    :mileage="mileage"
                />
            </template>
        </snbc-card>
        <div class="no-info" v-if="!isSoureceShow && !isActualShow">
            暂无数据
        </div>
    </div>
</template>
<script>
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import ActualLogisticsInfoForm from './ActualLogisticsInfoForm.vue';
import LogisticsSourceForm from './LogisticsSourceForm.vue';

const form = {
    transportMode: '',
    logisticsName: '',
    logisticsTel: '',
    logisticsPerson: '',
    mileage: '',
    carType: '',
    trunkFee: '',
    unitPrice: '',
    deliveryFee: '',
    packFee: '',
    totalFee: '',
    feeDesc: ''
};

export default {
    name: 'AllocationLogisticsInfo',
    components: {
        SnbcCard,
        ActualLogisticsInfoForm,
        LogisticsSourceForm
    },
    props: {
        isDetails: {
            type: Boolean,
            default() {
                return false;
            }
        },
        allocateTaskCode: {
            type: String,
            default() {
                return '';
            }
        },
        mileage: {
            type: Number,
            default() {
                return 0;
            }
        }
    },
    data() {
        return {
            logisticsSourceForm: {
                ...form
            },
            actualLogisticsForm: {
                ...form,
                settlementParty: '',
                settlementPrice: ''
            },
            isSoureceShow: true,
            isActualShow: true
        };
    },
    watch: {
        allocateTaskCode: {
            handler: 'init',
            immediate: true
        }
    },
    methods: {
        async init() {
            if (!this.allocateTaskCode) return;
            const { result } =
                await this.$service.warehouse.allocation.queryAllLogisticsInfo(
                    this.allocateTaskCode
                );
            this.isActualShow = !this.isDetails || !!result.logisticsResultVO;
            this.isSoureceShow = !this.isDetails || !!result.logisticsSourceVO;
            const { logisticsSourceVO, logisticsResultVO } = result;
            for (const [key, value] of Object.entries(
                logisticsSourceVO || {}
            )) {
                if (
                    typeof value === 'number' &&
                    !['mileage', 'cubicNumber'].includes(key)
                ) {
                    logisticsSourceVO[key] = value / 100;
                }
            }
            for (const [key, value] of Object.entries(
                logisticsResultVO || {}
            )) {
                if (
                    typeof value === 'number' &&
                    !['mileage', 'cubicNumber'].includes(key)
                ) {
                    logisticsResultVO[key] = value / 100;
                }
            }
            this.logisticsSourceForm = logisticsSourceVO || {};
            this.actualLogisticsForm =
                logisticsResultVO || logisticsSourceVO || {};
        },
        validateForm() {
            return this.$refs.actualLogisticsFormRef.validateForm();
        },
        resetForm() {
            this.$refs.actualLogisticsFormRef.resetForm();
            this.$refs.logisticsSourceFormRef.resetForm();
        },
        getForm() {
            return this.$refs.actualLogisticsFormRef.getForm();
        }
    }
};
</script>
<style lang="scss" scoped>
.no-info {
    line-height: 32px;
    text-align: center;
}
</style>
