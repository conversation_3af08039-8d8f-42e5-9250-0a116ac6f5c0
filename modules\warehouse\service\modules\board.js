/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        board_home_page: {
            /**
             * 查询看板首页指标
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getHomeStatistics(data = {}) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board_home_page/select_metric',
                    method: 'post',
                    data
                });
            },
            /**
             * 区仓首页看板Top数据（包含四类指标）
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getTopData(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board_home_page/select_metric_top_data',
                    method: 'post',
                    data
                });
            },
            /**
             * 区仓首页看板Top平均库龄数据
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getAverageInventoryAge(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board_home_page/select_top_average_inventory_age',
                    method: 'post',
                    data
                });
            },
            /**
             * 区仓首页看板Top货账相符率率数据
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getInventoryAccuracy(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board_home_page/select_top_inventory_accuracy',
                    method: 'post',
                    data
                });
            },
            /**
             * 区仓首页看板Top出入库及时率
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getOnTimeDelivery(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board_home_page/select_top_on_time_delivery',
                    method: 'post',
                    data
                });
            },
            /**
             * 区仓首页看板Top面积利用率数据
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getSpaceUtilization(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board_home_page/select_top_space_utilization',
                    method: 'post',
                    data
                });
            },
            /**
             * 区仓首页看板业务合规率图表
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getComplianceRateChart(data = {}) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board_home_page/select_compliance_rate_chart',
                    method: 'post',
                    data
                });
            },
            /**
             * 区仓首页看板区仓支出和收入图表
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getFinancialChart(data = {}) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board_home_page/select_financial_chart',
                    method: 'post',
                    data
                });
            },
            /**
             * 区仓首页看板区仓数量和流量图表
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getFlowChart(data = {}) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board_home_page/select_flow_chart',
                    method: 'post',
                    data
                });
            },
            /**
             * 区仓首页看板货账相符率图表
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getInventoryAccuracyChart(data = {}) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board_home_page/select_inventory_accuracy_chart',
                    method: 'post',
                    data
                });
            },
            /**
             * 区仓首页看板面积利用率图表
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getSpaceUtilizationChart(data = {}) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board_home_page/select_space_utilization_chart',
                    method: 'post',
                    data
                });
            }
        },
        board: {
            /**
             * 查询每日统计数据
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getDailyStatistics(data) {
                data.startDate = data.requestDate[0];
                data.endDate = data.requestDate[1];
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board/select_daily_statistics',
                    method: 'post',
                    data
                });
            },
            /**
             * 导出每日统计数据
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            exportDailyStatistics(data) {
                data.startDate = data.requestDate[0];
                data.endDate = data.requestDate[1];
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board/export_daily_list',
                    method: 'post',
                    responseType: 'blob',
                    data
                });
            },
            /**
             * 查询每日总计数据
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getDailySummary(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board/select_daily_summary',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
