{"_args": [["commander@2.14.1", "C:\\Users\\<USER>\\Desktop\\si-warehouse-page"]], "_from": "commander@2.14.1", "_id": "commander@2.14.1", "_inBundle": false, "_integrity": "sha512-+YR16o3rK53SmWHU3rEM3tPAh2rwb1yPcQX5irVn7mb0gXbwuCCrnkbV5+PBfETdfg1vui07nM6PCG1zndcjQw==", "_location": "/wtf-core-vue/codepage/commander", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "commander@2.14.1", "name": "commander", "escapedName": "commander", "rawSpec": "2.14.1", "saveSpec": null, "fetchSpec": "2.14.1"}, "_requiredBy": ["/wtf-core-vue/codepage"], "_resolved": "http://maven.xtjc.net/repository/npm-all/commander/-/commander-2.14.1.tgz", "_spec": "2.14.1", "_where": "C:\\Users\\<USER>\\Desktop\\si-warehouse-page", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "dependencies": {}, "description": "the complete solution for node.js command-line programs", "devDependencies": {"@types/node": "^7.0.52", "eslint": "^3.19.0", "should": "^11.2.1", "sinon": "^2.4.1", "standard": "^10.0.3", "typescript": "^2.7.1"}, "files": ["index.js", "typings/index.d.ts"], "homepage": "https://github.com/tj/commander.js#readme", "keywords": ["commander", "command", "option", "parser"], "license": "MIT", "main": "index", "name": "commander", "repository": {"type": "git", "url": "git+https://github.com/tj/commander.js.git"}, "scripts": {"lint": "eslint index.js", "test": "make test && npm run test-typings", "test-typings": "node_modules/typescript/bin/tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "version": "2.14.1"}