/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    // 区仓模块下拉框数据
    const service = {
        warehouseSelectData: {
            /**
             * 查询产品下拉框数据
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            getAllProduct(data = {}) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/basedata/product_info/search_all_product',
                    method: 'post',
                    data
                });
            },
            /**
             * 查询客户下拉框数据
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            getAllCustomer(data = {}) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/basedata/customer_info/search_all_customer',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
