/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        warehouseFeeRules: {
            /**
             * 新增
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            add(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/warehouse/fee_rules/save/0',
                    method: 'post',
                    data
                });
            },
            /**
             * 编辑
             * @param {Object} data 参数对象 
             * @returns {Promise} http请求
             */
            edit(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/warehouse/fee_rules/save/1',
                    method: 'post',
                    data
                });
            },
            /**
             * 列表查询
             * @param {Object} data 参数对象 
             * @returns {Promise} http请求
             */
            list(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/warehouse/fee_rules/query_fee_rules_list',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
