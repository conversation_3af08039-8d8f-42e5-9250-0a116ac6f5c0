// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    min-width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}
// dialog 弹框的样式
.el-dialog.custom-dialog {
    width: 36%;
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    .el-dialog__header {
        display: flex;
        align-items: center;
        padding: 18px 20px;
        height: 50px;
        background: #f5f6fa;
        .el-dialog__title {
            font-size: 14px;
            font-weight: bold;
            color: #000000;
            &::before {
                content: "";
                display: inline-block;
                width: 5px;
                height: 15px;
                background: #09c;
                margin-right: 10px;
                position: relative;
                top: 2px;
            }
        }
        .el-dialog__close {
            font-size: 15px;
            color: #000000;
            font-weight: bold;
        }
    }
    .el-dialog__body {
        box-sizing: border-box;
        padding: 20px;
        .el-input .el-input__inner {
            height: 40px;
            line-height: 40px;
            border: 1px solid #ececec;
            &:active,
            &:hover {
                border: 1px solid #09c;
            }
        }
        .el-form-item.is-error {
            .el-input__inner,
            .el-textarea__inner {
                border: 1px solid #ff0000;
            }
        }
        .el-input.is-disabled .el-input__inner {
            border: 1px solid #ececec;
            &:active,
            &:hover {
                border: 1px solid #ececec;
            }
        }
        .el-form-item__label {
            padding: 0 17px 0 0;
            font-weight: 400;
        }
        .el-switch__core {
            width: 42px;
        }
        .el-form-item:last-child {
            margin-bottom: 0;
        }
        .el-row .el-form-item:last-child {
            margin-bottom: 22px;
        }
        //  dialog 下的穿梭框
        .transfer-component {
            height: calc(100% - 90px);
            .row-bg {
                .transfer-box {
                    width: calc(50% - 5px);
                    box-sizing: border-box;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                    padding: 20px;
                    &:last-child {
                        margin-left: 10px;
                    }
                    // 标题
                    .title {
                        font-size: 14px;
                        font-weight: bold;
                        color: #000000;
                        margin-bottom: 20px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        .el-icon-error {
                            font-size: 16px;
                            color: #8290a3;
                            cursor: pointer;
                        }
                    }
                    // 右侧标题有border
                    .title-border {
                        box-sizing: border-box;
                        padding-bottom: 20px;
                        border-bottom: 1px solid #e7e7e7;
                    }
                    .el-tree {
                        font-size: 14px;
                        .el-tree-node {
                            .el-tree-node__content {
                                height: 36px;
                                color: #676c6f !important;
                                &:hover {
                                    color: #09c;
                                }
                                .is-checked + span {
                                    color: #09c !important;
                                }
                            }
                        }
                    }
                    .selected-box {
                        .selected-item {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            height: 36px;
                        }
                        .el-icon-error {
                            font-size: 16px;
                            color: #c7c7c7;
                            cursor: pointer;
                        }
                    }
                    .el-checkbox-group {
                        .el-checkbox {
                            display: block;
                            height: 36px;
                            color: #676c6f !important;
                            &:hover {
                                color: #09c;
                            }
                            .is-checked + span {
                                color: #09c !important;
                            }
                        }
                    }

                    .left-box {
                        margin-top: 20px;
                        height: 256px;
                        overflow: auto;
                    }
                    .right-box {
                        height: 300px;
                        overflow: auto;
                    }
                }
                .user-row {
                    display: flex;
                    justify-content: space-between;
                }
                // 类似角色管理中添加用户弹框样式
                .user-transfer-box {
                    width: calc(33.33% - 5px);
                    margin-right: 10px;
                    box-sizing: border-box;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                    padding: 20px;
                    &:last-child {
                        margin-right: 0px;
                    }
                    // 标题
                    .title {
                        font-size: 14px;
                        font-weight: bold;
                        color: #000000;
                        margin-bottom: 20px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        .el-icon-error {
                            font-size: 16px;
                            color: #8290a3;
                            cursor: pointer;
                        }
                    }
                    // 右侧标题有border
                    .title-border {
                        box-sizing: border-box;
                        padding-bottom: 20px;
                        border-bottom: 1px solid #e7e7e7;
                    }
                    .el-tree {
                        .el-tree-node {
                            .el-tree-node__content {
                                height: 36px;
                                color: #676c6f !important;
                                &:hover {
                                    color: #09c;
                                }
                                .is-checked + span {
                                    color: #09c !important;
                                }
                            }
                        }
                    }
                    .selected-box {
                        .selected-item {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            height: 36px;
                        }
                        .el-icon-error {
                            font-size: 16px;
                            color: #c7c7c7;
                            cursor: pointer;
                        }
                    }

                    .left-box {
                        margin-top: 20px;
                        height: 300px;
                        overflow: auto;
                    }
                    .center-box {
                        margin-top: 20px;
                        height: 260px;
                        overflow: auto;
                    }
                    .right-box {
                        height: 320px;
                        overflow: auto;
                    }
                }
            }
        }
        // 弹框中的面板样式eg. 角色管理中的添加用户弹框

        .user-item {
            display: flex;
            align-items: center;
            height: 40px;
            border-radius: 8px;
            box-sizing: border-box;
            padding: 5px 10px;
            color: #2f2f2f;
            cursor: pointer;
            font-size: 12px;
            font-weight: 400;
            margin-bottom: 10px;
            .user-item-img {
                margin-right: 11px;
                background: #dce3ec;
            }
            .user-item-text {
                display: flex;
                align-items: center;
                justify-content: space-between;
                height: 30px;
                flex: 1;
                .text-desc {
                    margin-top: 5px;
                }
                .text-jobnumber {
                    margin-right: 11px;
                }
                .text-close {
                    font-size: 15px;
                    color: #8290a3;
                }
            }
            &:hover {
                background: #09c;
                color: #ffffff;
            }
        }
        .user-item-selected {
            background: #f5f6fa;
            &:hover {
                background: #f5f6fa;
                color: #2f2f2f;
            }
        }
        .active {
            background: #09c;
            color: #ffffff;
        }
    }
    .el-dialog__footer {
        margin: 0px;
        margin-top: 0;
        padding-top: 20px;
        box-sizing: border-box;
        border-top: 1px solid #f5f6fa;
        .btn-border {
            width: 100px;
            height: 40px;
            background: #ffffff;
            border: 1px solid #09c;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 400;
            color: #09c;
        }
        .btn-bg {
            width: 100px;
            height: 40px;
            background: #09c;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 400;
            color: #ffffff;
        }
    }
}
.el-dialog.transfer-dialog .el-dialog__footer {
    border-top: 1px solid transparent !important;
    padding-top: 0px;
}
