<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig"> </snbc-base-table>
        </div>
        <SettlementDetail ref="detailDialog"></SettlementDetail>
        <snbc-base-form-dialog ref="dialogRef" :config="dialogConfig" @submit="handleSubmit" />
        <PaymentLogAddDialog ref="addDialog" @add="handleSubmit"></PaymentLogAddDialog>
        <EasyPrint ref="reconciliationPrint">
            <template>
                <ReconciliationContent v-for="item in selections" :key="item.id" :content="item" />
            </template>
        </EasyPrint>
        <EasyPrint ref="confirmPrint">
            <template>
                <ConfirmContent v-for="item in selections" :key="item.id" :content="item"></ConfirmContent>
            </template>
        </EasyPrint>
    </div>
</template>
<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import formItems from 'warehouse/common/form-items/index.js';
import SettlementDetail from './detail.vue';
import SnbcBaseFormDialog from 'warehouse/components/snbc-dialog/SnbcBaseFormDialog.vue';
import PaymentLogAddDialog from './components/PaymentLogAddDialog.vue';
import EasyPrint from './components/EasyPrint.vue';
import ReconciliationContent from './components/ReconciliationContent';
import ConfirmContent from './components/ConfirmContent.vue';
import { inputRequired, maxLength, numberRequired, fileRequired } from 'warehouse/common/form-rules/index.js';
import { uploadValidate } from '@/tools';

const { warehouseMultiSelect, dateRange, select, file, textarea, number } = formItems;

const elUploadAttrs = {
    limit: 5,
    accept: '.png,.jpg'
};

const confirmItems = [
    {
        ...textarea,
        modelKey: 'confirmationResult',
        name: '确认说明'
    },
    {
        ...file,
        modelKey: 'confirmationDocumentResult',
        name: '费用凭证',
        tip: '只能上传最多5个jpg/png文件，且单个文件不超过10M',
        dataType: 'string',
        elUploadAttrs,
        ...uploadValidate(10, ['png', 'jpg'])
    }
];

const rules = {
    confirmationResult: [inputRequired('确认说明'), maxLength(255)],
    settlementCost: [inputRequired('结算费用'), numberRequired('结算费用')],
    settlementDescription: [inputRequired('结算说明'), maxLength(255)],
    settlementArea: [inputRequired('结算面积'), numberRequired('结算面积')],
    confirmationDocumentResult: [fileRequired('请上传费用凭证')]
};

const commonItems = [
    {
        ...number,
        modelKey: 'settlementCost',
        name: '结算费用(元)',
        elInputNumberAttrs: {
            min: 0,
            controls: false,
            precision: 2,
            max: Math.pow(10, 7) - 1
        }
    },
    {
        ...textarea,
        modelKey: 'settlementDescription',
        name: '结算说明'
    },
    {
        ...file,
        modelKey: 'reconciliationResult',
        name: '对账凭证',
        tip: '只能上传最多5个jpg/png文件，且单个文件不超过10M',
        dataType: 'string',
        elUploadAttrs,
        ...uploadValidate(10, ['png', 'jpg'])
    }
];
const areaItems = [
    {
        ...number,
        modelKey: 'settlementArea',
        name: '结算面积(平)',
        elInputNumberAttrs: {
            min: 0,
            controls: false,
            precision: 2,
            max: Math.pow(10, 5) - 1
        }
    },
    ...commonItems
];
const uploadItems = [
    {
        ...file,
        modelKey: 'reconciliationResult',
        tip: '只能上传最多5个jpg/png文件，且单个文件不超过10M',
        name: '对账凭证',
        dataType: 'string',
        elUploadAttrs,
        ...uploadValidate(10, ['png', 'jpg'])
    }
];

const trafiicItems = [...commonItems];

export default {
    name: 'Settlement',
    components: {
        SnbcBaseTable,
        SettlementDetail,
        SnbcBaseFormDialog,
        PaymentLogAddDialog,
        EasyPrint,
        ReconciliationContent,
        ConfirmContent
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        const { detail, selectByPage, confirmFee, checkUpload, addPayment, completeCheck, recreate } =
            this.$service.warehouse.settlement;
        return {
            detail,
            selectByPage,
            confirmFee,
            checkUpload,
            addPayment,
            completeCheck,
            recreate,
            tableConfig: {
                queryParams: {
                    warehouseCodes: [],
                    dateRange: [],
                    settlementState: ''
                },
                queryConfig: {
                    items: [
                        { ...warehouseMultiSelect, modelKey: 'warehouseCodes' },
                        {
                            ...dateRange,
                            modelKey: 'dateRange',
                            name: '结算期',
                            elDatePickerAttrs: {
                                'value-format': 'yyyy-MM',
                                'type': 'monthrange'
                            }
                        },
                        {
                            ...select,
                            modelKey: 'settlementState',
                            name: '结算状态',
                            elOptions: (() =>
                                ['待对账', '已对账', '已确认', '支付中', '已完成', '异常'].map((item) => ({
                                    label: item,
                                    value: item
                                })))()
                        }
                    ],
                    elFormAttrs: {
                        'label-width': '120px'
                    }
                },
                queryApi: selectByPage,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '结算单号',
                        prop: 'billCode',
                        show: true,
                        minWidth: 180,
                        renderMode: 'button',
                        elButtonAttrs: {
                            type: 'text'
                        },
                        handleClick: this.handleClickCell
                    },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '结算期',
                        prop: 'settlementPeriod',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '结算状态',
                        prop: 'settlementState',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '服务站区仓负责人',
                        prop: 'warehouseMaster',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '结算方式',
                        prop: 'settlementMethod',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '实际发生费用(元)',
                        prop: 'feeActual',
                        show: true,
                        minWidth: 180
                    },
                    {
                        label: '结算费用(元)',
                        prop: 'feeSettlement',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '供应商',
                        prop: 'supplier',
                        show: true,
                        minWidth: 120
                    }
                ],
                headerButtons: [
                    {
                        name: '对账数据导出',
                        type: 'primary',
                        handleClick: this.exportReconciliation
                    },
                    {
                        name: '费用确认数据导出',
                        type: 'primary',
                        handleClick: this.exportComfirm
                    }
                ],
                operations: [
                    {
                        name: '对账完成',
                        type: 'primary',
                        handleClick: this.handleReconciliation,
                        handleShow: (item) => item.settlementState === '待对账'
                    },
                    {
                        name: '费用确认完成',
                        type: 'primary',
                        handleClick: this.handleComfirm,
                        handleShow: (item) => item.settlementState === '已对账'
                    },
                    {
                        name: '支付',
                        type: 'primary',
                        handleClick: this.handlePay,
                        handleShow: (item) => ['已确认', '支付中'].includes(item.settlementState)
                    },
                    {
                        name: '对账凭证上传',
                        type: 'primary',
                        handleClick: this.upload,
                        handleShow: (item) => !['待对账', '异常'].includes(item.settlementState)
                    },
                    {
                        name: '重新生成结算单',
                        type: 'primary',
                        handleClick: this.recreateSettlement,
                        handleShow: (item) => item.settlementState === '异常'
                    }
                ],
                handleSelectionChange: this.handleSelectionChange,
                selectionAble: true,
                operationColumnWidth: 240
            },
            // 弹窗配置
            dialogConfig: {
                rules: { ...rules },
                items: []
            },
            mode: '',
            selections: []
        };
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.handleQuery();
    },
    methods: {
        async handleClickCell(item) {
            const instance = await this.getDetail(item.billCode);
            if (!instance) return;
            this.$refs.detailDialog.showDialog(instance);
        },
        // 对账数据导出
        exportReconciliation() {
            if (!this.selections.length) {
                return this.$tools.message.err('请选择结算单');
            }

            this.$refs.reconciliationPrint.print();
        },
        // 费用确认导出
        exportComfirm() {
            if (!this.selections.length) {
                return this.$tools.message.err('请选择结算单');
            }

            this.$refs.confirmPrint.print();
        },
        // 对账完成处理
        handleReconciliation(item) {
            this.mode = 'reconciliation';
            const data = {
                billCode: item.billCode,
                settlementCost: 0,
                settlementPeriod: item.settlementPeriod,
                warehouseCode: item.warehouseCode
            };
            if (item.settlementMethod === '面积') {
                data.settlementArea = 0;
            }
            this.dialogConfig.items = item.settlementMethod === '面积' ? areaItems : trafiicItems;
            this.dialogConfig.rules = rules;
            this.$refs.dialogRef.baseAddDialog(data, '对账完成');
        },
        // 费用确认完成
        handleComfirm(item) {
            this.mode = 'comfirm';
            const data = {
                billCode: item.billCode
            };
            this.dialogConfig.items = confirmItems;
            this.dialogConfig.rules = rules;
            this.$refs.dialogRef.baseAddDialog(data, '费用确认完成');
        },
        // 支付
        async handlePay(item) {
            this.mode = 'pay';
            const result = await this.getDetail(item.billCode);
            this.$refs.addDialog.showDialog({
                paymentList: result.paymentList.map((ele, index) => {
                    ele.index = index + 1;
                    return ele;
                }),
                billCode: item.billCode,
                settlementPeriod: item.settlementPeriod,
                feeSettlement: item.feeSettlement
            });
        },

        // 对账凭证上传
        async upload(item) {
            this.mode = 'upload';
            const { detail } = await this.getDetail(item.billCode);
            const data = { reconciliationResult: detail.reconciliationResult, billCode: item.billCode };
            this.dialogConfig.items = uploadItems;
            this.dialogConfig.rules = {
                reconciliationResult: [fileRequired('请上传对账凭证')]
            };
            this.$refs.dialogRef.baseAddDialog(data, '对账凭证上传');
        },
        async recreateSettlement(item) {
            const params = {
                billCode: item.billCode,
                settlementPeriod: item.settlementPeriod,
                warehouseCode: item.warehouseCode
            };
            const { code, message } = await this.recreate(params);
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
                return;
            }
            this.$tools.message.suc(`重新生成结算单成功`);
            this.$refs.tableRef.queryList();
        },
        async handleSubmit({ form }) {
            const match = {
                // 对账完成
                reconciliation: ['completeCheck', '对账完成'],
                // 费用确认完成
                comfirm: ['confirmFee', '费用确认完成'],
                // 支付
                pay: ['addPayment', '支付'],
                // 对账上传
                upload: ['checkUpload', '对账上传']
            };
            const { code, message } = await this[match[this.mode][0]](form);
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
                return;
            }
            this.$tools.message.suc(`${match[this.mode][1]}成功`);
            this.$refs.dialogRef.hideDialog();
            this.$refs.tableRef.queryList();
        },
        // 多选回调
        handleSelectionChange(selections) {
            this.selections = selections;
        },
        // 获取对账单详情
        async getDetail(billCode) {
            const { code, message, result } = await this.detail({ billCode });
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
                return;
            }
            return result;
        }
    }
};
</script>
<style lang="scss" scoped></style>
