// 方法
import DynamicComponent from './dynamic-component';
import service from './request';
import eventBus from './event-bus';
import moduleLoader from './module-loader';
import { setModulesRouter } from '../router/index.js';
import { loadModuleStore } from '../store/index.js';
import { loadModuleMessage, getLanguage } from '../lang/index.js';
import Tools from '../utils';
import storage from './storage.js'; // 导入存储方法
import websocket from './websocket.js';
import { loadModuleConstant } from './module-constant.js';

export default (context) => {
    context.Vue.prototype.$addRoutes = setModulesRouter;
    context.Vue.prototype.$addStore = loadModuleStore;
    context.Vue.prototype.$addI18n = loadModuleMessage;
    context.Vue.prototype.$getLanguage = getLanguage;
    context.Vue.prototype.$tools = new Tools();
    context.Vue.prototype.$eventBus = eventBus;
    context.Vue.prototype.$websocket = websocket;
    context.Vue.prototype.$http = service;
    context.Vue.prototype.$service = {}; // 注册$service
    context.Vue.prototype.$storage = storage; // 导入存储方法
    context.Vue.prototype.$dynamicComponent = new DynamicComponent(context.store);
    context.Vue.prototype.$moduleLoader = moduleLoader(context);
    context.Vue.prototype.$addConstant = loadModuleConstant;
};
