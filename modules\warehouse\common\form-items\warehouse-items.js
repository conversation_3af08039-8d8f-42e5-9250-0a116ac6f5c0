import commonItems from './common-items.js';

const warehouseCode = {
    ...commonItems.input,
    name: '区仓编号',
    modelKey: 'warehouseCode'
};
const warehouseName = {
    ...commonItems.input,
    name: '区仓名称',
    modelKey: 'warehouseName'
};
const repositoryType = {
    ...commonItems.select,
    name: '仓库类型',
    modelKey: 'repositoryType',
    elOptions: [
        { label: '封闭仓库', value: '封闭仓库' },
        { label: '非封闭仓库', value: '非封闭仓库' }
    ]
};
const structureType = {
    ...commonItems.select,
    name: '结构类型',
    modelKey: 'structureType',
    elOptions: [
        { label: '彩钢棚式', value: '彩钢棚式' },
        { label: '混凝土式', value: '混凝土式' },
        { label: '高标仓', value: '高标仓' },
        { label: '工厂仓', value: '工厂仓' }
    ]
};
const buildingSharingFactor = {
    ...commonItems.number,
    name: '建筑公摊系数',
    modelKey: 'buildingSharingFactor',
    elInputNumberAttrs: {
        precision: 2,
        min: 0,
        max: Math.pow(10, 6) - 0.01
    }
};
const firefightingSharingFactor = {
    ...commonItems.number,
    name: '消防公摊系数',
    modelKey: 'firefightingSharingFactor',
    elInputNumberAttrs: {
        precision: 2,
        min: 0,
        max: Math.pow(10, 6) - 0.01
    }
};
const serviceName = {
    component: 'SnbcFormProviderSelect',
    name: '服务商',
    modelKey: 'serviceName'
};
const serviceContact = {
    ...commonItems.input,
    name: '服务商联系人',
    modelKey: 'serviceContact'
};
const serviceContactTel = {
    ...commonItems.input,
    name: '服务商联系电话',
    modelKey: 'serviceContactTel'
};
const productName = {
    ...commonItems.input,
    name: '产品名称',
    modelKey: 'productName'
};
const customerName = {
    ...commonItems.input,
    name: '客户名称',
    modelKey: 'customerName'
};
const assertDate = {
    ...commonItems.date,
    name: '生效日期',
    modelKey: 'assertDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};
const totalArea = {
    ...commonItems.number,
    name: '总面积',
    modelKey: 'totalArea',
    elInputNumberAttrs: {
        precision: 2,
        min: 0,
        max: Math.pow(10, 6) - 0.01
    }
};
const detailAddress = {
    ...commonItems.input,
    name: '详细地址',
    modelKey: 'detailAddress'
};
const onRoadMaxDay = {
    ...commonItems.number,
    name: '在途最大天数',
    modelKey: 'onRoadMaxDay',
    elInputNumberAttrs: {
        precision: 0,
        min: 0
    }
};
const warehouseDescription = {
    ...commonItems.textarea,
    name: '区仓描述',
    modelKey: 'warehouseDescription'
};
const warehouseState = {
    ...commonItems.select,
    name: '区仓状态',
    modelKey: 'warehouseState',
    elOptions: [
        { label: '启用', value: '启用' },
        { label: '停用', value: '停用' },
        { label: '草稿', value: '草稿' }
    ]
};
const ruleState = {
    ...commonItems.select,
    name: '规则状态',
    modelKey: 'ruleState',
    elOptions: [
        { label: '启用', value: '启用' },
        { label: '停用', value: '停用' }
    ]
};
const warehouseType = {
    ...commonItems.select,
    name: '区仓类型',
    modelKey: 'warehouseType',
    elOptions: [
        { label: '保内', value: '保内' },
        { label: '保外', value: '保外' }
    ]
};
// 区仓下拉选择-单选
const warehouseSelect = {
    name: '区仓名称',
    component: 'SnbcFormWarehouseSelect',
    modelKey: 'warehouseCode'
};
// 区仓下拉选择-多选
const warehouseMultiSelect = {
    name: '区仓名称',
    component: 'SnbcFormWarehouseSelect',
    modelKey: 'warehouseCodeList',
    elSelectAttrs: {
        multiple: true
    }
};

// 区仓资产出入规则类型
const ruleType = {
    ...commonItems.select,
    name: '规则类型',
    modelKey: 'ruleType',
    elOptions: [
        { label: '先入先出', value: '先入先出' },
        { label: '设备准入', value: '设备准入' }
    ]
};

// 区仓宽恕天数
const forgiveDay = {
    ...commonItems.number,
    name: '宽恕天数',
    modelKey: 'forgiveDay',
    elInputNumberAttrs: {
        min: 0
    }
};
// 产品下拉选择-单选
const productSelect = {
    name: '产品名称',
    component: 'SnbcFormProductSelect',
    modelKey: 'productId'
};
// 产品下拉选择-多选
const productMultiSelect = {
    name: '产品名称',
    component: 'SnbcFormProductSelect',
    modelKey: 'productIdList',
    elSelectAttrs: {
        multiple: true
    }
};
// 区仓上限
const upperLimit = {
    ...commonItems.number,
    name: '区仓上限',
    modelKey: 'upperLimit',
    elInputNumberAttrs: {
        min: 0,
        precision: 0,
        max: 1000
    }
};
// 区仓下限
const lowerLimit = {
    ...commonItems.number,
    name: '区仓下限',
    modelKey: 'lowerLimit',
    elInputNumberAttrs: {
        min: 0,
        precision: 0,
        max: 1000
    }
};
// 客户名称选择-单选
const customerSelect = {
    name: '客户名称',
    component: 'SnbcFormCustomerSelect',
    modelKey: 'customerCode'
};
// 区仓排序指标
const orderMetric = {
    ...commonItems.select,
    name: '排序指标',
    modelKey: 'orderMetric',
    elOptions: [
        { label: '累计流量', value: 'traffic' },
        { label: '出入库及时率', value: 'timelyRate' },
        { label: '面积利用率', value: 'areaUsageRate' },
        { label: '平均库龄', value: 'stockAgeAverage' },
        { label: '新机平均库龄', value: 'stockAgeAverageNew' },
        { label: '撤机平均库龄', value: 'stockAgeAverageWithDraw' }
    ],
    defaultValue: 'traffic'
};
// 区仓排序指标
const orderMetricDaily = {
    ...commonItems.select,
    name: '排序指标',
    modelKey: 'orderMetric',
    elOptions: [
        { label: '在库资产总数', value: 'totalStock' },
        { label: '新机在库数量', value: 'newStock' },
        { label: '撤机在库数量', value: 'removedStock' },
        { label: '入库数量', value: 'inboundQuantity' },
        { label: '出库数量', value: 'outboundQuantity' },
        { label: '使用面积', value: 'assetOccupyArea' }
    ],
    defaultValue: 'totalStock'
};
// 排序规则
const orderRule = {
    ...commonItems.select,
    name: '排序规则',
    modelKey: 'orderRule',
    elOptions: [
        { label: '降序', value: 'desc' },
        { label: '升序', value: 'asc' }
    ],
    defaultValue: 'desc'
};
export default {
    warehouseCode,
    customerName,
    warehouseName,
    productName,
    warehouseState,
    assertDate,
    ruleState,
    warehouseType,
    totalArea,
    detailAddress,
    onRoadMaxDay,
    warehouseSelect,
    warehouseMultiSelect,
    warehouseDescription,
    ruleType,
    forgiveDay,
    productSelect,
    productMultiSelect,
    upperLimit,
    lowerLimit,
    customerSelect,
    repositoryType,
    structureType,
    buildingSharingFactor,
    firefightingSharingFactor,
    serviceContact,
    serviceContactTel,
    serviceName,
    orderMetric,
    orderMetricDaily,
    orderRule
};
