/// 设置`position`
/// @access public
/// <AUTHOR>
/// @example
/// //SCSS
/// 
/// .absolute{
///   @include position(absolute,top 10px right 20px);
/// }
/// .absolute{
///   @include position(absolute,top 0 right 0 bottom 0 left 0);
/// }
///	
/// //Output CSS
/// 
/// .absolute {
///   top: 10px;
///   right: 20px;
///   position: absolute; 
/// }
/// 
/// .absolute {
///   top: 0;
///   right: 0;
///   bottom: 0;
///   left: 0;
///   position: absolute; 
/// }
/// @link http://sassmeister.com/gist/a70eb473b806c4e2a57d
/// @link 
/// @param {string} $position - 可选值: `static` | `relative` | `absolute` | `fixed`
/// @param {string} $args - 可设置值: `top` | `right` | `bottom` | `left`

@mixin position($position,$args){
	@each $o in top right bottom left {
		$i: index($args, $o);
		@if $i and $i + 1 <= length($args) and type-of(nth($args, $i + 1)) == number {
			#{$o}: nth($args, $i + 1);
		}
	}
	position: $position;
}