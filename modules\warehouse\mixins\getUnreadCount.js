// 此js用于更新未读消息数
export default {
    computed: {
        unreadCount() {
            return this.$store.state.systemManagement.unreadCount;
        }
    },
    methods: {
        // 获取未读消息数
        getUnreadCount() {
            this.$service.systemManagement.getUnReadCount({}).then((res) => {
                if (res.head.code === '000000') {
                    this.$store.commit('systemManagement/updateUnreadCount', res.body);
                } else {
                    const msg = `productService.bgReturnError[${res.head.code}]`;
                    this.$message({
                        message: this.$t(msg),
                        type: 'error'
                    });
                }
            });
        }
    }
};
