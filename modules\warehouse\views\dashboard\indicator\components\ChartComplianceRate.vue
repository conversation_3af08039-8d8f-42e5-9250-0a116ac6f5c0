<template>
    <!-- 出入库及时率 -->
    <div ref="chartRef" class="chart-box" />
</template>
<script>
import moment from 'moment';
import echarts from 'echarts';
import resize from 'wtf-core-vue/src/components/Charts/mixins/resize';

export default {
    name: 'ChartFeeAndIncome',
    mixins: [resize],
    data() {
        return {
            // 图表实例
            chart: null
        };
    },
    mounted() {
        this.chart = echarts.init(this.$refs.chartRef);
    },
    beforeDestroy() {
        if (!this.chart) {
            return;
        }
        this.chart.dispose();
        this.chart = null;
    },
    methods: {
        // 清除图表
        clearChart() {
            this.chart.clear();
        },
        handleData(data) {
            return data.map((item) => {
                const date = item.statisticsDate || item.time;
                item.date = moment(date).format('YY年MM月');
                item.value = item.complianceRate || item.assembleKey;
                return item;
            });
        },
        // eslint-disable-next-line max-lines-per-function
        drawChart(result) {
            const data = this.handleData(result);
            this.clearChart();
            this.chart.setOption({
                color: ['#26BAD5'],
                title: {
                    left: 'center',
                    top: '10px',
                    text: '出入库及时率'
                },
                tooltip: {
                    trigger: 'axis',
                    formatter(params) {
                        let relVal = params[0].name;
                        for (let i = 0, l = params.length; i < l; i++) {
                            relVal += `<br/>${params[i].marker}${params[i].seriesName}：${params[i].value}%`;
                        }
                        return relVal;
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: 20,
                    top: 80,
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: data.map((item) => item.date),
                    axisTick: {
                        alignWithLabel: true
                    },
                    axisLabel: {
                        interval: 0,
                        // X轴三个字一换行
                        formatter: (value, idx) => {
                            return this.$tools.formatAxisLabel(value);
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '比率(%)',
                    splitLine: {
                        lineStyle: {
                            type: 'dashed',
                            color: '#303437'
                        }
                    }
                },
                series: [
                    {
                        type: 'line',
                        data: data.map((item) => {
                            return {
                                name: item.date,
                                value: item.value
                            };
                        }),
                        itemStyle: {
                            normal: {
                                label: {
                                    show: true,
                                    position: 'top',
                                    color: '#000000',
                                    formatter: (item) => {
                                        return `${item.value}%`;
                                    }
                                }
                            }
                        },
                        name: '出入库及时率'
                    }
                ]
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.chart-box {
    width: calc(50% - 5px);
    height: 300px;
    border: 1px solid #dee5e7;
    background: #e6ebf5;
}
</style>
