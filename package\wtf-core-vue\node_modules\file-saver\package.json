{"_args": [["file-saver@2.0.1", "C:\\Users\\<USER>\\Desktop\\si-warehouse-page"]], "_from": "file-saver@2.0.1", "_id": "file-saver@2.0.1", "_inBundle": false, "_integrity": "sha512-dCB3K7/BvAcUmtmh1DzFdv0eXSVJ9IAFt1mw3XZfAexodNRoE29l3xB2EX4wH2q8m/UTzwzEPq/ArYk98kUkBQ==", "_location": "/wtf-core-vue/file-saver", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "file-saver@2.0.1", "name": "file-saver", "escapedName": "file-saver", "rawSpec": "2.0.1", "saveSpec": null, "fetchSpec": "2.0.1"}, "_requiredBy": ["/wtf-core-vue"], "_resolved": "http://maven.xtjc.net/repository/npm-all/file-saver/-/file-saver-2.0.1.tgz", "_spec": "2.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\si-warehouse-page", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/eligrey/FileSaver.js/issues"}, "description": "An HTML5 saveAs() FileSaver implementation", "devDependencies": {"@babel/cli": "^7.1.0", "@babel/core": "^7.1.0", "@babel/plugin-transform-modules-umd": "^7.1.0", "babel-preset-minify": "^0.4.3"}, "files": ["dist/FileSaver.js", "dist/FileSaver.min.js", "dist/FileSaver.min.js.map", "src/FileSaver.js"], "homepage": "https://github.com/eligrey/FileSaver.js#readme", "keywords": ["filesaver", "saveas", "blob"], "license": "MIT", "main": "dist/FileSaver.min.js", "name": "file-saver", "repository": {"type": "git", "url": "git+https://github.com/eligrey/FileSaver.js.git"}, "scripts": {"build": "npm run build:development && npm run build:production", "build:development": "babel -o dist/FileSaver.js --plugins @babel/plugin-transform-modules-umd src/FileSaver.js", "build:production": "babel -o dist/FileSaver.min.js -s --plugins @babel/plugin-transform-modules-umd --presets minify src/FileSaver.js", "prepublishOnly": "npm run build", "test": "echo \"Error: no test specified\" && exit 0"}, "version": "2.0.1"}