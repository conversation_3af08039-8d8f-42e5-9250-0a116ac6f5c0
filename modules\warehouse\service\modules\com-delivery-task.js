/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        comDeliveryTask: {
            /**
             * 按条件获取公司发货任务信息
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/com_delivery_task/get_list',
                    method: 'post',
                    data
                });
            },
            /**
             * 根据id获取公司发货任务详情
             *
             * @param {String} id 公司发货任务ID
             * @returns {Promise} http
             */
            getInfoById(id) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/com_delivery_task/get_info/${id}`,
                    method: 'get'
                });
            },
            /**
             * 删除公司发货任务
             *
             * @param {String} id 公司发货任务ID
             * @returns {Promise} http
             */
            deleteTaskById(id) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/com_delivery_task/delete/${id}`,
                    method: 'get'
                });
            },
            /**
             * 推送公司发货任务
             *
             * @param {String} id 公司发货任务ID
             * @returns {Promise} http
             */
            sendTask(id) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/com_delivery_task/send/${id}`,
                    method: 'get'
                });
            },
            /**
             * 修改公司发货任务
             *
             * @param {Object} data 公司发货任务
             * @returns {Promise} http
             */
            updateTask(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/com_delivery_task/update`,
                    method: 'post',
                    data
                });
            },
            /**
             * 公司发货清单-查询发货清单
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getComDeliveryBill(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/delivery_bill/warehouse/get_com_delivery_bill',
                    method: 'post',
                    data
                });
            },
            /**
             * 公司发货清单-查询发货清单详情
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getComDeliveryBillDetail(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/delivery_bill/warehouse/get_com_delivery_bill_detail',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
