<template>
    <el-card class="snbc-card padding-top-5">
        <div slot="header" class="snbc-card-header">
            <span>区仓指标</span>
            <div>
                <el-button class="padding-right-0" type="text" @click="handleCheckRules"
                    >查看指标计算规则&gt;&gt;</el-button
                >
                <el-button class="padding-right-0" type="text" @click="handleMore"> 更多&gt;&gt; </el-button>
            </div>
        </div>
        <el-row :gutter="10">
            <el-col :span="8" v-for="(group, groupIndex) in indicatorGroup" :key="groupIndex">
                <el-card class="margin-top-10 group-card" shadow="hover">
                    <el-row :gutter="10">
                        <el-col :span="6" class="flex-name">
                            <svg-icon class-name="card-icon" :icon-class="group.icon" />
                            <span class="group-name">
                                {{ group.name }}
                            </span>
                        </el-col>
                        <el-col :span="18" class="flex-content">
                            <div v-for="(item, index) in group.list" :key="index" class="group-item">
                                <span class="group-item-name" :style="{ width: group.nameWidth }">
                                    {{ item.name }}
                                </span>
                                <i
                                    v-if="item.tip"
                                    :title="item.tip"
                                    class="el-icon-question snbc-text-primary question-tip"
                                    style="margin-left: 5px"
                                ></i>
                                ：
                                <span v-if="!item.type">
                                    {{ item.value }}
                                </span>
                                <span v-if="item.type === 'multi'" class="group-item-info">
                                    <span v-for="(valueItem, index) in item.value" :key="item.valueTip[index] + index">
                                        <span :title="item.valueTip[index]">{{ valueItem }}</span>
                                        <span class="separate-data" v-if="index < item.value.length - 1">|</span>
                                    </span>
                                </span>
                                <el-link v-if="item.type === 'link'" type="primary" @click="handleLink(item)">
                                    {{ item.value }}
                                </el-link>
                            </div>
                        </el-col>
                    </el-row>
                </el-card>
            </el-col>
        </el-row>
    </el-card>
</template>
<script>
import moment from 'moment';
import textRender from 'warehouse/common/text-render/index.js';

const { unitRender } = textRender;
export default {
    name: 'IndicatorCard',
    components: {},
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 查询参数统计周期
            queryParams: {
                startDate: '',
                endDate: ''
            },
            // 指标数据
            countList: [
                {
                    name: '区仓总数',
                    value: '-',
                    type: 'link',
                    tip: '当前启用状态的区仓数量'
                },
                {
                    name: '区仓面积',
                    value: '-',
                    tip: '当前启用状态的区仓面积之和'
                },
                {
                    name: '资产总数',
                    value: '-',
                    type: 'link',
                    tip: '当前在库资产总数'
                },
                {
                    name: '支出',
                    value: '-',
                    type: 'multi',
                    contentClassName: 'snbc-text-danger',
                    tip: '本年度所有区仓的结算费用合计 | 上月度所有区仓结算费用合计',
                    valueTip: ['本年度所有区仓的结算费用合计', '上月度所有区仓结算费用合计']
                },
                {
                    name: '已支付',
                    value: '-',
                    contentClassName: 'snbc-text-danger',
                    tip: '本年度所有区仓已支付费用合计'
                },
                {
                    name: '收入',
                    value: '-',
                    type: 'multi',
                    contentClassName: 'snbc-text-danger',
                    tip: '本年度所有区仓的收入合计 | 上月度所有区仓收入合计',
                    valueTip: ['本年度所有区仓的收入合计', '上月度所有区仓收入合计']
                },
                {
                    name: '货账相符率',
                    value: '-',
                    type: 'multi',
                    tip: '本年度所有区仓货账相符率平均值 | 上月度所有区仓货账相符率平均值',
                    valueTip: ['本年度货账相符率', '上月度货账相符率']
                },
                {
                    name: '面积利用率',
                    value: '-',
                    type: 'multi',
                    tip: '本年度所有面积计费的区仓面积利用率平均值| 上月度所有面积计费的区仓的面积利用率平均值',
                    valueTip: ['本年度面积利用率', '上月度面积利用率']
                },
                {
                    name: '出入库及时率',
                    value: '-',
                    type: 'multi',
                    tip: '本年度所有吞吐量大于零的区仓的出入库及时率平均值 | 上月度所有吞吐量大于零的区仓的出入库及时率平均值',
                    valueTip: ['本年度出入库及时率', '上月度出入库及时率']
                },
                {
                    name: '货损丢失',
                    value: '-',
                    type: 'link',
                    tip: '本年度所有的货损丢失任务数量'
                },
                {
                    name: '平均库龄',
                    value: '-',
                    type: 'multi',
                    tip: '本年度资产的平均库龄 | 本年度新机平均库龄 | 本年度撤机平均库龄',
                    valueTip: ['本年度所有资产平均库龄', '本年度新机平均库龄', '本年度撤机平均库龄']
                },
                {
                    name: '累计吞吐量',
                    value: '-',
                    tip: '本年度所有区仓的吞吐量合计（2023年仅计算7月1日之后的吞吐量）'
                },
                {
                    name: '累计流量',
                    value: '-',
                    tip: '本年度所有区仓的累计流量合计（2023年仅计算7月1日之后的累计流量）'
                }
            ]
        };
    },
    computed: {
        // 区仓分组
        indicatorGroup() {
            return [
                {
                    name: '信息汇总',
                    icon: 'qucangxinxi',
                    nameWidth: '5em',
                    list: this.countList.filter((item) =>
                        ['区仓总数', '区仓面积', '支出', '已支付', '收入'].includes(item.name)
                    )
                },
                {
                    name: '资产汇总',
                    icon: 'qucangzichan',
                    nameWidth: '5em',
                    list: this.countList.filter((item) =>
                        ['资产总数', '货损丢失', '累计吞吐量', '平均库龄', '累计流量'].includes(item.name)
                    )
                },
                {
                    name: '指标汇总',
                    icon: 'qucangzhibiao',
                    nameWidth: '6em',
                    list: this.countList.filter((item) =>
                        ['货账相符率', '面积利用率', '出入库及时率'].includes(item.name)
                    )
                }
            ];
        }
    },
    methods: {
        // 数据查询
        async queryData(params) {
            // 避免重复请求
            if (params.startDate === this.queryParams.startDate && params.endDate === this.queryParams.endDate) {
                return;
            }
            this.queryParams = {
                startDate: params.startDate,
                endDate: params.endDate
            };
            this.$tools.setIndicator(this.countList, {
                区仓总数: unitRender('', ''),
                区仓面积: unitRender('', '㎡'),
                资产总数: unitRender('', '台'),
                支出: [unitRender('', '元'), unitRender('', '元')],
                已支付: unitRender('', '元'),
                收入: [unitRender('', '元'), unitRender('', '元')],
                货账相符率: [unitRender('', '%'), unitRender('', '%')],
                面积利用率: [unitRender('', '%'), unitRender('', '%')],
                出入库及时率: [unitRender('', '%'), unitRender('', '%')],
                货损丢失: unitRender('', '台'),
                平均库龄: [unitRender('', '天'), unitRender('', '天'), unitRender('', '天')],
                累计吞吐量: unitRender('', '台次'),
                累计流量: unitRender('', '台')
            });
            try {
                const { getHomeStatistics } = this.$service.warehouse.board_home_page;
                const res = await getHomeStatistics(this.queryParams);
                const { code, message, result = {} } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                const {
                    areaCount,
                    areaSize,
                    assetCount,
                    averageInventoryAge,
                    newAssetAgeAverage,
                    withDrawAssetAgeAverage,
                    cost,
                    cumulativeThroughput,
                    accumulateTraffic,
                    damageLoss,
                    income,
                    inventoryAccuracy,
                    preMonthInventoryAccuracy,
                    onTimeDelivery,
                    preMonthTimelyRate,
                    paidCost,
                    spaceUtilization,
                    preMonthAreaUsageRate,
                    monthlyCost,
                    monthlyIncome
                } = result;
                // 设置具体值
                this.$tools.setIndicator(this.countList, {
                    区仓总数: unitRender(areaCount, ''),
                    区仓面积: unitRender(areaSize, '㎡'),
                    资产总数: unitRender(assetCount, '台'),
                    支出: [unitRender(cost, '元'), unitRender(monthlyCost, '元')],
                    已支付: unitRender(paidCost, '元'),
                    收入: [unitRender(income, '元'), unitRender(monthlyIncome, '元')],
                    货账相符率: [unitRender(inventoryAccuracy, '%'), unitRender(preMonthInventoryAccuracy, '%')],
                    面积利用率: [unitRender(spaceUtilization, '%'), unitRender(preMonthAreaUsageRate, '%')],
                    出入库及时率: [unitRender(onTimeDelivery, '%'), unitRender(preMonthTimelyRate, '%')],
                    货损丢失: unitRender(damageLoss, '台'),
                    平均库龄: [
                        unitRender(averageInventoryAge, '天'),
                        unitRender(newAssetAgeAverage, '天'),
                        unitRender(withDrawAssetAgeAverage, '天')
                    ],
                    累计吞吐量: unitRender(cumulativeThroughput, '台次'),
                    累计流量: unitRender(accumulateTraffic, '台')
                });
                this.$emit('getIndicators', result);
            } catch (error) {
                this.$tools.message.err('系统异常');
            }
        },
        // 指标项点击跳转
        handleLink(item) {
            if (item.name === '区仓总数') {
                this.$router.push({
                    path: '/app/warehouse/base-information',
                    query: {
                        warehouseState: '启用'
                    }
                });
            }
            if (item.name === '资产总数') {
                this.$router.push({
                    path: '/app/assets/assets-management'
                });
            }
            if (item.name === '货损丢失') {
                this.$router.push({
                    path: '/app/error-deal/damage-task'
                });
            }
        },
        // 更多操作跳转
        handleMore() {
            const { startDate, endDate } = this.queryParams;
            this.$router.push({
                path: '/app/dashboard/indicator-query',
                query: {
                    requestDate: [moment(startDate).format('yyyy-MM'), moment(endDate).format('yyyy-MM')]
                }
            });
        },
        // 查看指标计算规则
        handleCheckRules() {
            this.$emit('checkRules');
        }
    }
};
</script>
<style lang="scss" scoped>
@import '~warehouse/styles/indicator-card.scss';
.separate-data {
    margin: 0 0.5rem;
}
.group-item {
    display: inline-flex;
    .group-item-name {
        flex-shrink: 0;
    }
}
</style>
