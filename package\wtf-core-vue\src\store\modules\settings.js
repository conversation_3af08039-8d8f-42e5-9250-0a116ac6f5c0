import defaultSettings from '../../settings';
import variables from '../../styles/element-variables.scss';
import { initKey } from '../../utils/crypto.js';

const { desKeyIV, desKey, rsaPubKey, title, permissionIconKey, permissionOrderKey, permissionKey, permissionSettings, showSettings, tagsView, fixedHeader, fixedFooter,sidebarLogo, supportPinyinSearch, timeout, token, navbar,interfaceWhiteList,timeoutList,isWebI18n,functionMenu } = defaultSettings;

const state = {
    theme: variables.theme,
    title,
    permissionSettings,
    permissionKey,
    permissionOrderKey,
    permissionIconKey,
    showSettings,
    tagsView,
    fixedHeader,
	fixedFooter,
    sidebarLogo,
    supportPinyinSearch,
    timeout,
    token,
    desKeyIV,
    desKey,
    rsaPubKey,
    navbar,
	interfaceWhiteList,
	timeoutList,
	isWebI18n,
	functionMenu
};

const mutations = {
    CHANGE_SETTING: (state, data) => {
        // eslint-disable-next-line no-prototype-builtins
        Object.keys(data).forEach(objectKey => {
            if (Object.prototype.hasOwnProperty.call(state, objectKey)) {
                state[objectKey] = data[objectKey];
            }
        });
        initKey(state.rsaPubKey, state.desKey, state.desKeyIV);
    }
};

const actions = {
    changeSetting({ commit }, data) {
        commit('CHANGE_SETTING', data);
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
