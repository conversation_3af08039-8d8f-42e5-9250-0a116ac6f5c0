import Vue from 'vue';
import Vuex from 'vuex';
import getters from './getters';

Vue.use(Vuex);

// https://webpack.js.org/guides/dependency-management/#requirecontext
const modulesFiles = require.context('./modules', true, /\.js$/);

// you do not need `import app from './modules/app'`
// it will auto require all vuex module from modules file
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
    // set './app.js' => 'app'
    const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1');
    const value = modulesFiles(modulePath);
    modules[moduleName] = value.default;
    return modules;
}, {});

const store = new Vuex.Store({
    modules,
    getters
});

export function loadModuleStore(moduleName, moduleStore) {
    if (!moduleName) {
        console.error(
            `%c加载store的模块名称不能为空，请配置package.json中的moduleName字段的值`,
            'background: #4192d9; padding: 5px; color: #fff; border-radius: 5px'
        );
        return;
    }
    if (!moduleStore) {
        console.error(
            `%c加载store的值不能为空`,
            'background: #4192d9; padding: 5px; color: #fff; border-radius: 5px'
        );
        return;
    }
    // 注册模块 名字按照module文件夹名称命名
    store.registerModule(moduleName, moduleStore);
}

export default store;
