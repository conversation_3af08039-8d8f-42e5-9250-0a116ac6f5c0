<template>
    <el-card class="snbc-card padding-top-5">
        <div slot="header" class="snbc-card-header">
            <span>TOP数据查询</span>
            <el-button class="padding-right-0" type="text" @click="handleMore"> 更多&gt;&gt; </el-button>
        </div>
        <el-form class="query-area" :model="form" label-width="100px">
            <el-form-item label="快捷查询：">
                <el-radio-group v-model="form.dateType" size="medium" @input="handleRadio">
                    <el-radio-button
                        v-for="(item, index) in dateTypeOptions"
                        :key="index"
                        :label="item.dateType"
                    ></el-radio-button>
                </el-radio-group>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="queryData" style="margin-left: 30px"
                    >查询</el-button
                >
            </el-form-item>
            <el-form-item label="统计周期：">
                <el-date-picker
                    v-model="form.requestDate"
                    type="monthrange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    size="small"
                    style="width: 300px"
                    :unlink-panels="true"
                    :disabled="form.dateType !== '自定义'"
                >
                </el-date-picker>
                <span style="float: right; color: #33add6">
                    {{ averageValue }}
                </span>
            </el-form-item>
        </el-form>
        <el-tabs type="border-card" v-model="form.tabName" @tab-click="handleTabClick">
            <el-tab-pane v-for="item in tabPanes" :key="item.name" :label="item.name + 'TOP10'" :name="item.name">
                <snbc-table-list :config="item.config" :list="item.list" @sort-change="handleSortChange" />
            </el-tab-pane>
        </el-tabs>
    </el-card>
</template>
<script>
import moment from 'moment';
import { getDateRange } from 'warehouse/common/picker-options/index.js';
import textRender from 'warehouse/common/text-render/index.js';
import SnbcTableList from 'warehouse/components/snbc-table/SnbcTableList.vue';

// 日期区间选择配置
const dateTypeOptions = [
    {
        dateType: '前一月',
        dateRange: getDateRange('前一月')
    },
    {
        dateType: '前一季度',
        dateRange: getDateRange('前一季度')
    },
    {
        dateType: '上半年',
        dateRange: getDateRange('上半年')
    },
    {
        dateType: '下半年',
        dateRange: getDateRange('下半年')
    },
    {
        dateType: '本年度',
        dateRange: getDateRange('本年度')
    },
    {
        dateType: '上一年度',
        dateRange: getDateRange('上一年度')
    },
    {
        dateType: '自定义',
        dateRange: []
    }
];

// 参数
const form = {
    dateType: '前一月',
    requestDate: getDateRange('前一月'),
    tabName: '出入库及时率'
};

export default {
    name: 'IndicatorTop',
    components: {
        SnbcTableList
    },
    props: {
        // 区仓指标数据
        indicators: {
            type: Object,
            default() {
                return {};
            }
        }
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        // 列表配置
        const tableConfig = {
            elTableColumns: [
                {
                    label: '排名',
                    prop: 'index',
                    show: true,
                    minWidth: 50
                },
                {
                    label: '区仓名称',
                    prop: 'warehouseName',
                    show: true,
                    minWidth: 100
                },
                {
                    label: '建仓时间',
                    prop: 'createTime',
                    show: true,
                    minWidth: 100,
                    render: (value) => {
                        return moment(value).format('YYYY-MM-DD');
                    }
                },
                {
                    label: '供应商',
                    prop: 'supplier',
                    show: true,
                    minWidth: 200
                },
                {
                    label: '服务站区仓负责人',
                    prop: 'responsiblePerson',
                    show: true,
                    minWidth: 100
                },
                {
                    label: '指标数据',
                    prop: 'performanceData',
                    show: true,
                    minWidth: 100,
                    elTableColumnAttrs: {
                        sortable: 'custom'
                    },
                    cellClassName: (column, row) => {
                        return this.$tools.getClassByCompareValue(row.performanceData, this.activeTab.averageValue);
                    },
                    render: (value) => {
                        if (this.activeTabName === '平均库龄') {
                            return textRender.unitRender(value, '天');
                        }
                        return textRender.unitRender(value, '%');
                    }
                },
                {
                    label: '历史趋势',
                    prop: 'historyTrend',
                    show: true,
                    minWidth: 100,
                    renderMode: 'button',
                    elButtonAttrs: {
                        type: 'text'
                    },
                    handleClick: (row) => {
                        this.handleHistoryTrend(row);
                    }
                }
            ]
        };
        const { getOnTimeDelivery, getSpaceUtilization, getInventoryAccuracy, getAverageInventoryAge } =
            this.$service.warehouse.board_home_page;
        return {
            form,
            dateTypeOptions,
            tabPanes: [
                {
                    name: '出入库及时率',
                    api: getOnTimeDelivery,
                    config: {
                        ...tableConfig,
                        sortParams: {
                            sortKey: 'performanceData',
                            sortOrder: 'ascending'
                        },
                        metricName: 'timelyRate'
                    },
                    list: [],
                    averageValue: ''
                },
                {
                    name: '面积利用率',
                    api: getSpaceUtilization,
                    config: {
                        ...tableConfig,
                        sortParams: {
                            sortKey: 'performanceData',
                            sortOrder: 'ascending'
                        },
                        metricName: 'areaUsageRate'
                    },
                    list: [],
                    averageValue: ''
                },
                {
                    name: '货账相符率',
                    api: getInventoryAccuracy,
                    config: {
                        ...tableConfig,
                        sortParams: {
                            sortKey: 'performanceData',
                            sortOrder: 'ascending'
                        },
                        metricName: 'inventoryMatchRate'
                    },
                    list: [],
                    averageValue: ''
                },
                {
                    name: '平均库龄',
                    api: getAverageInventoryAge,
                    config: {
                        ...tableConfig,
                        sortParams: {
                            sortKey: 'performanceData',
                            sortOrder: 'descending'
                        },
                        metricName: 'stockAgeAverage'
                    },
                    list: [],
                    averageValue: ''
                }
            ]
        };
    },
    computed: {
        // 选中的tab
        activeTab() {
            return this.tabPanes.find((tab) => tab.name === this.form.tabName);
        },
        // 选中的tab的名称
        activeTabName() {
            return this.activeTab.name;
        },
        // 平均指标值
        averageValue() {
            const { unitRender } = textRender;
            const name = this.activeTabName;
            const { averageValue } = this.activeTab;
            if (name === '出入库及时率') {
                return `${name}（仅涉及吞吐量大于零的区仓数据）：${unitRender(averageValue, '%')}`;
            } else if (name === '面积利用率') {
                return `${name}（仅涉及面积计费区仓数据）：${unitRender(averageValue, '%')}`;
            } else if (name === '货账相符率') {
                return `${name}：${unitRender(averageValue, '%')}`;
            } else if (name === '平均库龄') {
                return `${name}：${unitRender(averageValue, '天')}`;
            }
            return '';
        },
        // 指标数据列
        performanceDataColumn() {
            return this.activeTab.config.elTableColumns.find((column) => column.prop === 'performanceData');
        }
    },
    methods: {
        // 切换日期选项
        handleRadio(dateType) {
            const target = dateTypeOptions.find((item) => item.dateType === dateType);
            this.$set(this.form, 'requestDate', target.dateRange);
            const [startDate, endDate] = this.form.requestDate;
            if (startDate && endDate) {
                this.queryData();
            }
        },
        // 切换tab页
        handleTabClick() {
            this.queryData();
        },
        // 排序条件发生变化
        handleSortChange(params) {
            const { order } = params;
            this.$set(this.activeTab.config.sortParams, 'sortOrder', order);
            this.queryData();
        },
        // 查询数据
        async queryData() {
            const tab = this.activeTab;
            this.$set(tab, 'list', []);
            this.$set(this.performanceDataColumn, 'label', tab.name);
            const [startDate, endDate] = this.form.requestDate;
            if (!startDate || !endDate) {
                this.$tools.message.warning('请选择统计周期的起止日期');
                return;
            }
            const { sortOrder } = this.activeTab.config.sortParams;
            const params = {
                startDate: moment(startDate).format('yyyy-MM'),
                endDate: moment(endDate).format('yyyy-MM'),
                orderBy: sortOrder ? sortOrder.replace('ending', '') : '',
                metricName: this.activeTab.config.metricName
            };
            // 区仓指标按统计周期进行查询
            this.$emit('query', params);
            try {
                const res = await this.$service.warehouse.board_home_page.getTopData(params);
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                const resultList = result.metricTopDataList || [];
                resultList.map((item, index) => {
                    item.index = index + 1;
                    item.performanceData = Number(item.performanceData);
                    item.historyTrend = '历史趋势';
                    return item;
                });
                this.$set(tab, 'list', resultList);
                this.$set(tab, 'averageValue', result.metricAverageValue);
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 历史趋势
        handleHistoryTrend(row) {
            const [startDate, endDate] = this.form.requestDate;
            this.$router.push({
                path: '/app/dashboard/indicator-history-trend',
                query: {
                    warehouseCode: row.warehouseCode,
                    requestDate: [moment(startDate).format('yyyy-MM'), moment(endDate).format('yyyy-MM')]
                }
            });
        },
        // 更多操作跳转
        handleMore() {
            const [startDate, endDate] = this.form.requestDate;
            this.$router.push({
                path: '/app/dashboard/indicator-query',
                query: {
                    requestDate: [moment(startDate).format('yyyy-MM'), moment(endDate).format('yyyy-MM')]
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .query-area {
    margin-top: 10px;
    .el-form-item {
        margin-bottom: 10px;
    }
}
</style>
