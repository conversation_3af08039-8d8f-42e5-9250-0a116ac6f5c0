<template>
    <div class="view">
        <withdrawal-list v-show="isListShow" @handleDetails="handleDetails"></withdrawal-list>
        <withdrawal-details ref="detailsRef" v-show="!isListShow" @handleBack="setIsListShow"></withdrawal-details>
    </div>
</template>
<script>
import functions from 'frame/mixins/functions.js';
import WithdrawalList from './components/WithdrawalList';
import WithdrawalDetails from './components/WithdrawalDetails';

export default {
    name: 'Withdrawal',
    components: {
        WithdrawalList,
        WithdrawalDetails
    },
    mixins: [functions],
    data() {
        return {
            isListShow: true
        };
    },
    computed: {},
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
    },
    methods: {
        setIsListShow() {
            this.isListShow = !this.isListShow;
        },
        handleDetails(row) {
            this.$refs.detailsRef.init(row);
            this.setIsListShow();
        }
    }
};
</script>
<style lang="scss" scoped></style>
