<template>
    <snbc-base-table ref="tableRef" :table-config="tableConfig">
        <template #tabs>
            <snbc-table-tabs :tabs-config="tabsConfig" />
        </template>
    </snbc-base-table>
</template>

<script>
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcTableTabs from 'warehouse/components/snbc-table/SnbcTableTabs.vue';

export default {
    name: 'ProposalDisappear',
    components: {
        SnbcBaseTable,
        SnbcTableTabs
    },
    props: {
        tabsConfig: {
            type: Object,
            default() {
                return {};
            }
        },
        taskCode: {
            type: String,
            default() {
                return '';
            }
        }
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            tableConfig: {
                queryParams: {
                    taskCode: ''
                },
                queryApi:
                    this.$service.warehouse.inventoryManagement
                        .getInventoryResult,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '物料编码',
                        prop: 'materialCode',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '产品序列号',
                        prop: 'assetCode',
                        show: true,
                        minWidth: 180
                    },
                    {
                        label: '区仓',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '盘点结果',
                        prop: 'invResult',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '资产状态',
                        prop: 'assetState',
                        show: true,
                        minWidth: 140
                    },
                    {
                        label: '修正状态',
                        prop: 'isConfirm',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '关联单号',
                        prop: 'relatedCode',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '处理人',
                        prop: 'dealPerson',
                        show: true,
                        minWidth: 80
                    },
                    {
                        label: '处理时间',
                        prop: 'dealTime',
                        show: true,
                        minWidth: 160
                    }
                ]
            }
        };
    },
    // mounted() {},
    watch: {
        taskCode(newVal) {
            this.tableConfig.queryParams.taskCode = newVal;
            this.handleQuery();
        }
    },
    methods: {
        handleQuery() {
            if (!this.taskCode) return;
            this.$refs.tableRef.queryList();
        }
    }
};
</script>

<style lang="scss" scoped></style>
