import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();
    const service = {
        supplier: {
            /**
             * 按条件获取承运商
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/basedata/supplier_info/search_supplierList_by_condition',
                    method: 'post',
                    data
                });
            },
            /**
             * 按条件获取服务商
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getProviderList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/warehouse/provider/search_provider',
                    method: 'post',
                    data
                });
            }
        }
    };
    return service;
};
