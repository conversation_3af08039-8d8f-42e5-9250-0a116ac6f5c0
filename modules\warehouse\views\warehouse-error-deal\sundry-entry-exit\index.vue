<template>
    <div class="view">
        <div class="content">
            <snbc-base-table
                v-show="tabsConfig.activeName === '待处理'"
                ref="tableRef1"
                :table-config="tableConfig"
            >
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
            </snbc-base-table>
            <snbc-base-table
                v-show="tabsConfig.activeName === '查询'"
                ref="tableRef2"
                :table-config="tableConfig"
            >
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
            </snbc-base-table>
        </div>
        <sundry-dialog ref="sundryDialog" :config="dialogConfig">
            <template v-if="['add', 'edit'].includes(mode)" #dialog-body>
                <BasicInfo
                    ref="basicInfoRef"
                    :mode="mode"
                    :form="form"
                ></BasicInfo></template
            ><template v-else #dialog-body>
                <snbc-tabs :tabs="tabs">
                    <template #basicInfo
                        ><BasicInfo
                            style="padding: 8px"
                            ref="basicInfoRef"
                            :mode="mode"
                            :form="form"
                        ></BasicInfo>
                    </template>
                    <template #logisticsInfo
                        ><execute-log
                            style="margin: 8px"
                            :sundryEntryExitCode="
                                form.sundryApplyCode
                            " /></template></snbc-tabs></template
        ></sundry-dialog>
    </div>
</template>

<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcTableTabs from 'warehouse/components/snbc-table/SnbcTableTabs.vue';
import SundryDialog from './components/SundryDialog.vue';
import BasicInfo from './components/BasicInfo.vue';
import SnbcTabs from 'warehouse/components/snbc-tabs/SnbcTabs.vue';
import ExecuteLog from './executeLog.vue';
import { queryConfigItems } from './formItems';
import { mapState } from 'vuex';

export default {
    name: 'SundryEntryExit',
    components: {
        SnbcBaseTable,
        SnbcTableTabs,
        SundryDialog,
        BasicInfo,
        SnbcTabs,
        ExecuteLog
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        const {
            getList: listApi,
            add: addApi,
            update: editApi,
            getTodoList: todoListApi,
            getDetail,
            examine
        } = this.$service.warehouse.sundryEntryExit;
        return {
            listApi,
            addApi,
            editApi,
            todoListApi,
            getDetail,
            examine,
            tabs: [
                {
                    label: '基础信息',
                    id: 'basicInfo'
                },
                {
                    label: '执行日志',
                    id: 'logisticsInfo'
                }
            ],
            tableConfig: {
                queryParams: {
                    warehouseName: '',
                    warehouseState: '',
                    warehouseType: ''
                },
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: listApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '申请单号',
                        prop: 'sundryApplyCode',
                        show: true,
                        minWidth: 200,
                        renderMode: 'button',
                        elButtonAttrs: {
                            type: 'text'
                        },
                        handleClick: this.handleDetail
                    },
                    {
                        label: '申请名称',
                        prop: 'sundryApplyName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '出入库类型',
                        prop: 'accessType',
                        show: true,
                        minWidth: 140
                    },
                    {
                        label: '申请类型',
                        prop: 'applyType',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '申请来源',
                        prop: 'applySource',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '来源任务',
                        prop: 'associatedTaskCode',
                        show: true,
                        minWidth: 180
                    },
                    {
                        label: '申请人',
                        prop: 'createUserName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '申请原因',
                        prop: 'applyReason',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '状态',
                        prop: 'applyState',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '申请时间',
                        prop: 'createTime',
                        show: true,
                        minWidth: 160
                    },
                    { label: '备注', prop: 'remark', show: true, minWidth: 160 }
                ],
                operations: [
                    {
                        name: '修改',
                        type: 'primary',
                        handleClick: this.handleEdit,
                        handleShow: (item) =>
                            ['待提交', '驳回'].includes(item.applyState)
                    },
                    {
                        name: '审核',
                        type: 'success',
                        handleClick: this.handleAudit,
                        handleShow: (item) => item.applyState === '审核中'
                    }
                ],
                headerButtons: [
                    {
                        name: '新增',
                        type: 'primary',
                        handleClick: this.handleAdd,
                        permissionCode: 'FWZQCZRR'
                    }
                ]
            },
            operations: [
                {
                    name: '修改',
                    type: 'primary',
                    handleClick: this.handleEdit,
                    handleShow: (item) =>
                        ['待提交', '驳回'].includes(item.applyState)
                },
                {
                    name: '审核',
                    type: 'success',
                    handleClick: this.handleAudit,
                    handleShow: (item) => item.applyState === '审核中'
                }
            ],
            // 标签页配置
            tabsConfig: {
                activeName: '待处理',
                tabItems: ['待处理', '查询'],
                handleTabClick: this.handleTabClick
            },
            // 弹窗配置
            dialogConfig: {
                elDialogAttrs: {
                    title: '资产出入库新增'
                },
                operations: []
            },
            dialogAddOptions: [
                {
                    name: '保存',
                    type: 'primary',
                    handleClick: this.handleSave
                }
            ],
            dialogEditOptions: [
                {
                    name: '保存',
                    type: 'warning',
                    handleClick: this.handleSave
                },
                {
                    name: '提交',
                    type: 'primary',
                    handleClick: this.handleSubmit
                }
            ],
            dialogAuditOptions: [
                {
                    name: '驳回',
                    type: 'danger',
                    handleClick: this.handleRefuse
                },
                {
                    name: '通过',
                    type: 'primary',
                    handleClick: this.handlePass
                }
            ],
            // add edit audit detail
            mode: 'add',
            form: {}
        };
    },
    computed: {
        ...mapState(['permission'])
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef1.handleQuery();
    },
    methods: {
        // 新增操作
        handleAdd() {
            this.mode = 'add';
            this.dialogConfig.elDialogAttrs.title = '资产杂项出入库新增';
            this.dialogConfig.operations = this.dialogAddOptions;
            this.$refs.sundryDialog.openDialog();
        },
        async getDetailOpen(item) {
            const { code, result, message } = await this.getDetail(
                item.sundryApplyCode
            );
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
                return;
            }
            this.$refs.sundryDialog.openDialog();
            this.$nextTick(() => {
                this.form = result;
            });
        },
        // 编辑操作
        async handleEdit(item) {
            this.mode = 'edit';
            this.dialogConfig.elDialogAttrs.title = '资产杂项出入库编辑';
            this.dialogConfig.operations = this.dialogEditOptions;
            this.getDetailOpen(item);
        },
        // 审核操作
        async handleAudit(item) {
            this.mode = 'audit';
            this.dialogConfig.elDialogAttrs.title = '资产杂项出入库审核';
            this.dialogConfig.operations = this.dialogAuditOptions;
            this.getDetailOpen(item);
        },
        // 查看详情
        handleDetail(row) {
            this.mode = 'detail';
            this.dialogConfig.elDialogAttrs.title = '资产杂项出入库详情';
            this.dialogConfig.operations = [];
            this.getDetailOpen(row);
        },
        // 标签切换
        handleTabClick(tab) {
            if (tab.name === '查询') {
                this.$refs.tableRef2.tableConfig.queryApi = this.todoListApi;
                this.$refs.tableRef2.handleQuery();
                this.tableConfig.operations = [];
            } else {
                this.$refs.tableRef1.tableConfig.queryApi = this.listApi;
                this.$refs.tableRef1.handleQuery();
                this.tableConfig.operations = this.operations;
            }
        },
        // 杂项出入库保存
        async handleSave() {
            try {
                await this.$refs.basicInfoRef.getFormValidate();
                await this.$tools.confirm('确认保存？');
                const params = { ...this.$refs.basicInfoRef.getForm() };
                let submitApi = this.addApi;
                if (this.mode === 'edit') {
                    submitApi = this.editApi;
                }
                const { code, message } = await submitApi(params);
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.$refs.sundryDialog.hideDialog();
                this.$refs.tableRef1.handleQuery();
                this.$refs.tableRef2.handleQuery();
            } catch (e) {
                return e.message;
            }
        },
        // 杂项出入库提交
        async handleSubmit() {
            try {
                await this.$refs.basicInfoRef.getFormValidate();
                await this.$tools.confirm('确认提交？');
                const params = {
                    ...this.$refs.basicInfoRef.getForm(),
                    applyState: '审核中'
                };
                const { code, message } = await this.editApi(params);
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.$refs.sundryDialog.hideDialog();
                this.$refs.tableRef1.handleQuery();
                this.$refs.tableRef2.handleQuery();
            } catch (e) {
                return e.message;
            }
        },
        handleDialogClose() {
            this.$refs.basicInfoRef.resetForm();
        },
        async handleRefuse() {
            try {
                const sundryCode =
                    this.$refs.basicInfoRef.getForm().sundryApplyCode;
                const content = await this.$tools.prompt(
                    '请填写驳回原因',
                    '杂项出入库驳回',
                    {
                        inputValidator: (msg) => {
                            return !!(msg && msg.length <= 255);
                        },
                        inputErrorMessage: '驳回原因为必填,且最多为255字符',
                        inputType: 'textarea'
                    }
                );
                this.handleExamine(sundryCode, content, '驳回');
            } catch (e) {
                return e.message;
            }
        },
        async handlePass() {
            const sundryCode =
                this.$refs.basicInfoRef.getForm().sundryApplyCode;
            const content = await this.$tools.prompt(
                '请填写通过原因',
                '杂项出入库通过',
                {
                    inputValidator: (msg) => {
                        return !!(msg && msg.length <= 255);
                    },
                    inputErrorMessage: '驳回原因为必填,且最多为255字符',
                    inputType: 'textarea'
                }
            );
            this.handleExamine(sundryCode, content, '通过');
        },
        async handleExamine(sundryCode, content, operateState) {
            const { code, message } = await this.examine({
                sundryCode,
                content,
                operateState,
                nodeIndex: this.form.nodeIndex
            });
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
                return;
            }
            this.$tools.message.suc(
                operateState === '驳回' ? '审核驳回成功' : '审核通过成功'
            );
            this.$refs.sundryDialog.hideDialog();
            this.$refs.tableRef1.handleQuery();
            this.$refs.tableRef2.handleQuery();
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__content {
    padding: 0 0 0 0;
}
</style>
