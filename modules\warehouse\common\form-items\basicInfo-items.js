import commonItems from './common-items.js';

const warehouseCode = {
    ...commonItems.select,
    name: '区仓名称',
    modelKey: 'warehouseCode'
};
const roleCode = {
    ...commonItems.select,
    name: '角色',
    modelKey: 'roleCode'
};
const phone = {
    ...commonItems.input,
    name: '电话号',
    modelKey: 'phone'
};
const newPassword = {
    ...commonItems.input,
    name: '密码',
    modelKey: 'newPassword'
};
const newPasswordAgain = {
    ...commonItems.input,
    name: '确认密码',
    modelKey: 'newPasswordAgain',
    elInputAttrs: {
        placeholder: '请再次输入密码确认'
    }
};
const loginAccount = {
    ...commonItems.input,
    name: '登陆账号',
    modelKey: 'loginAccount'
};
const userName = {
    ...commonItems.input,
    name: '人员名称',
    modelKey: 'userName'
};
const warehouseCodeList = {
    ...commonItems.select,
    name: '区仓名称',
    modelKey: 'warehouseCodeList',
    selectAllAble: true,
    elSelectAttrs: {
        multiple: true
    }
};

const supplierName = {
    ...commonItems.input,
    name: '供应商名称',
    modelKey: 'supplierName'
};
const supplierCode = {
    ...commonItems.input,
    name: '供应商编码',
    modelKey: 'supplierCode',
    elInputAttrs: {
        placeholder: 'QCGYS20230524+4位流水号'
    }
};
const supplierContactsName = {
    ...commonItems.input,
    name: '供应商联系人姓名',
    modelKey: 'supplierContactsName'
};
const supplierContactsPhone = {
    ...commonItems.input,
    name: '供应商联系人电话',
    modelKey: 'supplierContactsPhone'
};
const isLogistics = {
    ...commonItems.select,
    name: '是否为物流供应商',
    modelKey: 'isLogistics',
    elOptions: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
    ]
};
const isInstaller = {
    ...commonItems.select,
    name: '是否是安装供应商',
    modelKey: 'isInstaller',
    elOptions: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
    ]
};
const isWarehouse = {
    ...commonItems.select,
    name: '是否是区仓供应商',
    modelKey: 'isWarehouse',
    elOptions: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
    ]
};

export default {
    warehouseCodeList,
    warehouseCode,
    userName,
    roleCode,
    phone,
    loginAccount,
    newPassword,
    newPasswordAgain,
    supplierName,
    supplierContactsName,
    isWarehouse,
    isInstaller,
    isLogistics,
    supplierContactsPhone,
    supplierCode
};
