/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        sundryEntryExit: {
            /**
             * 新增
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            add(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/sundryentryexit/exceptiontask/add',
                    method: 'post',
                    data
                });
            },
            /**
             * 编辑
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            update(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/sundryentryexit/exceptiontask/edit',
                    method: 'post',
                    data
                });
            },
            /**
             * 删除
             * @param {String} id id
             * @returns {Promise} http请求
             */
            delete(id) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/sundryentryexit/exceptiontask/remove`,
                    method: 'get',
                    params: {
                        id
                    }
                });
            },
            /**
             * 查看详情
             * @param {String} sundryEntryExitCode 杂项出入库申请code
             * @returns {Promise} http请求
             */
            getDetail(sundryEntryExitCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/sundryentryexit/exceptiontask/info`,
                    method: 'get',
                    params: {
                        sundryEntryExitCode
                    }
                });
            },
            /**
             * 审核杂项出入库
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            examine(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/sundryentryexit/exceptiontask/examine`,
                    method: 'post',
                    data
                });
            },
            /**
             * 查询页签列表查询
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            getList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/sundryentryexit/exceptiontask/list',
                    method: 'post',
                    data
                });
            },
            /**
             * 待处理页签列表查询
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            getTodoList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/sundryentryexit/exceptiontask/list_search',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
