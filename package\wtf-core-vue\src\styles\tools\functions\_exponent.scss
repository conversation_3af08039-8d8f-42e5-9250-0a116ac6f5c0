/// Exponent
/// @param {Number} $base
/// @param {Number} $exponent
/// <AUTHOR>
/// @link https://github.com/at-import/Sassy-math/blob/master/sass/math.scss
@function exponent($base, $exponent) {
  // reset value
  $value: $base;
  // positive intergers get multiplied
  @if $exponent > 1 {
    @for $i from 2 through $exponent {
      $value: $value * $base; } }
  // negitive intergers get divided. A number divided by itself is 1
  @if $exponent < 1 {
    @for $i from 0 through -$exponent {
      $value: $value / $base; } }
  // return the last value written
  @return $value; 
}
