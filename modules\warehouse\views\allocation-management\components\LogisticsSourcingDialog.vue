<template>
    <el-dialog
        class="custom-dialog"
        :visible="visible"
        title="物流寻源"
        width="900px"
        @close="handleCancel"
    >
        <LogisticsSourceForm
            ref="logisticsSourceFormRef"
            :form="form"
            :mileage="mileage"
        />
        <template slot="footer" class="dialog-footer">
            <el-button type="danger" @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleSave">保 存</el-button>
        </template>
    </el-dialog>
</template>
<script>
import LogisticsSourceForm from 'warehouse/views/allocation-management/components/LogisticsSourceForm';

export default {
    name: 'LogisticsSourceDialog',
    components: {
        LogisticsSourceForm
    },
    data() {
        return {
            visible: false,
            form: {
                allocateTaskCode: '',
                mileage: 0
            },
            mileage: 0
        };
    },
    methods: {
        showDialog(allocateTaskCode, mileage, form) {
            this.visible = true;
            this.mileage = mileage;
            this.form.allocateTaskCode = allocateTaskCode;
            if (form) {
                this.form = form;
            }
        },
        async handleSave() {
            try {
                await this.$refs.logisticsSourceFormRef.getFormValidate();
                const form = {
                    ...this.$refs.logisticsSourceFormRef.getForm()
                };
                for (const [key, value] of Object.entries(form)) {
                    if (
                        typeof value === 'number' &&
                        !['mileage', 'cubicNumber'].includes(key)
                    ) {
                        form[key] = value * 100;
                    }
                }
                const { code, message } =
                    await this.$service.warehouse.allocation.inputLogisticsSourceResult(
                        form
                    );
                if (code !== '000000') {
                    this.$tools.message.err(
                        message || '寻源结果录入保存失败，请稍后重试'
                    );
                    return;
                }
                this.$tools.message.suc('寻源结果录入保存成功');
                this.handleCancel();
                this.$parent.handleQuery();
            } catch (e) {
                return e.message;
            }
        },
        async handleCancel() {
            this.$refs.logisticsSourceFormRef.resetForm();
            Object.keys(this.form).forEach((key) => {
                this.form[key] = '';
            });
            this.$nextTick(() => {
                this.visible = false;
            });
        }
    }
};
</script>
