<template>
    <div class="view">
        <div class="content">
            <!-- <snbc-base-table ref="tableRef" :table-config="tableConfig" /> -->
            <Table1
                ref="tableRef1"
                v-show="tabsConfig.activeName === '未处理'"
                :tabs-config="tabsConfig"
                :query-config="queryConfig"
            />
            <Table2
                ref="tableRef2"
                v-show="tabsConfig.activeName === '已任务补录'"
                :tabs-config="tabsConfig"
                :query-config="queryConfig"
            />
            <Table3
                ref="tableRef3"
                v-show="tabsConfig.activeName === '已杂项出入库'"
                :tabs-config="tabsConfig"
                :query-config="queryConfig"
            />
            <Table4
                ref="tableRef4"
                v-show="tabsConfig.activeName === '无源合并'"
                :tabs-config="tabsConfig"
                :query-config="queryConfig"
            />
            <Table7
                ref="tableRef7"
                v-show="tabsConfig.activeName === '已报货损'"
                :tabs-config="tabsConfig"
                :query-config="queryConfig"
            />
            <snbc-base-form-dialog ref="dialogRef" :config="dialogConfig" @submit="handleSubmit" />
        </div>
    </div>
</template>

<script>
import functions from 'frame/mixins/functions.js';
import Table1 from './components/UndealTable.vue';
import Table2 from './components/AlreadyTaskDeal.vue';
import Table3 from './components/AlreadyInOut.vue';
import Table4 from './components/NoSource.vue';
import Table7 from './components/CargoDamage.vue';
import FormItems from 'warehouse/common/form-items/inventory-items';
import FormRules from 'warehouse/common/form-rules';
import SnbcBaseFormDialog from 'warehouse/components/snbc-dialog/SnbcBaseFormDialog.vue';
import CommonFormItems from 'warehouse/common/form-items/index.js';

const { input, select } = CommonFormItems;

// 查询参数

const { remark } = FormItems;
const dialogConfigItems = [remark];
const { inputRequired } = FormRules;
const rules = {
    remark: [inputRequired('修正备注')]
};

const productName = {
    ...input,
    name: '产品名称',
    modelKey: 'productName'
};

const assetCode = {
    ...input,
    name: '产品序列号',
    modelKey: 'assetCode'
};

const invResultList = {
    ...select,
    name: '盘点结果',
    modelKey: 'invResultList',
    elOptions: [
        { label: '盘盈', value: 'profit' },
        { label: '盘亏', value: 'loss' },
        { label: '正常', value: 'normal' }
    ]
};

const assetState = {
    ...select,
    name: '资产状态',
    modelKey: 'assetState',
    elOptions: [
        { label: '正常', value: 'normal' },
        { label: '货损', value: 'damage' }
    ]
};
export default {
    name: 'WarehouseInventoryResult',
    components: {
        Table1,
        Table2,
        Table3,
        Table4,
        Table7,
        SnbcBaseFormDialog
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        const { getExceptionData: listApi } = this.$service.warehouse.inventoryManagement;
        return {
            listApi,

            config: {
                elDescriptionsAttrs: {
                    column: 3
                }
            },
            dialogConfig: {
                rules,
                items: dialogConfigItems
            },
            queryConfig: {
                queryParams: {
                    planCode: '',
                    productName: '',
                    invResultList: '',
                    assetCode: '',
                    assetState: ''
                },
                queryConfig: {
                    items: []
                },
                queryParamsHook: (params) => {
                    Object.keys(params).forEach((key) => {
                        !params[key] && delete params[key];
                    });
                    if (params.invResultList) {
                        params.invResultList = [params.invResultList];
                    }
                    if (params.assetState) {
                        params.assetState = [params.assetState];
                    }
                }
            },
            // 标签页配置
            tabsConfig: {
                activeName: '未处理',
                tabItems: ['未处理', '已任务补录', '已杂项出入库', '无源合并', '已报货损'],
                // tabDesc: {
                //     未处理: '未处理',
                //     已任务补录: '已任务补录',
                //     已杂项出入库: '已杂项出入库',
                //     无源合并: '无源合并',
                //     已报货损: '已报货损'
                // },
                handleTabClick: this.handleTabClick
            }
        };
    },
    computed: {},
    watch: {
        'tabsConfig.activeName': {
            handler(val) {
                if (val === '未处理') {
                    this.queryConfig.queryConfig.items = [productName, assetCode, invResultList, assetState];
                    return;
                }
                if (['已任务补录', '已杂项出入库'].includes(val)) {
                    this.queryConfig.queryConfig.items = [productName, assetCode, invResultList];
                    this.queryConfig.queryParams.assetState = '';
                    return;
                }
                if (['无源合并', '已报货损'].includes(val)) {
                    this.queryConfig.queryConfig.items = [productName, assetCode];
                    this.queryConfig.queryParams.assetState = '';
                    this.queryConfig.queryParams.invResultList = '';
                }
            },
            immediate: true
        }
    },
    created() {
        this.queryConfig.queryParams.planCode = this.$route.query.planCode;
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();

        this.$refs.tableRef1.handleQuery();
    },
    methods: {
        // 标签切换
        handleTabClick(tab) {
            if (tab.name === '未处理') {
                this.$refs.tableRef1.handleQuery();
            }
            if (tab.name === '已任务补录') {
                this.$refs.tableRef2.handleQuery();
            }
            if (tab.name === '已杂项出入库') {
                this.$refs.tableRef3.handleQuery();
            }
            if (tab.name === '无源合并') {
                this.$refs.tableRef4.handleQuery();
            }
            if (tab.name === '已报货损') {
                this.$refs.tableRef7.handleQuery();
            }
        },
        // 填写备注信息
        handleConfirm(row) {
            this.$refs.dialogRef.baseAddDialog(row, '修正备注');
        },
        async handleSubmit({ form }) {
            await this.$tools.confirm('确认提交？');
            const params = this.$tools.deepClone(form);
            try {
                const res = await this.$service.warehouse.inventoryManagement.addRemarkForResult(params);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.$refs.dialogRef.hideDialog();
                await this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        }
    }
};
</script>

<style lang="scss" scoped></style>
