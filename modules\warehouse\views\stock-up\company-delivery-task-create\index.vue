<template>
    <div class="view">
        <div class="content">
            <!-- 备货基本信息 -->
            <snbc-card title="备货基本信息">
                <template #card-body>
                    <snbc-descriptions :items="baseInfo" />
                </template>
            </snbc-card>
            <!-- 调拨任务 -->
            <allocation-task
                ref="allocationTaskRefs"
                v-for="(item, index) in taskInfo.stockRequestInfoTransferList"
                :key="'allocationTask' + index"
                :list="item"
            />
            <!-- 公司发货任务 -->
            <company-delivery-task
                ref="companyDeliveryTaskRefs"
                v-for="(item, index) in taskInfo.stockRequestInfoCompanyList"
                :key="'companyDeliveryTask' + index"
                :list="item"
            />
            <div class="button-block">
                <el-button type="danger" @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleSave">保存</el-button>
            </div>
        </div>
    </div>
</template>
<script>
import functions from 'frame/mixins/functions.js';
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import SnbcDescriptions from 'warehouse/components/snbc-descriptions/SnbcDescriptions.vue';
import AllocationTask from './components/AllocationTask.vue';
import CompanyDeliveryTask from './components/CompanyDeliveryTask.vue';

export default {
    name: 'CompanyDeliveryTaskCreate',
    components: {
        SnbcCard,
        SnbcDescriptions,
        AllocationTask,
        CompanyDeliveryTask
    },
    mixins: [functions],
    data() {
        return {
            taskInfo: {
                stockRequestInfoCompanyList: [],
                stockRequestInfoTransferList: []
            },
            baseInfo: [
                { label: '备货申请任务编号', prop: 'stockRequestCode', value: '' },
                { label: '备货申请名称', prop: 'stockRequestName', value: '' },
                { label: '备货来源', prop: 'requestFrom', value: '' },
                {
                    label: '目标区仓',
                    prop: 'directionWarehouseName',
                    value: ''
                },
                { label: '备注', prop: 'remark', value: '' }
            ],
            info: {
                directionWarehouseCode: '',
                directionWarehouseName: '',
                requestFrom: '',
                stockRequestCode: '',
                stockRequestName: ''
            }
        };
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.createBaseInfo();
    },
    methods: {
        // 获取备货申请发货任务生成信息
        async createBaseInfo() {
            const { params } = this.$route;
            try {
                const res = await this.$service.warehouse.stockUp.getBasicInfo(params);
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                // 获取备货基本信息
                this.baseInfo.map((item) => {
                    item.value = result[item.prop];
                    return item;
                });
                // 根据详情数据获取传参字段值
                Object.keys(this.info).forEach((key) => {
                    this.info[key] = result[key];
                });
                this.taskInfo = result;
            } catch (error) {
                console.error(error);
                this.$tools.message.err(error);
            }
        },
        // 取消操作
        handleCancel() {
            // 删除页面dom跳转到审核界面
            this.$store
                .dispatch('tagsView/delView', {
                    path: '/app/stock-up/delivery-task-create',
                    name: 'CompanyDeliveryTaskCreate'
                })
                .then(({ visitedViews }) => {
                    this.$router.push({
                        path: '/app/stock-up/application-review'
                    });
                });
        },
        // 保存操作
        async handleSave() {
            try {
                // 校验表单信息
                const result = [];
                for (let i = 0; i < this.taskInfo.stockRequestInfoTransferList.length; i++) {
                    const allocationTaskRef = this.$refs.allocationTaskRefs[i];
                    result.push(this.$tools.validateForm(allocationTaskRef.getFormRef()));
                }
                for (let index = 0; index < this.taskInfo.stockRequestInfoCompanyList.length; index++) {
                    const companyDeliveryTaskRef = this.$refs.companyDeliveryTaskRefs[index];
                    result.push(this.$tools.validateForm(companyDeliveryTaskRef.getFormRef()));
                }
                await Promise.all(result);
            } catch (error) {
                this.$tools.message.err('表单信息填写不完整，请检查');
                return;
            }
            await this.$tools.confirm('确认保存？');
            const params = {
                ...this.info,
                taskListFromCompany: this.taskInfo.stockRequestInfoCompanyList,
                taskListFromWarehouse: this.taskInfo.stockRequestInfoTransferList
            };
            try {
                const res = await this.$service.warehouse.stockUp.addTask(params);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('保存成功');
                this.handleCancel();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.content {
    margin-bottom: 30px;
    .button-block {
        display: flex;
        justify-content: center;
        margin: 30px 0;
    }
}
</style>
