<template>
    <div class="view">
        <div class="content">
            <snbc-base-table v-show="tabsConfig.activeName === '待处理'" ref="tableRef1" :table-config="tableConfig1">
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
            </snbc-base-table>
            <snbc-base-table v-show="tabsConfig.activeName === '查询'" ref="tableRef2" :table-config="tableConfig2">
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
            </snbc-base-table>
            <task-additional-form ref="taskAdditionalRef" />
            <task-additional-details ref="taskAdditionalDetailsRef" />
        </div>
    </div>
</template>
<script>
import taskAdditionalDetails from './components/details.vue';
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcTableTabs from 'warehouse/components/snbc-table/SnbcTableTabs.vue';
import TaskAdditionalForm from './components/TaskAdditionalForm.vue';

const taskAdditionalName = {
    name: '申请名称',
    component: 'SnbcFormInput',
    modelKey: 'taskAdditionalName'
};

const taskAdditionalCode = {
    name: '申请单号',
    component: 'SnbcFormInput',
    modelKey: 'taskAdditionalCode'
};
const taskAdditionalApplyStartTime = {
    name: '开始时间',
    component: 'SnbcFormDatePicker',
    modelKey: 'taskAdditionalApplyStartTime'
};
const taskAdditionalApplyEndTime = {
    name: '结束时间',
    component: 'SnbcFormDatePicker',
    modelKey: 'taskAdditionalApplyEndTime'
};
const warehouseCode = {
    name: '区仓',
    component: 'SnbcFormWarehouseSelect',
    modelKey: 'warehouseCode'
};

const taskAdditionalState = {
    name: '状态',
    component: 'SnbcFormSelect',
    modelKey: 'taskAdditionalState',
    elOptions: [
        { label: '审核中', value: '审核中' },
        { label: '待提交', value: '待提交' },
        { label: '通过', value: '通过' },
        { label: '驳回', value: '驳回' }
    ]
};

const taskAdditionalType = {
    name: '补录类型',
    component: 'SnbcFormSelect',
    modelKey: 'taskAdditionalType',
    elOptions: [
        { label: '入库', value: '入库' },
        { label: '出库', value: '出库' }
    ]
};

export default {
    name: 'TaskAdditionalManagement',
    components: {
        taskAdditionalDetails,
        SnbcBaseTable,
        SnbcTableTabs,
        TaskAdditionalForm
    },
    mixins: [functions],

    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            queryParams: {
                taskAdditionalName: '',
                taskAdditionalCode: '',
                taskAdditionalApplyStartTime: '',
                taskAdditionalApplyEndTime: '',
                warehouseCode: '',
                associatedTaskType: '',
                associatedTaskCode: '',
                taskAdditionalState: '',
                taskAdditionalType: ''
            },

            // 查询区域配置项
            queryConfig: {
                items: [
                    taskAdditionalName,
                    taskAdditionalCode,
                    taskAdditionalApplyStartTime,
                    taskAdditionalApplyEndTime,
                    warehouseCode,
                    taskAdditionalState,
                    taskAdditionalType
                ]
            },
            elTableColumns: [
                { label: '序号', prop: 'index', show: true, minWidth: 50 },
                {
                    label: '申请单号',
                    prop: 'taskAdditionalCode',
                    show: true,
                    minWidth: 200,
                    renderMode: 'button',
                    elButtonAttrs: {
                        type: 'text'
                    },
                    handleClick: this.handleView
                },
                {
                    label: '申请名称',
                    prop: 'taskAdditionalName',
                    show: true,
                    minWidth: 160
                },
                {
                    label: '源仓名称',
                    prop: 'srcWarehouseName',
                    show: true,
                    minWidth: 160
                },
                {
                    label: '目标区仓',
                    prop: 'warehouseName',
                    show: true,
                    minWidth: 160
                },
                {
                    label: '补录类型',
                    prop: 'taskAdditionalType',
                    show: true,
                    minWidth: 160
                },
                {
                    label: '关联任务类型',
                    prop: 'associatedTaskType',
                    show: true,
                    minWidth: 160
                },
                {
                    label: '关联任务编码',
                    prop: 'associatedTaskCode',
                    show: true,
                    minWidth: 160
                },
                {
                    label: '创建时间',
                    prop: 'createTime',
                    show: true,
                    minWidth: 160
                },
                {
                    label: '状态',
                    prop: 'taskAdditionalState',
                    show: true,
                    minWidth: 160
                }
            ],
            // 标签页配置
            tabsConfig: {
                activeName: '待处理',
                tabItems: ['待处理', '查询'],
                handleTabClick: this.handleTabClick
            },
            // 待提交Table配置
            tableConfig1: {
                queryParams: {},
                // 查询区域配置项
                queryConfig: {},
                elTableColumns: [],
                queryApi: this.$service.warehouse.taskAdditional.list,
                operations: [
                    {
                        name: '编辑',
                        type: 'warning',
                        handleClick: this.handleEdit,
                        handleShow(row) {
                            return row.taskAdditionalState === '待提交' || row.taskAdditionalState === '驳回';
                        }
                    },
                    {
                        name: '提交',
                        handleClick: this.handleSubmit,
                        handleShow(row) {
                            return row.taskAdditionalState === '待提交' || row.taskAdditionalState === '驳回';
                        }
                    },
                    {
                        name: '审核',
                        type: 'primary',
                        handleClick: this.handleAudit,
                        handleShow(row) {
                            return row.taskAdditionalState === '审核中';
                        }
                    }
                ]
            },
            // 已提交Table配置
            tableConfig2: {
                queryParams: {},
                // 查询区域配置项
                queryConfig: {},
                elTableColumns: [],
                queryApi: this.$service.warehouse.taskAdditional.listSearch,
                hooks: {
                    tableListHook: this.tableListHook
                }
            }
        };
    },
    computed: {},
    mounted() {
        this.tableConfig1.elTableColumns = this.elTableColumns;
        this.tableConfig2.elTableColumns = this.elTableColumns;
        this.tableConfig1.queryParams = this.queryParams;
        this.tableConfig2.queryParams = this.queryParams;
        this.tableConfig1.queryConfig = this.queryConfig;
        this.tableConfig2.queryConfig = this.queryConfig;
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef1.handleQuery();
    },
    methods: {
        // 查看操作
        async handleView(row) {
            const res = await this.$service.warehouse.taskAdditional.detail(row.taskAdditionalCode);
            const { code, message } = res;
            if (code !== '000000') {
                this.$tools.message.err(message || '获取任务补录详情失败！');
                return;
            }
            this.$refs.taskAdditionalDetailsRef.openDialog(res.result);
        },
        // 标签切换
        handleTabClick(tab) {
            if (tab.name === '查询') {
                this.$refs.tableRef2.handleQuery();
            } else {
                this.$refs.tableRef1.handleQuery();
            }
        },
        handleClickAdd() {
            const data = {};
            this.$refs.dialogRef.openDialog('add', data);
        },
        // 编辑操作
        async handleSubmit(row) {
            try {
                const res = await this.$service.warehouse.taskAdditional.detail(row.taskAdditionalCode);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '获取任务补录详情失败！');
                    return;
                }
                this.$refs.taskAdditionalRef.openDialog(res.result, 'edit', true);
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 编辑操作
        async handleEdit(row) {
            try {
                const res = await this.$service.warehouse.taskAdditional.detail(row.taskAdditionalCode);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '获取任务补录详情失败！');
                    return;
                }
                this.$refs.taskAdditionalRef.openDialog(res.result, 'edit');
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 审核
        async handleAudit(row) {
            try {
                const res = await this.$service.warehouse.taskAdditional.detail(row.taskAdditionalCode);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '获取任务补录详情失败！');
                    return;
                }
                this.$refs.taskAdditionalRef.openDialog(res.result, 'audit');
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        }
    }
};
</script>
<style lang="scss" scoped></style>
