<template>
    <div class="view">
        <div class="content">
            <snbc-base-table v-show="tabsConfig.activeName === '待审核'" ref="tableRef1" :table-config="tableConfig1">
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
            </snbc-base-table>
            <snbc-base-table v-show="tabsConfig.activeName === '已审核'" ref="tableRef2" :table-config="tableConfig2">
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
            </snbc-base-table>
        </div>
        <application-details ref="applicationDetailsRef" />
        <task-details ref="taskDetailsRef" />
    </div>
</template>

<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcTableTabs from 'warehouse/components/snbc-table/SnbcTableTabs.vue';
import ApplicationDetails from './components/ApplicationDetails.vue';
import TaskDetails from './components/TaskDetails.vue';
import FormItems from 'warehouse/common/form-items/index.js';

const { stockRequestCode, requestDate, directionWarehouseCode, stockRequestName, createUserName } = FormItems;

// 页面Table配置
const commonTableConfig = {
    queryParams: {
        stockRequestCode: '',
        directionWarehouseCode: '',
        createUserName: '',
        stockRequestName: '',
        requestDate: []
    },
    queryConfig: {
        items: [stockRequestCode, directionWarehouseCode, createUserName, stockRequestName, requestDate]
    },
    elTableColumns: []
};

export default {
    name: 'StockUpApplicationReview',
    components: {
        SnbcBaseTable,
        SnbcTableTabs,
        ApplicationDetails,
        TaskDetails
    },
    mixins: [functions],
    data() {
        const hooks = {
            queryParamsHook: this.queryParamsHook
        };
        return {
            // 标签页配置
            tabsConfig: {
                activeName: '待审核',
                tabItems: ['待审核', '已审核'],
                handleTabClick: this.handleTabClick
            },
            // 待提交Table配置
            tableConfig1: {
                ...this.$tools.cloneDeep(commonTableConfig),
                queryApi: this.$service.warehouse.stockUp.getList,
                operations: [
                    {
                        name: '审核',
                        type: 'primary',
                        handleClick: this.handleReview
                    }
                ],
                hooks
            },
            // 已提交Table配置
            tableConfig2: {
                ...this.$tools.cloneDeep(commonTableConfig),
                queryApi: this.$service.warehouse.stockUp.getReviewedList,
                operations: [
                    {
                        name: '分解',
                        type: 'success',
                        width: 80,
                        handleClick: this.handleSendOut,
                        handleShow(row) {
                            return (
                                (row.deliverGoods === null || row.checkNumber > row.splitNumber) &&
                                row.requestState !== '拒绝'
                            );
                        }
                    },
                    {
                        name: '查看调拨任务',
                        type: 'primary',
                        width: 100,
                        handleClick: this.handleViewAllocationTask,
                        handleShow(row) {
                            return row.deliverGoods === '调拨发货' || row.deliverGoods === '公司调拨';
                        }
                    },
                    {
                        name: '查看公司发货任务',
                        type: 'primary',
                        width: 100,
                        handleClick: this.handleViewCompanyDeliveryTask,
                        handleShow(row) {
                            return row.deliverGoods === '公司发货' || row.deliverGoods === '公司调拨';
                        }
                    },
                    {
                        name: '查看原因',
                        type: 'danger',
                        width: 80,
                        handleClick: this.handleReason,
                        handleShow(row) {
                            return row.requestState === '拒绝';
                        }
                    }
                ],
                hooks
            }
        };
    },
    computed: {},
    created() {
        this.tableColumnInit();
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef1.handleQuery();
    },
    methods: {
        // 初始化table 的 列
        tableColumnInit() {
            const columns1 = [
                { label: '序号', prop: 'index', show: true, minWidth: 80 },
                {
                    label: '备货申请任务编号',
                    prop: 'stockRequestCode',
                    show: true,
                    minWidth: 200,
                    renderMode: 'button',
                    elButtonAttrs: {
                        type: 'text'
                    },
                    handleClick: this.handleDetail
                },
                {
                    label: '备货申请任务名称',
                    prop: 'stockRequestName',
                    show: true,
                    minWidth: 240
                },
                {
                    label: '申请时间',
                    prop: 'createTime',
                    show: true,
                    minWidth: 160
                },
                {
                    label: '申请人',
                    prop: 'createUserName',
                    show: true,
                    minWidth: 120
                },
                {
                    label: '目标区仓',
                    prop: 'directionWarehouseName',
                    show: true,
                    minWidth: 160
                },
                { label: '备注', prop: 'remark', show: true, minWidth: 120 }
            ];
            const columns2 = [
                ...this.$tools.cloneDeep(columns1),
                {
                    label: '审核状态',
                    prop: 'requestState',
                    show: true,
                    minWidth: 120
                }
            ];
            this.tableConfig1.elTableColumns = columns1;
            this.tableConfig2.elTableColumns = columns2;
        },
        // 标签切换
        handleTabClick(tab) {
            if (tab.name === '已审核') {
                this.$refs.tableRef2.handleQuery();
            } else {
                this.$refs.tableRef1.handleQuery();
            }
        },
        // 列表查询参数hook方法
        queryParamsHook(params) {
            params.queryStatus = this.tabsConfig.activeName;
            params.requestStartDate = params.requestDate[0] || '';
            params.requestEndDate = params.requestDate[1] || '';
        },
        // 审核操作
        handleReview(row) {
            this.$refs.applicationDetailsRef.showDetailsDialog(row.stockRequestCode);
        },
        // 发货任务
        handleSendOut(row) {
            this.$refs.taskDetailsRef.showDetailsDialog(row.stockRequestCode);
        },
        // 查看调拨任务
        handleViewAllocationTask(row) {
            this.$router.push({
                path: '/app/allocation/allocation-management',
                query: { stockRequestCode: row.stockRequestCode }
            });
        },
        // 查看公司发货任务
        handleViewCompanyDeliveryTask(row) {
            this.$router.push({
                path: '/app/company-delivery/management-list',
                query: { stockRequestCode: row.stockRequestCode }
            });
        },
        // 查看拒绝原因
        handleReason(row) {
            this.$tools.confirm(row.reviewRemark, '拒绝原因', {
                type: 'error',
                showCancelButton: false
            });
        },
        // 查看备货申请详情
        handleDetail(row) {
            this.$router.push({
                path: '/app/stock-up/application-details',
                query: {
                    stockRequestCode: row.stockRequestCode
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.content {
    ::v-deep .el-tabs__content {
        padding: 3px;
    }
}
</style>
