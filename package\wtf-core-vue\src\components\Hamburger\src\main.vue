<template>
  <div style="padding: 0 15px;" @click="toggleClick">
	<i class="el-icon-s-unfold sub-el-icon" v-show="isActive"></i>
	<i class="el-icon-s-fold sub-el-icon" v-show="!isActive"></i>
  </div>
</template>

<script>
export default {
    name: '<PERSON><PERSON>',
    props: {
        isActive: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        toggleClick() {
            this.$emit('toggleClick');
        }
    }
};
</script>

<style scoped>
.hamburger {
  display: inline-block;
  vertical-align: middle;
  width: 20px;
  height: 20px;
}

.hamburger.is-active {
  transform: rotate(180deg);
}
.sub-el-icon{
	font-size:18px;
	color:#ffffff;
}
</style>
