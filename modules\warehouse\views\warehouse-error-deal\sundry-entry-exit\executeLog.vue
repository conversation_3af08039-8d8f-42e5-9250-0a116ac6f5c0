<template>
    <div class="dialog-container">
        <el-timeline>
            <el-timeline-item
                v-for="item in timeline"
                :key="item.key"
                :timestamp="item.timestamp"
                :hide-timestamp="true"
                color="#409eff"
            >
                <el-card>
                    <p>【{{ item.operateTime }}】 {{ item.content }}</p>
                    <p>
                        【{{ item.operateType }}】
                        {{ item.operateUserName }} 【{{ item.operateState }}】
                    </p>
                </el-card>
            </el-timeline-item>
        </el-timeline>
        <div class="no-data" v-if="!timeline.length">暂无数据</div>
    </div>
</template>
<script>
export default {
    name: 'SundryExecuteLog',
    props: {
        sundryEntryExitCode: {
            type: String,
            default() {
                return '';
            }
        }
    },
    data() {
        return {
            timeline: []
        };
    },
    watch: {
        sundryEntryExitCode: {
            handler: 'querySundryExecuteLog',
            immediate: true
        }
    },
    methods: {
        querySundryExecuteLog() {
            if (!this.sundryEntryExitCode) return;
            this.$service.warehouse.sundryEntryExit
                .getDetail(this.sundryEntryExitCode)
                .then(({ code, result, message }) => {
                    if (code !== '000000') {
                        this.$tools.message.err(message || '系统异常');
                        return;
                    }
                    this.timeline = result.sundryEntryExitLogs;
                });
        }
    }
};
</script>
<style lang="scss" scoped>
.no-data {
    padding: 16px;
    text-align: center;
}
.dialog-container {
    height: 400px;
    overflow-y: auto;
    padding: 16px;
    margin: 0 !important;
}
</style>
