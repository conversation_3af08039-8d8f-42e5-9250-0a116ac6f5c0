<template>
    <component :is="config.component" :config="config" @validate="handleValidate">
        <template #[slotName] v-for="(slot, slotName) in $slots">
            <slot :name="slotName" />
        </template>
    </component>
</template>
<script>
import SnbcFormInput from 'warehouse/components/snbc-form/SnbcFormInput.vue';
import SnbcFormInputNumber from 'warehouse/components/snbc-form/SnbcFormInputNumber.vue';
import SnbcFormTextarea from 'warehouse/components/snbc-form/SnbcFormTextarea.vue';
import SnbcFormSelect from 'warehouse/components/snbc-form/SnbcFormSelect.vue';
import SnbcFormDatePicker from 'warehouse/components/snbc-form/SnbcFormDatePicker.vue';
import SnbcFormDateRangePicker from 'warehouse/components/snbc-form/SnbcFormDateRangePicker.vue';
import SnbcFormDictSelect from 'warehouse/components/snbc-form/SnbcFormDictSelect.vue';
import SnbcFormRegion from 'warehouse/components/snbc-form/SnbcFormRegion.vue';
import SnbcFormWarehouseSelect from 'warehouse/components/snbc-form/SnbcFormWarehouseSelect.vue';
import SnbcFormProductSelect from 'warehouse/components/snbc-form/SnbcFormProductSelect.vue';
import SnbcFormSupplierSelect from 'warehouse/components/snbc-form/SnbcFormSupplierSelect.vue';
import SnbcFormCustomerSelect from 'warehouse/components/snbc-form/SnbcFormCustomerSelect.vue';
import SnbcFormDeliveryTaskSelect from 'warehouse/components/snbc-form/SnbcFormDeliveryTaskSelect.vue';
import SnbcFormFileUpload from 'warehouse/components/snbc-form/SnbcFormFileUpload.vue';
import SnbcFormCarrierSupplierSelect from 'warehouse/components/snbc-form/SnbcFormCarrierSupplierSelect.vue';
import SnbcFormTaskStateSelect from 'warehouse/components/snbc-form/SnbcFormTaskStateSelect.vue';
import SnbcFormDistanceInput from 'warehouse/components/snbc-form/SnbcFormDistanceInput.vue';
import SnbcFormRegionWarehouseSelect from 'warehouse/components/snbc-form/SnbcFormRegionWarehouseSelect.vue';
import SnbcFormMultiCitySelect from 'warehouse/components/snbc-form/SnbcFormMultiCitySelect.vue';
import SnbcFormProviderSelect from 'warehouse/components/snbc-form/SnbcFormProviderSelect.vue';
import SnbcFormServeClientsSelect from 'warehouse/components/snbc-form/SnbcFormServeClientsSelect.vue';

export default {
    // 动态组件，根据config.component值不同应用不同的表单组件
    name: 'SnbcFormItem',
    components: {
        SnbcFormInput,
        SnbcFormInputNumber,
        SnbcFormTextarea,
        SnbcFormSelect,
        SnbcFormDatePicker,
        SnbcFormDateRangePicker,
        SnbcFormDictSelect,
        SnbcFormRegion,
        SnbcFormWarehouseSelect,
        SnbcFormProductSelect,
        SnbcFormSupplierSelect,
        SnbcFormCustomerSelect,
        SnbcFormFileUpload,
        SnbcFormDeliveryTaskSelect,
        SnbcFormCarrierSupplierSelect,
        SnbcFormTaskStateSelect,
        SnbcFormDistanceInput,
        SnbcFormRegionWarehouseSelect,
        SnbcFormMultiCitySelect,
        SnbcFormProviderSelect,
        SnbcFormServeClientsSelect
    },
    props: {
        /**
         * SnbcFormItem组件配置
         */
        config: {
            type: Object,
            default() {
                return {};
            }
        }
    },
    data() {
        return {};
    },
    computed: {},
    methods: {
        handleValidate(prop) {
            this.$emit('validate', prop);
        }
    }
};
</script>
