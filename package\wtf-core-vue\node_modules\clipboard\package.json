{"_args": [["clipboard@2.0.4", "C:\\Users\\<USER>\\Desktop\\si-warehouse-page"]], "_from": "clipboard@2.0.4", "_id": "clipboard@2.0.4", "_inBundle": false, "_integrity": "sha512-Vw26VSLRpJfBofiVaFb/I8PVfdI1OxKcYShe6fm0sP/DtmiWQNCjhM/okTvdCo0G+lMMm1rMYbk4IK4x1X+kgQ==", "_location": "/wtf-core-vue/clipboard", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "clipboard@2.0.4", "name": "clipboard", "escapedName": "clipboard", "rawSpec": "2.0.4", "saveSpec": null, "fetchSpec": "2.0.4"}, "_requiredBy": ["/wtf-core-vue"], "_resolved": "http://maven.xtjc.net/repository/npm-all/clipboard/-/clipboard-2.0.4.tgz", "_spec": "2.0.4", "_where": "C:\\Users\\<USER>\\Desktop\\si-warehouse-page", "browserslist": ["> 1%", "last 2 versions"], "bugs": {"url": "https://github.com/zenorocha/clipboard.js/issues"}, "dependencies": {"good-listener": "^1.2.2", "select": "^1.1.2", "tiny-emitter": "^2.0.0"}, "description": "Modern copy to clipboard. No Flash. Just 2kb", "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-loader": "^7.1.4", "babel-preset-env": "^1.6.1", "chai": "^4.2.0", "cross-env": "^5.2.0", "karma": "^3.1.1", "karma-chai": "^0.1.0", "karma-mocha": "^1.2.0", "karma-phantomjs-launcher": "^1.0.0", "karma-sinon": "^1.0.4", "karma-webpack": "^3.0.5", "mocha": "^5.2.0", "phantomjs-prebuilt": "^2.1.4", "sinon": "^7.1.1", "uglifyjs-webpack-plugin": "^2.0.1", "webpack": "^4.5.0", "webpack-cli": "^3.1.2"}, "homepage": "https://github.com/zenorocha/clipboard.js#readme", "keywords": ["clipboard", "copy", "cut"], "license": "MIT", "main": "dist/clipboard.js", "name": "clipboard", "repository": {"type": "git", "url": "git+https://github.com/zenorocha/clipboard.js.git"}, "scripts": {"build": "npm run build-debug && npm run build-min", "build-debug": "webpack", "build-min": "cross-env NODE_ENV=production webpack", "build-watch": "webpack --watch", "prepublish": "npm run build", "test": "karma start --single-run"}, "version": "2.0.4"}