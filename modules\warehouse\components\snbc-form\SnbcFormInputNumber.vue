<template>
    <el-form-item v-bind="elFormItemAttrs">
        <el-input-number
            v-model="config.modelObj[config.modelKey]"
            v-bind="elInputNumberAttrs"
            @keyup.enter.native="handleEnter"
        />
    </el-form-item>
</template>
<script>
export default {
    name: 'SnbcFormInputNumber',
    props: {
        /**
         * SnbcFormInputNumber组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elFormItemAttrs: {},
                    elInputNumberAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-input组件默认属性设置
            defaultElInputNumberAttrs: {
                'clearable': true,
                'controls-position': 'right'
            }
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-input-number组件应用属性
        elInputNumberAttrs() {
            return {
                ...this.defaultElInputNumberAttrs,
                placeholder: `请输入${this.config.name}`,
                ...(this.config.elInputNumberAttrs || {})
            };
        }
    },
    methods: {
        // el-input-number组件回车操作
        handleEnter() {
            this.config.enterHandler && this.config.enterHandler();
        }
    }
};
</script>
