<template>
    <div class="view">
        <div class="content">
            <snbc-base-table v-show="tabsConfig.activeName === '待提交'" ref="tableRef1" :table-config="tableConfig1">
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
            </snbc-base-table>
            <snbc-base-table v-show="tabsConfig.activeName === '已提交'" ref="tableRef2" :table-config="tableConfig2">
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
            </snbc-base-table>
        </div>
        <non-designated-assets-application ref="nonDesignatedAssetsApplicationRef" />
    </div>
</template>

<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcTableTabs from 'warehouse/components/snbc-table/SnbcTableTabs.vue';
import NonDesignatedAssetsApplication from './components/NonDesignatedAssetsApplication.vue';
import FormItems from 'warehouse/common/form-items/index.js';

const { stockRequestCode, requestDate, directionWarehouseCode, stockRequestName, createUserName } = FormItems;

// 页面Table配置
const commonTableConfig = {
    queryParams: {
        stockRequestCode: '',
        directionWarehouseCode: '',
        createUserName: '',
        requestDate: [],
        stockRequestName: ''
    },
    queryConfig: {
        items: [
            stockRequestCode,
            {
                ...directionWarehouseCode,
                component: 'SnbcFormRegionWarehouseSelect'
            },
            createUserName,
            stockRequestName,
            requestDate
        ]
    },
    elTableColumns: []
};

export default {
    name: 'StockUpApplicationCreate',
    components: {
        SnbcBaseTable,
        SnbcTableTabs,
        NonDesignatedAssetsApplication
    },
    mixins: [functions],
    data() {
        const headerButtons = [
            {
                name: '新增',
                type: 'primary',
                handleClick: this.applyNonDesignatedAssets
            }
        ];
        const hooks = {
            queryParamsHook: this.queryParamsHook
        };
        return {
            // 标签页配置
            tabsConfig: {
                activeName: '待提交',
                tabItems: ['待提交', '已提交'],
                handleTabClick: this.handleTabClick
            },
            // 待提交Table配置
            tableConfig1: {
                ...this.$tools.cloneDeep(commonTableConfig),
                queryApi: this.$service.warehouse.stockUp.getNotSubmittedList,
                operations: [
                    {
                        name: '编辑',
                        type: 'warning',
                        handleClick: this.handleEdit
                    },
                    {
                        name: '删除',
                        type: 'danger',
                        handleClick: this.handleDelete,
                        handleShow(row) {
                            return row.requestState === '草稿';
                        }
                    }
                ],
                headerButtons,
                hooks
            },
            // 已提交Table配置
            tableConfig2: {
                ...this.$tools.cloneDeep(commonTableConfig),
                queryApi: this.$service.warehouse.stockUp.getList,
                operations: [
                    {
                        name: '撤回',
                        type: 'danger',
                        handleClick: this.handleRevoke,
                        handleShow(row) {
                            return row.requestState === '待审核';
                        }
                    }
                ],
                hooks
            }
        };
    },
    computed: {},
    created() {
        this.tableColumnInit();
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef1.handleQuery();
    },
    methods: {
        // 标签切换
        handleTabClick(tab) {
            if (tab.name === '已提交') {
                this.$refs.tableRef2.handleQuery();
            } else {
                this.$refs.tableRef1.handleQuery();
            }
        },
        // 初始化table 的 列
        tableColumnInit() {
            const columns = [
                { label: '序号', prop: 'index', show: true, minWidth: 80 },
                {
                    label: '备货申请任务编号',
                    prop: 'stockRequestCode',
                    show: true,
                    minWidth: 200,
                    renderMode: 'button',
                    elButtonAttrs: {
                        type: 'text'
                    },
                    handleClick: this.handleDetail
                },
                {
                    label: '备货申请任务名称',
                    prop: 'stockRequestName',
                    show: true,
                    minWidth: 200
                },
                {
                    label: '申请时间',
                    prop: 'createTime',
                    show: true,
                    minWidth: 160
                },
                {
                    label: '申请人',
                    prop: 'createUserName',
                    show: true,
                    minWidth: 120
                },
                {
                    label: '目标区仓',
                    prop: 'directionWarehouseName',
                    show: true,
                    minWidth: 120
                },
                { label: '备注', prop: 'remark', show: true, minWidth: 120 },
                {
                    label: '状态',
                    prop: 'requestState',
                    show: true,
                    minWidth: 120
                }
            ];
            this.tableConfig1.elTableColumns = columns;
            this.tableConfig2.elTableColumns = columns;
        },
        // 列表查询参数hook方法
        queryParamsHook(params) {
            params.queryStatus = this.tabsConfig.activeName;
            params.requestStartDate = params.requestDate[0] || '';
            params.requestEndDate = params.requestDate[1] || '';
        },
        // 非指定资产新增
        applyNonDesignatedAssets() {
            const data = {
                stockRequest: {},
                stockRequestDetailList: []
            };
            this.$refs.nonDesignatedAssetsApplicationRef.openDialog({ mode: 'add', title: '新增备货申请任务' }, data);
        },
        // 编辑操作
        async handleEdit(row) {
            const params = {
                stockRequestCode: row.stockRequestCode
            };
            try {
                const res = await this.$service.warehouse.stockUp.getDetails(params);
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$refs.nonDesignatedAssetsApplicationRef.openDialog(
                    { mode: 'edit', title: '编辑备货申请任务' },
                    result
                );
            } catch (error) {
                console.error(error);
                this.$tools.message.err(error);
            }
        },
        // 草稿删除操作
        async handleDelete(row) {
            await this.$tools.confirm('确认删除？');
            try {
                const res = await this.$service.warehouse.stockUp.delete(row.stockRequestCode);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.$refs.tableRef1.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err(error);
            }
        },
        // 撤回操作
        async handleRevoke(item) {
            await this.$tools.confirm('确认撤回？');
            try {
                const res = await this.$service.warehouse.stockUp.revoke(item.stockRequestCode);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.$refs.tableRef2.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 查看备货申请详情
        handleDetail(row) {
            this.$router.push({
                path: '/app/stock-up/application-details',
                query: {
                    stockRequestCode: row.stockRequestCode
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
