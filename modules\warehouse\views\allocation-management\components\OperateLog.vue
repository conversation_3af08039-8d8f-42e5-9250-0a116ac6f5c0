<template>
    <div>
        <el-timeline>
            <el-timeline-item
                v-for="item in timeline"
                :key="item.key"
                :timestamp="item.timestamp"
                :hide-timestamp="true"
                color="#409eff"
            >
                <el-card>
                    <p>
                        【{{ item.createTime }}】 {{ item.createUserName }}
                        {{ item.timestamp }}
                    </p>
                    <p>{{ item.operateContent }}</p>
                    <p>{{ item.operateType }}</p>
                </el-card>
            </el-timeline-item>
        </el-timeline>
        <div class="no-data" v-if="!timeline.length">暂无数据</div>
    </div>
</template>
<script>
export default {
    name: 'AllocationOperateLog',
    props: {
        allocateTaskCode: {
            type: String,
            default() {
                return '';
            }
        },
        whetherSpecifyAsset: {
            type: Boolean,
            default() {
                return false;
            }
        }
    },
    data() {
        return {
            timeline: []
        };
    },
    watch: {
        allocateTaskCode: {
            handler: 'queryAllocateTaskOperationLog',
            immediate: true
        }
    },
    methods: {
        queryAllocateTaskOperationLog() {
            this.$service.warehouse.allocation
                .queryAllocateTaskOperationLog(this.allocateTaskCode)
                .then((result) => {
                    if (result.code === '000000') {
                        this.timeline = result.result;
                    }
                });
        }
    }
};
</script>
<style lang="scss" scoped>
.no-data {
    padding: 16px;
    text-align: center;
}
</style>
