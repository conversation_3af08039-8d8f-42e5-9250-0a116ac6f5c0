<template>
    <div class="view">
        <div class="content query-label-line2">
            <snbc-table-query
                :query-params="tableConfig.queryParams"
                :query-config="tableConfig.queryConfig"
                @query="getOperationRecordDetails"
            />
            <snbc-card class="margin-top-10" title="产品信息">
                <template #card-body>
                    <snbc-descriptions :items="basicInfo" :config="config" />
                </template>
            </snbc-card>
            <snbc-card class="margin-top-10" title="产品操作记录">
                <template #card-body>
                    <snbc-table-list :config="tableConfig" :list="list" />
                </template>
            </snbc-card>
        </div>
    </div>
</template>
<script>
import functions from 'frame/mixins/functions.js';
import SnbcTableQuery from 'warehouse/components/snbc-table/SnbcTableQuery.vue';
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import SnbcDescriptions from 'warehouse/components/snbc-descriptions/SnbcDescriptions';
import SnbcTableList from 'warehouse/components/snbc-table/SnbcTableList.vue';
import CommonItems from 'warehouse/common/form-items/common-items.js';

// 查询条件
const queryParams = {
    assetCode: '',
    customerAssetCode: ''
};
const assetCode = {
    ...CommonItems.input,
    name: '产品序列号',
    modelKey: 'assetCode'
};
const customerAssetCode = {
    ...CommonItems.input,
    name: '客户资产编码',
    modelKey: 'customerAssetCode'
};
// 查询区域配置项
const queryConfigItems = [assetCode, customerAssetCode];

export default {
    name: 'OperateLogDetails',
    components: {
        SnbcTableQuery,
        SnbcDescriptions,
        SnbcTableList,
        SnbcCard
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 产品信息展示字段
            basicInfo: [
                { label: '产品名称', key: 'productName', value: '' },
                { label: '所在区仓', key: 'warehouseName', value: '' },
                { label: '资产状态', key: 'assetState', value: '' },
                { label: '在库状态', key: 'inState', value: '' },
                { label: '资产类型', key: 'assetType', value: '' },
                { label: '出入库时间', key: 'happenDate', value: '' },
                { label: '在库天数', key: 'storageDays', value: '' },
                { label: '客户资产编码', key: 'customerAssetCode', value: '' },
                { label: '产品序列号', key: 'assetCode', value: '' },
                { label: '物料编码', key: 'materialCode', value: '' }
            ],
            // 列表配置
            tableConfig: {
                // 查询条件对象
                queryParams,
                // 查询条件配置
                queryConfig: {
                    resetHidden: true,
                    items: queryConfigItems
                },
                // 列表列配置
                elTableColumns: [
                    {
                        label: '序号',
                        prop: 'index',
                        show: true,
                        minWidth: 60
                    },
                    {
                        label: '发生时间',
                        prop: 'happenDate',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '类型',
                        prop: 'operationType',
                        show: true,
                        minWidth: 80
                    },
                    {
                        label: '关联任务名称',
                        prop: 'associatedTaskName',
                        show: true,
                        minWidth: 240
                    },
                    {
                        label: '关联任务编号',
                        prop: 'associatedTaskCode',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '任务类型',
                        prop: 'taskType',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '修改申请编号',
                        prop: 'taskAdditionalCode',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '操作人',
                        prop: 'createUserName',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 120
                    }
                ]
            },
            // 列表数据
            list: [],
            config: {
                elDescriptionsAttrs: {
                    column: 4,
                    labelStyle: {
                        width: '100px'
                    },
                    contentStyle: {
                        'width': '150px',
                        'word-break': 'break-all'
                    }
                }
            }
        };
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.init();
    },
    methods: {
        init() {
            // 获取页面传参
            queryParams.assetCode = this.$route.query.assetCode || '';
            queryParams.customerAssetCode =
                this.$route.query.customerAssetCode || '';
            if (queryParams.assetCode || queryParams.customerAssetCode) {
                this.getOperationRecordDetails();
            }
        },
        // 设置默认数据
        setDefaultData() {
            this.basicInfo.forEach((item) => {
                item.value = '';
            });
            this.list = [];
        },
        // 获取资产操作记录详情
        async getOperationRecordDetails() {
            if (!queryParams.assetCode && !queryParams.customerAssetCode) {
                this.$tools.message.warning('参数不能为空');
                return;
            }
            // 重置页面数据
            this.setDefaultData();
            const { code, result, message } =
                await this.$service.warehouse.assets.getOperationRecordDetails(
                    queryParams.assetCode,
                    queryParams.customerAssetCode
                );
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
                return;
            }
            const assetInfoPO = result.assetInfoPO || {};
            const assetInOutLogPOList = result.assetInOutLogPOList || [];
            this.basicInfo.forEach((item) => {
                item.value = assetInfoPO[item.key];
            });
            this.list = assetInOutLogPOList.map((item, index) => {
                item.index = index + 1;
                return item;
            });
        }
    }
};
</script>
<style lang="scss" scoped></style>
