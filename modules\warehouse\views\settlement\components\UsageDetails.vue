<template>
    <div class="content">
        <snbc-table-list :config="tableConfig" :list="usageList" />
    </div>
</template>
<script>
import SnbcTableList from 'warehouse/components/snbc-table/SnbcTableList.vue';

const commonColumns = [
    {
        label: '序号',
        show: true,
        prop: 'index',
        elTableColumnAttrs: {
            width: 80
        }
    },
    {
        label: '客户',
        prop: 'customerName',
        show: true,
        minWidth: 120
    },
    {
        label: '资产类型',
        prop: 'assetType',
        show: true,
        minWidth: 120
    }
];

const trafficColumns = [
    ...commonColumns,
    {
        label: '资产数量',
        prop: 'assetCount',
        show: true,
        minWidth: 120
    },
    {
        label: '存放天数',
        prop: 'storageDays',
        show: true,
        minWidth: 120
    },
    {
        label: '单价(台/天/元)',
        prop: 'unitPrice',
        show: true,
        minWidth: 120
    },
    {
        label: '费用(元)',
        prop: 'fee',
        show: true,
        minWidth: 120
    }
];

const areaColumns = [
    ...commonColumns,
    {
        label: '月平均面积(平)',
        prop: 'areaAverage',
        show: true,
        minWidth: 120
    },
    {
        label: '单价(平/月/元)',
        prop: 'unitPrice',
        show: true,
        minWidth: 120
    },
    {
        label: '费用(元)',
        prop: 'fee',
        show: true,
        minWidth: 120
    },
    {
        label: '结算后面积(平)',
        prop: 'areaSettlement',
        show: true,
        minWidth: 120
    },
    {
        label: '结算后费用(元)',
        prop: 'feeSettlement',
        show: true,
        minWidth: 120
    }
];

export default {
    name: 'UsageDetails',
    components: {
        SnbcTableList
    },
    props: {
        usageList: {
            type: Array,
            default() {
                return [];
            }
        },
        settlementMethod: {
            type: String,
            default() {
                return '';
            }
        }
    },
    data() {
        return {
            tableConfig: {
                elTableColumns: []
            }
        };
    },
    watch: {
        usageList: {
            handler: 'init',
            deep: true,
            immediate: true
        }
    },
    methods: {
        init() {
            if (this.settlementMethod === '流量') {
                this.tableConfig.elTableColumns = trafficColumns;
            } else {
                this.tableConfig.elTableColumns = areaColumns;
            }
        }
    }
};
</script>
<style lang="scss" scoped></style>
