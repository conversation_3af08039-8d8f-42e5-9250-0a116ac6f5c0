<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig">
            </snbc-base-table>
        </div>
    </div>
</template>
<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';

const fixedColumns = [
    { label: '序号', prop: 'index', show: true, minWidth: 80 },
    {
        label: '产品线',
        prop: 'productLineName',
        show: true,
        minWidth: 160
    },
    {
        label: '类别',
        prop: 'productCategoryName',
        show: true,
        minWidth: 160
    },
    {
        label: '柜机类别',
        prop: 'cabinetType',
        show: true,
        minWidth: 160
    },
    {
        label: '客户',
        prop: 'customerName',
        show: true,
        minWidth: 240
    }
    // {
    //     label: '质保时长（月）',
    //     prop: 'protectTime',
    //     show: true,
    //     minWidth: 160
    // }
];

export default {
    name: 'Statistics',
    components: {
        SnbcBaseTable
    },
    mixins: [functions],
    data() {
        const { searchAssetPointByMonth: queryApi, exportExcel } =
            this.$service.asset.statistics;
        return {
            exportExcel,
            tableConfig: {
                queryParams: {
                    currentMonth: ''
                },
                queryConfig: {
                    items: [
                        {
                            name: '统计起始月份',
                            component: 'SnbcFormDatePicker',
                            modelKey: 'currentMonth',
                            elDatePickerAttrs: {
                                'value-format': 'yyyy-MM',
                                'type': 'month',
                                'clearable': false,
                                'editable': false
                            }
                        }
                    ],
                    elFormAttrs: {
                        'label-width': '120px'
                    },
                    resetHidden: true
                },
                queryApi,
                elTableColumns: [],
                // headerButtons: [
                //     {
                //         name: '导出',
                //         type: 'primary',
                //         handleClick: this.export
                //     }
                // ],
                hooks: {
                    tableListHook: this.handleColumns
                }
            }
        };
    },
    created() {
        let month = new Date().getMonth();
        const year = new Date().getFullYear() - (month ? 0 : 1);
        month = month || 12;
        month = month < 10 ? `0${month}` : month;
        this.tableConfig.queryParams.currentMonth = `${year}-${month || 12}`;
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.handleQuery();
    },
    methods: {
        async export() {
            const stream = await this.exportExcel(
                this.tableConfig.queryParams.currentMonth
            );
            this.$tools
                .downloadExprotFile(
                    stream,
                    `资产质保统计${this.tableConfig.queryParams.currentMonth}`,
                    'xlsx',
                    'xlsx'
                )
                .then(() => {
                    this.$tools.message.suc('导出成功');
                })
                .catch((e) => {
                    this.$tools.message.err(e || '导出失败');
                });
        },
        // 动态列
        dynamicColumns() {
            const [year, month] = this.tableConfig.queryParams.currentMonth
                .split('-')
                .map((item) => Number(item));
            const columns = [
                {
                    label: `截止${year}年${month}月安装数量`,
                    prop: 'deadlineCurrentMonthInstallNum',
                    show: true,
                    minWidth: 200
                },
                {
                    label: `截止${year}年${month}月保内数量`,
                    prop: 'deadlineCurrentMonthBnNum',
                    show: true,
                    minWidth: 200
                },
                {
                    label: `截止${year}年${month}月保外数量`,
                    prop: 'deadlineCurrentMonthBwNum',
                    show: true,
                    minWidth: 200
                },
                // {
                //     label: `截止${year}年${month}月保外承接数量(维保)`,
                //     prop: 'deadlineProtectOut',
                //     show: true,
                //     minWidth: 240
                // },
                // {
                //     label: '保外维护方式',
                //     prop: 'protectType',
                //     show: true,
                //     minWidth: 200
                // },
                {
                    label: `截止${year}年底过保总数量(含往期过保)`,
                    prop: 'currentYearBwNum',
                    show: true,
                    minWidth: 260
                },
                // {
                //     label: `${year}年新增过保数量(加权)`,
                //     prop: 'currentYearNewBwNumAdd',
                //     show: true,
                //     minWidth: 200
                // },
                {
                    label: `${year}年新增过保数量(不加权)`,
                    prop: 'currentYearNewBwNum',
                    show: true,
                    minWidth: 240
                },
                {
                    label: `${year}年${month}月新增过保数量`,
                    prop: 'currentMonthBwNum',
                    show: true,
                    minWidth: 200
                }
            ];
            for (let i = 1; i < 12; i++) {
                const tempMonth = month + i;
                const tempYear = tempMonth > 12 ? year + 1 : year;
                const column = {
                    label: `${tempYear}年${tempMonth % 12 || 12}月新增过保数量`,
                    prop: `nextMonth${i}BwNum`,
                    show: true,
                    minWidth: 200
                };
                columns.push({ ...column });
            }
            return columns;
        },
        handleColumns() {
            this.tableConfig.elTableColumns.splice(0, Number.MAX_SAFE_INTEGER);
            [...fixedColumns, ...this.dynamicColumns()].forEach((item) => {
                this.tableConfig.elTableColumns.push(item);
            });
        }
    }
};
</script>
<style lang="scss" scoped></style>
