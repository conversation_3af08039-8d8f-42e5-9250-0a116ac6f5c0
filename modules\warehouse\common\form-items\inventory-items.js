import commonItems from './common-items.js';

const planCode = {
    ...commonItems.input,
    name: '计划单号',
    modelKey: 'planCode'
};
const warehouseCodeList = {
    ...commonItems.select,
    name: '区仓名称',
    modelKey: 'warehouseCodeList',
    selectAllAble: true,
    elSelectAttrs: {
        multiple: true
    }
};
const warehouseCode = {
    name: '区仓名称',
    component: 'SnbcFormWarehouseSelect',
    modelKey: 'warehouseCode'
};
const startTime = {
    ...commonItems.input,
    name: '开始时间',
    component: 'SnbcFormDatePicker',
    modelKey: 'startTime'
};
const requiredFinishTime = {
    ...commonItems.input,
    name: '要求完成时间',
    component: 'SnbcFormDatePicker',
    modelKey: 'requiredFinishTime'
};
const sendTime = {
    ...commonItems.input,
    name: '下发时间',
    component: 'SnbcFormDatePicker',
    modelKey: 'sendTime'
};
const endTime = {
    ...commonItems.input,
    name: '结束时间',
    component: 'SnbcFormDatePicker',
    modelKey: 'endTime'
};
const planDesc = {
    ...commonItems.input,
    name: '盘点描述',
    modelKey: 'planDesc'
};
const createBy = {
    ...commonItems.input,
    name: '创建人',
    modelKey: 'createBy'
};
const planState = {
    ...commonItems.select,
    name: '盘点状态',
    modelKey: 'planState',
    elOptions: [
        { label: '未下发', value: 'unsend' },
        { label: '未开始', value: 'unstart' },
        { label: '进行中', value: 'inprogress' },
        { label: '已取消', value: 'canceled' },
        { label: '已完成', value: 'finished' }
    ]
};
const taskState = {
    ...commonItems.select,
    name: '任务状态',
    modelKey: 'taskState',
    elOptions: [
        { label: '未下发', value: 'unsend' },
        { label: '未开始', value: 'unstart' },
        { label: '进行中', value: 'inprogress' },
        { label: '已取消', value: 'canceled' },
        { label: '已完成', value: 'finished' }
    ]
};
const planName = {
    ...commonItems.input,
    name: '计划名称',
    modelKey: 'planName'
};
const productList = {
    ...commonItems.select,
    name: '产品名称',
    modelKey: 'productList',
    selectAllAble: true,
    elSelectAttrs: {
        multiple: true
    }
};
const taskCode = {
    ...commonItems.input,
    name: '任务单号',
    modelKey: 'taskCode'
};
const materialCode = {
    ...commonItems.input,
    name: '物料编码',
    modelKey: 'materialCode'
};
const remark = {
    ...commonItems.input,
    name: '备注',
    modelKey: 'remark',
    elInputAttrs: {
        placeholder:
            '请填写具体修正说明，示例：xxx已经和现场沟通确认，该资产实际在仓库。'
    }
};

export default {
    createBy,
    planCode,
    startTime,
    endTime,
    planDesc,
    planState,
    planName,
    productList,
    requiredFinishTime,
    warehouseCodeList,
    warehouseCode,
    taskCode,
    materialCode,
    sendTime,
    taskState,
    remark
};
