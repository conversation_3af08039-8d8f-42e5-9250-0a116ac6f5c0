/**
 * 备货申请相关表单组件
 */
import commonItems from './common-items.js';

// 备货申请任务编号
const stockRequestCode = {
    ...commonItems.input,
    name: '备货申请任务编号',
    modelKey: 'stockRequestCode'
};
// 备货申请时间
const requestDate = {
    ...commonItems.dateRange,
    name: '申请时间',
    modelKey: 'requestDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};
// 目标区仓
const directionWarehouseCode = {
    ...commonItems.select,
    name: '目标区仓',
    component: 'SnbcFormWarehouseSelect',
    modelKey: 'directionWarehouseCode'
};
// 备货申请任务名称
const stockRequestName = {
    ...commonItems.input,
    name: '备货申请任务名称',
    modelKey: 'stockRequestName'
};
// 申请人
const createUserName = {
    ...commonItems.input,
    name: '申请人',
    modelKey: 'createUserName'
};
// 备注
const remark = {
    ...commonItems.textarea,
    name: '备注',
    modelKey: 'remark',
    elInputAttrs: {
        maxlength: 255
    }
};
// 备货来源
const requestFrom = {
    ...commonItems.select,
    name: '备货来源',
    modelKey: 'requestFrom',
    elOptions: [
        { label: '客户需求', value: '客户需求' },
        { label: '关联公司需求', value: '关联公司需求' },
        { label: '自主发起', value: '自主发起' }
    ]
};
const expectedArrivalDate = {
    ...commonItems.date,
    name: '期望到货日期',
    modelKey: 'expectedArrivalDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd',
        'pickerOptions': {
            disabledDate(time) {
                return time.getTime() <= Date.now();
            }
        }
    }
};
export default {
    stockRequestCode,
    stockRequestName,
    requestDate,
    directionWarehouseCode,
    createUserName,
    remark,
    requestFrom,
    expectedArrivalDate
};
