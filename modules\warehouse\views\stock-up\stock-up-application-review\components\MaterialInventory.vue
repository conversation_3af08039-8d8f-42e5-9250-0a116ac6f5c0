<template>
    <el-dialog class="custom-dialog" :visible.sync="dialogVisible" v-bind="elDialogAttrs">
        <snbc-card title="产品汇总">
            <template #card-body>
                <snbc-descriptions :items="basicInfo" direction="vertical" />
            </template>
        </snbc-card>
        <snbc-card title="物料数量列表" class="margin-top-10">
            <template #card-body>
                <snbc-base-table ref="tableRef" :table-config="tableConfig" />
            </template>
        </snbc-card>
    </el-dialog>
</template>

<script>
import elAttrs from 'warehouse/common/el-attrs/index.js';
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import SnbcDescriptions from 'warehouse/components/snbc-descriptions/SnbcDescriptions.vue';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';

export default {
    name: 'MaterialInventory',
    components: {
        SnbcCard,
        SnbcDescriptions,
        SnbcBaseTable
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            elDialogAttrs: {
                ...elAttrs.elDialogAttrs,
                title: '公司物料库存',
                width: '1000px'
            },
            dialogVisible: false,
            // 基础信息
            basicInfo: [],
            // 列表配置
            tableConfig: {
                // 查询条件
                queryParams: {
                    productName: ''
                },
                // 列表查询接口
                queryApi: this.$service.warehouse.basedata.searchMaterialInfoByProduct,
                elTableColumns: [
                    {
                        label: '序号',
                        prop: 'index',
                        show: true,
                        minWidth: 50
                    },
                    {
                        label: '物料编码',
                        prop: 'materialCode',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '描述',
                        prop: 'materialDesc',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '公司仓库',
                        prop: 'subInventoryName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '数量',
                        prop: 'number',
                        show: true,
                        minWidth: 120
                    }
                ],
                // 无分页
                hasPage: false,
                // 钩子函数
                hook: {
                    tableListHook: this.tableListHook
                }
            }
        };
    },
    computed: {},
    methods: {
        // 查询详情
        async showDialog(data) {
            this.dialogVisible = true;
            // 列表查询参数
            this.tableConfig.queryParams.productName = data.productName;
            // 基础信息
            this.basicInfo = [
                { label: '产品名称', prop: 'productName', value: data.productName },
                { label: '物料总数', prop: 'total', value: data.erpNumber }
            ];
            this.$nextTick(() => {
                this.$refs.tableRef.queryList();
            });
        },
        // 隐藏弹窗
        hideDialog() {
            this.dialogVisible = false;
        },
        // 列表数据汇总
        async tableListHook(list) {
            const count = list.reduce((pre, current) => {
                return pre + current.number;
            }, 0);
            this.$set(this.basicInfo[1], 'value', count);
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .el-descriptions-item__content {
    max-width: 500px;
}
</style>
