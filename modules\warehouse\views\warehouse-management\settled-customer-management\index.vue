<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig">
                <template #table-notice>
                    <span style="color: red">
                        友情提示：<br />1、区仓客户来自平台服务客户，新增区仓入驻客户前，请先前往“服务客户管理”进行维护。<br />2、区仓客户名称来自服务客户简称，不可自行设置。<br />3、关联产品如需调整请前往“产品信息管理”页面进行维护。
                    </span>
                </template>
            </snbc-base-table>
            <snbc-base-form-dialog ref="dialogRef" :config="dialogConfig" @submit="handleSubmit" />
        </div>
    </div>
</template>

<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcBaseFormDialog from 'warehouse/components/snbc-dialog/SnbcBaseFormDialog.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import { inputRequired } from 'warehouse/common/form-rules/index.js';

const { input } = FormItems;

// 弹窗校验规则
const rules = {
    customerName: [inputRequired('区仓客户名称')],
    customerCode: [
        inputRequired('区仓客户编码'),
        {
            pattern: /^[A-Z0-9]{2,5}$/,
            message: '请填写包含大写字母或数字的二至五位编码',
            trigger: 'blur'
        }
    ]
};

// 查询参数
const queryParams = {
    customerName: '',
    customerCode: ''
};

// 查询区域配置项
const queryConfigItems = [
    {
        ...input,
        name: '区仓客户名称',
        modelKey: 'customerName'
    },
    {
        ...input,
        name: '区仓客户编码',
        modelKey: 'customerCode'
    }
];

export default {
    name: 'SettledCustomerManagement',
    components: {
        SnbcBaseTable,
        SnbcBaseFormDialog
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        const {
            queryCustomerPage: listApi,
            addCustomer: addApi,
            editCustomer: editApi
        } = this.$service.warehouse.warehouseBaseInformation;
        return {
            listApi,
            addApi,
            editApi,
            tableConfig: {
                queryParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: listApi,
                elTableColumns: [
                    {
                        label: '序号',
                        prop: 'index',
                        show: true,
                        elTableColumnAttrs: {
                            width: '60'
                        }
                    },
                    {
                        label: '区仓客户名称',
                        prop: 'customerName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '区仓客户编码',
                        prop: 'customerCode',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '关联产品数量',
                        prop: 'productTotal',
                        show: true,
                        minWidth: 120,
                        renderMode: 'button',
                        elButtonAttrs: {
                            type: 'text'
                        },
                        handleClick: this.handleClickCell
                    }
                ],
                operations: [
                    {
                        name: '编辑',
                        type: 'primary',
                        handleClick: this.handleEdit
                    }
                ],
                headerButtons: [
                    {
                        name: '新增',
                        type: 'primary',
                        elButtonAttrs: {
                            icon: 'el-icon-plus'
                        },
                        handleClick: this.handleAdd
                    }
                ]
            },
            mode: 'add'
        };
    },
    computed: {
        dialogConfig() {
            return {
                rules,
                items: [
                    {
                        name: '区仓客户名称',
                        modelKey: 'customerName',
                        component: 'SnbcFormServeClientsSelect'
                    },
                    {
                        ...input,
                        name: '区仓客户编码',
                        modelKey: 'customerCode',
                        elInputAttrs: {
                            disabled: this.mode !== 'add'
                        }
                    }
                ]
            };
        }
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.handleQuery();
    },
    methods: {
        // 编辑操作
        handleEdit(row) {
            const data = this.$tools.cloneDeep(row);
            this.mode = 'edit';
            this.$refs.dialogRef.baseEditDialog(data, '区仓客户修改', 'edit');
        },
        // 新增操作
        handleAdd() {
            const data = { customerName: '' };
            this.mode = 'add';
            this.$refs.dialogRef.baseAddDialog(data, '区仓客户入驻');
        },
        // adding or editting
        async handleSubmit({ mode, form }) {
            await this.$tools.confirm('确认提交？');
            const params = { ...form };
            let submitApi = this.addApi;
            if (mode === 'edit') {
                params.id = form.id;
                submitApi = this.editApi;
            }
            try {
                const res = await submitApi(params);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.$refs.dialogRef.hideDialog();
                this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // navigate to product-martierial detail
        handleClickCell(row) {
            this.$router.push({
                path: '/app/warehouse/product-info',
                query: {
                    customerCode: row.customerCode
                }
            });
        },
        queryList() {
            this.$refs.tableRef.queryList();
        }
    }
};
</script>

<style lang="scss" scoped></style>
