<template>
    <div class="view">
        <!-- 公司发货任务列表 -->
        <div class="content query-label-line2">
            <snbc-base-table ref="tableRef" :table-config="tableConfig" />
        </div>
    </div>
</template>

<script>
import Vue from 'vue';
import functions from 'frame/mixins/functions.js';
import elAttrs from 'warehouse/common/el-attrs/index.js';
import objectFactory from 'warehouse/common/object-factory/index.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import commonItems from 'warehouse/common/form-items/common-items.js';
import warehouseItems from 'warehouse/common/form-items/warehouse-items.js';
import pickerOptions from 'warehouse/common/picker-options/index.js';

const { cloneDeep } = Vue.prototype.$tools;

// 分页参数
const pageParams = objectFactory('pageParams');
// 查询参数
const queryParams = {
    warehouseCode: '',
    requestDate: []
};

// 支持内存缓存的页面参数
const allParams = {
    queryParams: cloneDeep(queryParams),
    // 分页参数
    pageParams: cloneDeep(pageParams)
};

// 统计周期
const requestDate = {
    ...commonItems.dateRange,
    name: '统计周期',
    modelKey: 'requestDate',
    elDatePickerAttrs: {
        'unlink-panels': true,
        'value-format': 'yyyy-MM-dd',
        'picker-options': {
            shortcuts: [
                pickerOptions.lastMonthOption,
                pickerOptions.lastQuarterOption,
                pickerOptions.firstHalfYearOption,
                pickerOptions.secondHalfYearOption,
                pickerOptions.currentYearOption,
                pickerOptions.lastYearOption
            ]
        }
    }
};

// 查询区域配置项
const queryConfigItems = [warehouseItems.warehouseSelect, requestDate];

export default {
    name: 'WarehouseHistoryReport',
    components: {
        SnbcBaseTable
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            tableConfig: {
                queryParams: allParams.queryParams,
                pageParams: allParams.pageParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: this.$service.warehouse.board.getDailyStatistics,
                // 导出操作
                headerButtons: [
                    {
                        ...elAttrs.exportBtnAttrs,
                        permissionCode: 'WAREHOUSE_HISTORY_REPORT_EXPORT',
                        handleClick: this.handleExport
                    }
                ],
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 60 },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 140
                    },
                    {
                        label: '日期',
                        prop: 'statisticsDate',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '在库总数',
                        prop: 'totalStock',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '新机在库',
                        prop: 'newStock',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '撤机在库',
                        prop: 'removedStock',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '其它在库',
                        prop: 'otherStock',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '干线入库在途',
                        prop: 'inboundTransit',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '调拨在途',
                        prop: 'allocationTransit',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '使用面积',
                        prop: 'assetOccupyArea',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '面积使用率',
                        prop: 'areaUtilization',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '平均库龄',
                        prop: 'ageAverage',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '入库数量',
                        prop: 'inboundQuantity',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '出库数量',
                        prop: 'outboundQuantity',
                        show: true,
                        minWidth: 120
                    }
                ],
                hooks: {
                    tableListHook: this.tableListHook
                }
            }
        };
    },
    beforeCreate() {
        if (!this.$store.getters.toggleView) {
            allParams.queryParams = cloneDeep(queryParams);
            allParams.pageParams = cloneDeep(pageParams);
        }
    },
    created() {
        // 非tag切换进入，重置页面参数至默认状态
        if (!this.$store.getters.toggleView) {
            this.$tools.setQueryParams.call(this);
        }
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.handleQuery();
    },
    methods: {
        // 列表查询操作
        handleQuery() {
            this.$refs.tableRef.queryList();
        },
        // 列表数据处理
        tableListHook(list) {
            list.map((item) => {
                Object.assign(item, item.baseDomain || {}, item.costDomain || {}, item.businessDomain || {});
                return item;
            });
        },
        // 导出操作
        async handleExport() {
            const { exportDailyStatistics } = this.$service.warehouse.board;
            const stream = await exportDailyStatistics(this.tableConfig.queryParams);
            this.$tools.downloadExprotFile(stream, '区仓历史信息', 'xlsx').catch((e) => {
                this.$tools.message.err(e || '导出失败');
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
