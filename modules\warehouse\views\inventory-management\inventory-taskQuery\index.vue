<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig" />
        </div>
    </div>
</template>

<script>
import Vue from 'vue';
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import FormItems from 'warehouse/common/form-items/inventory-items';

const { startTime, endTime, taskState, warehouseCode, taskCode } = FormItems;
const { setDatePickerDisabledValue } = Vue.prototype.$tools;

// 查询参数
const queryParams = {
    warehouseCode: '',
    startTime: '',
    endTime: '',
    taskState: '',
    planCode: '',
    taskCode: ''
};

// 查询区域配置项
const queryConfigItems = [
    taskCode,
    taskState,
    warehouseCode,
    setDatePickerDisabledValue(startTime, queryParams, 'endTime', 'start'),
    setDatePickerDisabledValue(endTime, queryParams, 'startTime', 'end')
];

export default {
    name: 'WarehouseInventoryTaskQuery',
    components: {
        SnbcBaseTable
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        const { getInventoryTaskList: listApi } =
            this.$service.warehouse.inventoryManagement;
        return {
            listApi,
            tableConfig: {
                queryParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: listApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '任务单号',
                        prop: 'taskCode',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '开始时间',
                        prop: 'startInventoryTime',
                        show: true,
                        minWidth: 140
                    },
                    {
                        label: '最后盘点时间',
                        prop: 'finalInventoryTime',
                        show: true,
                        minWidth: 140
                    },
                    {
                        label: '盘点状态',
                        prop: 'taskStateName',
                        renderMode: 'tag',
                        elTagAttrsFn: (item) => {
                            if (item.taskState === 'finished') {
                                return {
                                    type: 'success'
                                };
                            }
                        },
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '资产总数量',
                        prop: 'assetNumber',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '已完成数',
                        prop: 'finishNumber',
                        show: true,
                        minWidth: 80
                    },
                    {
                        label: '异常数量',
                        prop: 'exceptNumber',
                        show: true,
                        minWidth: 80
                    }
                ],
                operations: [
                    {
                        name: '详情',
                        type: 'primary',
                        handleClick: this.handleShowDetail,
                        handleShow: (row) => row.taskStateName === '已完成'
                    }
                ]
            }
        };
    },
    computed: {},
    mounted() {
        queryParams.planCode = this.$route.query.planCode;
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.handleQuery();
    },
    methods: {
        // 详情
        async handleShowDetail(row) {
            this.$router.push({
                path: '/app/inventory/inventory-result',
                query: { taskCode: row.taskCode }
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
