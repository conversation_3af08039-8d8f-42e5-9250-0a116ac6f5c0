import Cookies from 'js-cookie';
import VueCookies from 'vue-cookies';
const TokenKey = 'token';
// 引入BASE64解码
const Base64 = require('js-base64').Base64;

export function getToken() {
	let token = '';
	if(VueCookies.get(TokenKey)){
		token = Base64.decode(VueCookies.get(TokenKey))
	}
    return token;
}

export function setToken(token) {
    return Cookies.set(Token<PERSON>ey, token, { expires: 7 });
}

export function removeToken() {
    return Cookies.remove(TokenKey);
}
