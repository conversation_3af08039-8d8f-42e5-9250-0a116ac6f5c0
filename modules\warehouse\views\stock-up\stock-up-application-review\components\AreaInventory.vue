<template>
    <el-dialog title="区仓库存" class="custom-dialog" :visible.sync="dialogVisible" v-bind="elDialogAttrs">
        <snbc-base-table ref="inventoryTableRef" :table-config="tableConfig">
            <template #table-info-top>
                <el-alert
                    title="在库数量：该产品在本区仓在库数量（不含被占用设备）。在库超30天数量：该产品在本区仓在库设备中（含被占用设备）库龄超过30天的数量。如果“在库超30天数量”大于“在库数量”为正常现象，请先完成历史调拨任务或联系项目组解除占用即可。"
                    type="error"
                    :closable="false"
                />
            </template>
        </snbc-base-table>
    </el-dialog>
</template>
<script>
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import ElAttrs from 'warehouse/common/el-attrs/index.js';

const { input, directionWarehouseCode } = FormItems;
const { elDialogAttrs } = ElAttrs;
// 产品名称
const productName = {
    ...input,
    name: '产品名称',
    modelKey: 'productName',
    elInputAttrs: {
        disabled: true
    }
};
// 距离区间
const distance = {
    name: '距离',
    modelKey: 'mileageNumber',
    modelKeyList: ['mileageMin', 'mileageMax'],
    placeholder: ['请输入最小距离', '请输入最大距离'],
    component: 'SnbcFormDistanceInput'
};
// 区仓编码
const warehouseCode = {
    ...directionWarehouseCode,
    name: '区仓名称',
    modelKey: 'warehouseCode',
    queryEnable: true
};

// 页面Table配置
const commonTableConfig = {
    queryParams: {
        productName: '',
        mileageMin: undefined,
        mileageMax: undefined,
        warehouseCode: ''
    },
    queryConfig: {
        items: [productName, distance, warehouseCode],
        elFormAttrs: {
            'label-width': '70px'
        }
    },
    elTableColumns: [
        { label: '序号', prop: 'index', show: true, minWidth: 50 },
        { label: '区仓名称', prop: 'warehouseName', show: true, minWidth: 120 },
        {
            label: '在库数量',
            prop: 'occupiedNumber',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                sortable: true
            }
        },
        {
            label: '在库超30天数量',
            prop: 'thirtyDaysCount',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                sortable: true
            }
        },
        {
            label: '距离/km',
            prop: 'mileageNumber',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                sortable: true
            }
        },
        { label: '所在城市', prop: 'city', show: true, minWidth: 120 }
    ],

    selectionAble: true
};
export default {
    name: 'AreaInventory',
    components: {
        SnbcBaseTable
    },
    data() {
        const hooks = {
            queryParamsHook: this.queryParamsHook
        };
        return {
            elDialogAttrs: {
                ...elDialogAttrs,
                width: '1200px'
            },
            dialogVisible: false,
            tableConfig: {
                ...this.$tools.cloneDeep(commonTableConfig),
                queryApi: this.$service.warehouse.stockUp.getNationalInventoryList,
                headerButtons: [
                    {
                        name: '选择仓库',
                        needSelections: true,
                        type: 'primary',
                        handleClick: this.handleSelectWarehouse,
                        // 添加指令，防止快速连续点击
                        vPreventReClick: true
                    }
                ],
                hooks,
                selectable: this.selectable,
                // 无分页
                hasPage: false
            },
            // 原选择
            originSelectionList: [],
            // 基本信息
            info: {
                customerName: '',
                customerCode: '',
                productName: ''
            }
        };
    },
    methods: {
        openDialog(row, code) {
            // 获取当前产品名称、客户名称、客户编码信息
            Object.keys(this.info).forEach((key) => {
                this.info[key] = row[key];
            });
            // 赋值原已经选择的信息
            this.originSelectionList = row.sendOutGoodsList || [];
            // 产品名称回显
            this.tableConfig.queryParams = {
                ...this.$tools.cloneDeep(commonTableConfig.queryParams),
                productName: row.productName
            };
            // 目标区仓
            this.directionWarehouseCode = code;
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.$refs.inventoryTableRef.list = [];
                this.$refs.inventoryTableRef.handleQuery();
            });
        },
        hideDialog() {
            this.dialogVisible = false;
        },
        // 列表查询参数hook方法
        queryParamsHook(params) {
            this.tableConfig.queryParams.productName = this.info.productName;
            params.productName = this.info.productName;
            params.destinationBin = this.directionWarehouseCode;
        },
        // 选择区仓
        handleSelectWarehouse() {
            const { selections } = this.$refs.inventoryTableRef;
            const list = selections.map((item) => {
                return {
                    fromWarehouseCode: item.warehouseCode,
                    fromWarehouseName: item.warehouseName,
                    occupiedNumber: item.occupiedNumber,
                    materialCode: '无需指定',
                    warehouseNameAndNumber: `${item.warehouseName}:${item.occupiedNumber}`,
                    orderType: 'transfer',
                    type: '调拨订单',
                    ...this.info
                };
            });
            this.$emit('select-warehouse', list);
            this.dialogVisible = false;
        },
        // 复选框是否可选择
        selectable(row) {
            const repeatList = this.originSelectionList.filter((item) => item.fromWarehouseCode === row.warehouseCode);
            if (repeatList.length !== 0) {
                return false;
            }
            return true;
        }
    }
};
</script>
