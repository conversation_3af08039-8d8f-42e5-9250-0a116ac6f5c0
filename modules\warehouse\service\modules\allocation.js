/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        allocation: {
            /**
             * 调拨任务管理-查询列表数据
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            queryAllocateTask(data) {
                if (data.createDateRange && data.createDateRange.length === 2) {
                    data.createTimeBegin = data.createDateRange[0];
                    data.createTimeEnd = data.createDateRange[1];
                }
                if (
                    data.expectedDateRange &&
                    data.expectedDateRange.length === 2
                ) {
                    data.expectedDateBegin = data.expectedDateRange[0];
                    data.expectedDateEnd = data.expectedDateRange[1];
                }
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/allocate/query_allocate_task',
                    method: 'post',
                    data
                });
            },

            /**
             * 新增调拨任务
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            addAllocateTask(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/allocate/add_allocate_task',
                    method: 'post',
                    data
                });
            },

            /**
             * 调拨任务删除
             * @param {String} allocateTaskCode 参数 Code
             * @returns {Promise} http请求
             */
            deleteAllocateTask(allocateTaskCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate/delete_allocate_task`,
                    method: 'get',
                    params: {
                        allocateTaskCode
                    }
                });
            },

            /**
             * 调拨任务编辑
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            editAllocateTask(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate/edit_allocate_task`,
                    method: 'post',
                    data
                });
            },

            /**
             * 调拨任务基础信息
             * @param {String} allocateTaskCode 参数 Code
             * @returns {Promise} http请求
             */
            getAllocateTaskBaseInfo(allocateTaskCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate/get_allocate_task_base_info`,
                    method: 'get',
                    params: {
                        allocateTaskCode
                    }
                });
            },

            /**
             * 调拨任务下发
             * @param {String} allocateTaskCode 参数 Code
             * @returns {Promise} http请求
             */
            issueAllocateTask(allocateTaskCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate/issue_allocate_task`,
                    method: 'get',
                    params: {
                        allocateTaskCode
                    }
                });
            },

            /**
             * 调拨任务手动确认完成
             * @param {Object} data 参数 Code
             * @returns {Promise} http请求
             */
            confirmCompleteOutboundAndInbound(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate/force_complete_outbound_and_inbound`,
                    method: 'post',
                    data
                });
            },

            /**
             * 调拨任务审核
             * @param {String} allocateTaskCode 参数 Code
             * @returns {Promise} http请求
             */
            auditPassAllocateTask(allocateTaskCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate/audit_pass_allocate_task`,
                    method: 'get',
                    params: {
                        allocateTaskCode
                    }
                });
            },

            /**
             * 调拨任务审核拒绝
             * @param {Object} data 参数 data
             * @returns {Promise} http请求
             */
            auditRejectAllocateTask(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate/audit_reject_allocate_task`,
                    method: 'post',
                    data
                });
            },

            /**
             * 查询非指定调拨任务任务明细（含申请明细）
             * @param {String} allocateTaskCode 参数 Code
             * @param {String} warehouseCode 参数 Code
             * @returns {Promise} http请求
             */
            queryAllocateDetailNotSpecified(allocateTaskCode, warehouseCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate_detail/query_allocate_detail_not_specified`,
                    method: 'get',
                    params: {
                        allocateTaskCode,
                        warehouseCode: warehouseCode ?? ''
                    }
                });
            },

            /**
             * 查询指定调拨任务任务明细
             * @param {String} allocateTaskCode 参数 Code
             * @returns {Promise} http请求
             */
            querySpecifyAllocateDetailEditPage(allocateTaskCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate_detail/query_specify_allocate_detail_edit_page`,
                    method: 'get',
                    params: {
                        allocateTaskCode
                    }
                });
            },

            // 通过承运物流商获取联系人和联系电话

            /**
             * 获取调拨任务明细模板路径
             * @param {String} allocateTaskCode 参数 Code
             * @returns {Promise} http请求
             */
            getAllocateDetailTemplatePath() {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate_detail/get_allocate_detail_template_path`,
                    method: 'get'
                });
            },

            // 调拨明细导入
            uploadAllocateDetail(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate_detail/upload_allocate_detail`,
                    method: 'post',
                    data
                });
            },

            /**
             * 查询调拨任务明细列表
             * @param {String} allocateTaskCode 参数 Code
             * @returns {Promise} http请求
             */
            queryAllocateDetail(allocateTaskCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate_detail/query_allocate_detail`,
                    method: 'get',
                    params: {
                        allocateTaskCode
                    }
                });
            },

            /**
             * 确认实际物流信息
             * @param {String} allocateTaskCode 参数 Code
             * @returns {Promise} http请求
             */
            confirmActualLogisticsInfo(allocateTaskCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate/confirm_actual_logistics_info`,
                    method: 'get',
                    params: {
                        allocateTaskCode
                    }
                });
            },

            /**
             * 物流信息驳回的接口
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            rejectActualLogisticsInfo(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate/reject_actual_logistics_info`,
                    method: 'post',
                    data
                });
            },

            /**
             * 录入实际物流结果
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            inputActualLogisticsResult(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate_logistics/input_actual_logistics_result`,
                    method: 'post',
                    data
                });
            },

            /**
             * 查询实际物流结果
             * @param {String} allocateTaskCode 参数 Code
             * @returns {Promise} http请求
             */
            queryActualLogisticsResult(allocateTaskCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate_logistics/query_actual_logistics_result`,
                    method: 'get',
                    params: {
                        allocateTaskCode
                    }
                });
            },

            /**
             * 录入物流寻源结果
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            inputLogisticsSourceResult(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate_logistics/input_logistics_source_result`,
                    method: 'post',
                    data
                });
            },

            /**
             * 查询物流寻源信息和实际物流信息
             * @param {String} allocateTaskCode 参数 Code
             * @returns {Promise} http请求
             */
            queryAllLogisticsInfo(allocateTaskCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate_logistics/query_all_logistics_info`,
                    method: 'get',
                    params: {
                        allocateTaskCode
                    }
                });
            },

            /**
             * 查询物流寻源结果
             * @param {String} allocateTaskCode 参数 Code
             * @returns {Promise} http请求
             */
            queryLogisticsSourceResult(allocateTaskCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate_logistics/query_logistics_source_result`,
                    method: 'get',
                    params: {
                        allocateTaskCode
                    }
                });
            },

            /**
             * 查询调拨任务的操作记录
             * @param {String} allocateTaskCode 参数 Code
             * @returns {Promise} http请求
             */
            queryAllocateTaskOperationLog(allocateTaskCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate/query_allocate_task_operation_log`,
                    method: 'get',
                    params: {
                        allocateTaskCode
                    }
                });
            },

            /**
             * 调拨任务终止
             * @param {String} allocateTaskCode 参数 Code
             * @returns {Promise} http请求
             */
            stopAllocateTask(allocateTaskCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate/stop_allocate_task`,
                    method: 'get',
                    params: {
                        allocateTaskCode
                    }
                });
            },

            /**
             * 获取里程
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            getMileage(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/mileage/asset/list`,
                    method: 'post',
                    data
                });
            },

            /**
             * 通过code获取供应商信息
             * @param {String} supplierCode 参数 Code
             * @returns {Promise} http请求
             */
            getSupplierInfoByCode(supplierCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/basedata/supplier_info/get_supplierInfo_by_code`,
                    method: 'get',
                    params: {
                        supplierCode
                    }
                });
            },

            /**
             * 获取供应商列表
             * @returns {Promise} http请求
             */
            getSupplierForm() {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/basedata/supplier_info/get_supplier_form`,
                    method: 'get'
                });
            },

            /**
             * 获取供应商列表
             * @returns {Promise} http请求
             */
            queryAllocateTaskStateList() {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/allocate/query_allocate_task_state_list`,
                    method: 'get'
                });
            }
        }
    };

    return service;
};
