<template>
    <div>
        <snbc-base-table ref="tableRef" :table-config="tableConfig">
            <template #tabs>
                <snbc-table-tabs :tabs-config="tabsConfig" />
            </template>
        </snbc-base-table>
        <show-photo-dialog ref="showPhotoDialogRef" @fresh="handleQuery" />
    </div>
</template>

<script>
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcTableTabs from 'warehouse/components/snbc-table/SnbcTableTabs.vue';
import ShowPhotoDialog from './ShowPhotoDialog.vue';

export default {
    name: 'CargoDamage',
    components: {
        SnbcBaseTable,
        SnbcTableTabs,
        ShowPhotoDialog
    },
    props: {
        tabsConfig: {
            type: Object,
            default() {
                return {};
            }
        },
        queryConfig: {
            type: Object,
            default() {
                return {};
            }
        }
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            tableConfig: {
                queryApi: this.$service.warehouse.inventoryManagement.getHandledData,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '物料编码',
                        prop: 'materialCode',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '产品序列号',
                        prop: 'assetCode',
                        show: true,
                        minWidth: 180
                    },
                    {
                        label: '客户名称',
                        prop: 'customerName',
                        show: true,
                        minWidth: 140
                    },
                    {
                        label: '区仓',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 120
                    },

                    {
                        label: '关联单号',
                        prop: 'damageCode',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '处理时间',
                        prop: 'updateTime',
                        show: true,
                        minWidth: 160
                    }
                ],
                operations: [
                    {
                        name: '展示图片',
                        type: 'warning',
                        handleShow: (row) => row.damageImageUrlList,
                        handleClick: this.showPhoto,
                        width: 100
                    }
                ],
                hooks: {
                    queryParamsHook: this.queryParamsHook
                }
            }
        };
    },
    // mounted() {},
    mounted() {
        this.tableConfig = {
            ...this.queryConfig,
            ...this.tableConfig
        };
    },
    methods: {
        queryParamsHook(params) {
            params.planCode = this.$route.query.planCode;
            params.type = 'DAMAGE';
            this.queryConfig.queryParamsHook && this.queryConfig.queryParamsHook(params);
        },
        handleQuery() {
            this.$refs.tableRef.queryList();
        },
        showPhoto(row) {
            this.$refs.showPhotoDialogRef.openDialog(row.damageImageUrlList, '货损图片');
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .el-table__fixed-right {
    height: 100% !important;
}
</style>
