<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig" />
            <snbc-base-form-dialog
                ref="dialogRef"
                :config="dialogConfig"
                @submit="handleSubmit"
            />
        </div>
    </div>
</template>

<script>
import Vue from 'vue';
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcBaseFormDialog from 'warehouse/components/snbc-dialog/SnbcBaseFormDialog.vue';
import FormItems from 'warehouse/common/form-items/inventory-items';
import FormRules from 'warehouse/common/form-rules';

const { setDatePickerDisabledValue } = Vue.prototype.$tools;

const {
    planCode,
    startTime,
    endTime,
    planDesc,
    planState,
    productList,
    requiredFinishTime,
    warehouseCodeList,
    planName
} = FormItems;

const { selectRequired, inputRequired, maxLength } = FormRules;

// 弹窗校验规则
const rules = {
    planName: [inputRequired('盘点计划名称'), maxLength(255)],
    planDesc: [inputRequired('盘点描述'), maxLength(255)],
    warehouseCodeList: [selectRequired('区仓名称')],
    productList: [selectRequired('产品名称')],
    requiredFinishTime: [selectRequired('要求完成时间')]
};

// 查询参数
const queryParams = {
    planCode: '',
    startTime: '',
    endTime: '',
    planDesc: '',
    planState: '',
    planName: ''
};
// 查询区域配置项
const queryConfigItems = [
    planCode,
    planName,
    planDesc,
    planState,
    setDatePickerDisabledValue(startTime, queryParams, 'endTime', 'start'),
    setDatePickerDisabledValue(endTime, queryParams, 'startTime', 'end')
];

// 弹窗配置
const dialogConfigItems = [
    planName,
    planDesc,
    productList,
    warehouseCodeList,
    requiredFinishTime
];

export default {
    name: 'WarehouseInventoryInformation',
    components: {
        SnbcBaseTable,
        SnbcBaseFormDialog
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        const {
            getInventoryManageList: listApi,
            startAndCancleInventoryManagement: startApi,
            addInventoryManagement: addApi,
            editInventoryManagement: editApi
        } = this.$service.warehouse.inventoryManagement;
        return {
            listApi,
            addApi,
            editApi,
            startApi,
            tableConfig: {
                queryParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: listApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '盘点计划单号',
                        prop: 'planCode',
                        renderMode: 'tag',
                        elTagAttrsFn: (item) => {
                            if (item.timeoutFlag) {
                                return {
                                    type: 'danger'
                                };
                            }
                            return {
                                type: 'success'
                            };
                        },
                        show: true,
                        minWidth: 170
                    },
                    {
                        label: '盘点计划名称',
                        prop: 'planName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '盘点计划描述',
                        prop: 'planDesc',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '任务下发时间',
                        prop: 'sendTime',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '要求完成时间',
                        prop: 'requiredFinishTime',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '盘点状态',
                        prop: 'planStateName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '任务总数',
                        prop: 'taskNumber',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '已完成数',
                        prop: 'finishTaskNumber',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '异常数量',
                        prop: 'exceptNumber',
                        show: true,
                        minWidth: 100
                    }
                ],
                operations: [
                    {
                        name: '下发',
                        type: 'success',
                        handleClick: this.handleStart,
                        handleShow: (row) =>
                            row.planState === 'unstart' ||
                            row.planState === 'unsend'
                    },
                    {
                        name: '取消',
                        type: 'danger',
                        handleClick: this.handleCancel,
                        handleShow: (row) =>
                            row.planState === 'inprogress' ||
                            row.planState === 'unstart' ||
                            row.planState === 'unsend'
                    },
                    {
                        name: '详情',
                        type: 'primary',
                        handleClick: this.handleShowDetail
                    },
                    {
                        name: '编辑',
                        type: 'warning',
                        handleClick: this.handleEdit,
                        handleShow: (row) =>
                            row.planState === 'unstart' ||
                            row.planState === 'unsend'
                    },
                    {
                        name: '结果处理',
                        type: 'danger',
                        handleClick: this.handleJump,
                        handleShow: (row) => row.planState === 'finished'
                    }
                ],
                headerButtons: [
                    {
                        name: '新增',
                        type: 'primary',
                        handleClick: this.handleAdd
                    }
                ]
            },
            // 弹窗配置
            dialogConfig: {
                rules,
                items: dialogConfigItems
            }
        };
    },
    computed: {},
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.handleQuery();
        this.getWareHouseInfo();
        this.getProductName();
    },
    methods: {
        // 编辑操作
        async handleEdit(row) {
            const data =
                await this.$service.warehouse.inventoryManagement.getInventoryDetailInfo(
                    row.id
                );
            if (data.code !== '000000') {
                this.$tools.message.err(data.message || '系统异常');
                return;
            }
            if (data.result.productList.length === 0) {
                try {
                    const { code, message, result } =
                        await this.$service.warehouse.inventoryManagement.getProductList();
                    if (code !== '000000') {
                        this.$tools.message.err(message || '系统异常');
                        return;
                    }
                    const array = [];
                    for (let i = 0; i < result.length; i++) {
                        array.push(result[i].customerCode);
                    }
                    data.result.productList = array;
                } catch (error) {
                    console.error(error);
                    this.$tools.message.err('系统异常');
                }
            }
            if (data.result.warehouseCodeList.length === 0) {
                try {
                    const { code, message, result } =
                        await this.$service.warehouse.basicInfoManagement.getWarehouseInfo();
                    if (code !== '000000') {
                        this.$tools.message.err(message || '系统异常');
                        return;
                    }
                    const array = [];
                    for (let i = 0; i < result.length; i++) {
                        array.push(result[i].warehouseCode);
                    }
                    data.result.warehouseCodeList = array;
                } catch (error) {
                    console.error(error);
                    this.$tools.message.err('系统异常');
                }
            }
            this.$refs.dialogRef.baseEditDialog(data.result, '修改盘点计划');
        },
        // 取消
        async handleCancel(item) {
            await this.$tools.confirm('确认取消？');
            const params = {
                planCode: item.planCode,
                planState: 'canceled'
            };
            try {
                const res =
                    await this.$service.warehouse.inventoryManagement.startAndCancleInventoryManagement(
                        params
                    );
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('取消成功');
                await this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 详情
        async handleShowDetail(row) {
            this.$router.push({
                path: '/app/inventory/inventory-taskQuery',
                query: { planCode: row.planCode }
            });
        },
        // 跳转到结果处理页面
        async handleJump(row) {
            this.$router.push({
                path: '/app/inventory/inventory-deal',
                query: { planCode: row.planCode }
            });
        },
        // 开始
        async handleStart(row) {
            await this.$tools.confirm('确认下发？');
            const params = {
                planCode: row.planCode,
                planState: 'inprogress'
            };
            try {
                const res = await this.startApi(params);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('下发成功');
                await this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 新增操作
        handleAdd() {
            const data = {
                warehouseCodeList: [],
                productList: []
            };
            this.$refs.dialogRef.baseAddDialog(data, '新增盘点计划');
        },
        // 新增或编辑提交
        async handleSubmit({ mode, form }) {
            await this.$tools.confirm('确认提交？');
            const params = this.$tools.deepClone(form);
            if (
                this.dialogConfig.items[3].elOptions.length ===
                form.warehouseCodeList.length
            ) {
                params.warehouseCodeList = [];
            }
            if (
                this.dialogConfig.items[2].elOptions.length ===
                form.productList.length
            ) {
                params.productList = [];
            }
            let submitApi = this.addApi;
            if (mode === 'edit') {
                submitApi = this.editApi;
            }
            try {
                const res = await submitApi(params);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.$refs.dialogRef.hideDialog();
                await this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 初始化区仓
        async getWareHouseInfo() {
            const { code, message, result } =
                await this.$service.warehouse.basicInfoManagement.getWarehouseInfo();
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
                return;
            }
            this.dialogConfig.items[3].elOptions = result.map((item) => {
                return {
                    ...item,
                    label: item.warehouseName,
                    value: item.warehouseCode
                };
            });
        },
        // 初始化产品名称
        async getProductName() {
            const { code, message, result } =
                await this.$service.warehouse.inventoryManagement.getProductList();
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
                return;
            }
            this.dialogConfig.items[2].elOptions = result.map((item) => {
                return {
                    ...item,
                    label: item.productName,
                    value: item.productName
                };
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
