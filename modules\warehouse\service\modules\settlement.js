import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();
    const service = {
        settlement: {
            /**
             * 结算单管理-列表查询
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            selectByPage(data) {
                if (data.dateRange.length > 0) {
                    data.endDate = data.dateRange[1];
                    data.startDate = data.dateRange[0];
                }
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/settle/select_by_page`,
                    method: 'post',
                    data
                });
            },
            /**
             * 按条件获取服务商
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            detail(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/settle/detail`,
                    method: 'post',
                    data
                });
            },
            /**
             * 费用确认
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            confirmFee(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/settle/confirm_fee`,
                    method: 'post',
                    data
                });
            },
            /**
             * 新增支付记录
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            addPayment(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/settle/add_payment`,
                    method: 'post',
                    data
                });
            },
            /**
             * 对账完成
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            completeCheck(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/settle/complete_check`,
                    method: 'post',
                    data
                });
            },
            /**
             * 对账上传
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            checkUpload(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/settle/check_upload`,
                    method: 'post',
                    data
                });
            },
            /**
             * 重新生成确认单
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            recreate(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/settle/recreate`,
                    method: 'post',
                    data
                });
            }
        }
    };
    return service;
};
