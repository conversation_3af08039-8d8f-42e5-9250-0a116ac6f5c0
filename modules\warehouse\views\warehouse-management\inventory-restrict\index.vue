<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig" />
            <snbc-base-form-dialog ref="dialogRef" :config="dialogConfig" @submit="handleSubmit" />
        </div>
    </div>
</template>

<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcBaseFormDialog from 'warehouse/components/snbc-dialog/SnbcBaseFormDialog.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import FormRules from 'warehouse/common/form-rules/index.js';

const {
    warehouseName,
    warehouseSelect,
    productName,
    warehouseMultiSelect,
    productMultiSelect,
    productSelect,
    upperLimit,
    lowerLimit
} = FormItems;

const { selectRequired, inputRequired, inputRequiredOnBlur } = FormRules;

// 弹窗校验规则
const rules = {
    warehouseCodeList: [selectRequired('区仓名称')],
    productIdList: [selectRequired('产品名称')],
    warehouseName: [inputRequired('区仓名称')],
    productName: [inputRequired('产品名称')],
    upperLimit: [inputRequiredOnBlur('区仓上限')],
    lowerLimit: [inputRequiredOnBlur('区仓下限')]
};

// 查询参数
const queryParams = {
    warehouseName: '',
    productName: ''
};

// 查询区域配置项
const queryConfigItems = [warehouseSelect, productSelect];

// 编辑弹窗配置
const editDialogConfigItems = [
    {
        ...warehouseName,
        elInputAttrs: {
            disabled: true
        }
    },
    {
        ...productName,
        elInputAttrs: {
            disabled: true
        }
    },
    upperLimit,
    lowerLimit
];

// 新增弹窗配置
const addDialogConfigItems = [warehouseMultiSelect, productMultiSelect, upperLimit, lowerLimit];

export default {
    name: 'WarehouseInventoryRestrict',
    components: {
        SnbcBaseTable,
        SnbcBaseFormDialog
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        const {
            list: listApi,
            remove: removeApi,
            add: addApi,
            edit: editApi
        } = this.$service.warehouse.warehouseInventoryRestrict;
        return {
            listApi,
            addApi,
            editApi,
            removeApi,
            tableConfig: {
                queryParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: listApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '区仓编号',
                        prop: 'warehouseCode',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '库存上限',
                        prop: 'upperLimit',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '库存下限',
                        prop: 'lowerLimit',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '创建时间',
                        prop: 'createTime',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '创建者',
                        prop: 'createUserName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '修改时间',
                        prop: 'updateTime',
                        show: false,
                        minWidth: 160
                    },
                    {
                        label: '修改者',
                        prop: 'updateUserName',
                        show: false,
                        minWidth: 120
                    }
                ],
                operations: [
                    {
                        name: '编辑',
                        type: 'primary',
                        handleClick: this.handleEdit
                    }
                ],
                headerButtons: [
                    {
                        name: '新增',
                        type: 'primary',
                        handleClick: this.handleAdd
                    }
                ]
            },
            // 弹窗配置
            dialogConfig: {
                rules,
                items: editDialogConfigItems
            }
        };
    },
    computed: {},
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.handleQuery();
    },
    methods: {
        // 编辑操作
        handleEdit(row) {
            const data = this.$tools.cloneDeep(row);
            this.dialogConfig.items = editDialogConfigItems;
            this.$refs.dialogRef.baseEditDialog(data, '区仓库存上下限编辑');
        },
        // 新增操作
        handleAdd() {
            const data = {
                warehouseCodeList: [],
                productIdList: []
            };
            this.dialogConfig.items = addDialogConfigItems;
            this.$refs.dialogRef.baseAddDialog(data, '区仓库存上下限新增');
        },
        // 新增或编辑提交
        async handleSubmit({ mode, form }) {
            await this.$tools.confirm('确认提交？');
            const params = {};
            let submitApi = this.addApi;
            if (mode === 'edit') {
                params.id = form.id;
                params.warehouseCodeList = [form.warehouseCode];
                params.productIdList = [form.productId];
                params.upperLimit = form.upperLimit;
                params.lowerLimit = form.lowerLimit;
                submitApi = this.editApi;
            } else {
                params.upperLimit = form.upperLimit;
                params.lowerLimit = form.lowerLimit;
                params.warehouseCodeList = form.warehouseCodeList;
                params.productIdList = form.productIdList;
            }
            try {
                const res = await submitApi(params);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.$refs.dialogRef.hideDialog();
                await this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        }
    }
};
</script>

<style lang="scss" scoped></style>
