<template>
    <div class="view">
        <div class="content query-label-line2">
            <snbc-base-table v-show="tabsConfig.activeName === '未完成'" ref="tableRef1" :table-config="tableConfig">
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
            </snbc-base-table>
            <snbc-base-table v-show="tabsConfig.activeName === '已完成'" ref="tableRef2" :table-config="tableConfig">
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
            </snbc-base-table>
        </div>
    </div>
</template>

<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcTableTabs from 'warehouse/components/snbc-table/SnbcTableTabs.vue';
import CommonItems from 'warehouse/common/form-items/common-items.js';

const { input } = CommonItems;

const code = {
    ...input,
    name: '公司发货清单编号',
    modelKey: 'code'
};
const name = {
    ...input,
    name: '公司发货清单名称',
    modelKey: 'name'
};
const warehouseName = {
    ...input,
    name: '目标区仓',
    modelKey: 'warehouseName'
};

// 查询参数
const queryParams = {
    code: '',
    name: '',
    warehouseName: ''
};

// 查询区域配置项
const queryConfigItems = [code, name, warehouseName];

export default {
    name: 'CompanyDeliveryList',
    components: {
        SnbcBaseTable,
        SnbcTableTabs
    },
    mixins: [functions],
    data() {
        return {
            tableConfig: {
                queryParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: this.$service.warehouse.comDeliveryTask.getComDeliveryBill,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '公司发货清单编号',
                        prop: 'code',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '公司发货清单名称',
                        prop: 'name',
                        show: true,
                        minWidth: 350
                    },
                    {
                        label: '目标区仓',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '到货状态',
                        prop: 'billState',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '到货进度',
                        prop: 'progress',
                        show: true,
                        minWidth: 120,
                        renderMode: 'button',
                        elButtonAttrs: {
                            type: 'text'
                        },
                        handleClick: this.handleViewArrivalProgress
                    },
                    {
                        label: '创建时间',
                        prop: 'createTime',
                        show: true,
                        minWidth: 160
                    }
                ],
                operations: [],
                hooks: {
                    queryParamsHook: this.queryParamsHook
                }
            },
            // 标签页配置
            tabsConfig: {
                activeName: '未完成',
                tabItems: ['未完成', '已完成'],
                handleTabClick: this.handleTabClick
            }
        };
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef1.handleQuery();
    },
    methods: {
        // 列表查询参数hook方法
        queryParamsHook(params) {
            params.billState = this.tabsConfig.activeName;
        },
        // 到货进度
        handleViewArrivalProgress(row) {
            this.$router.push({
                path: '/app/company-delivery/assets-list',
                query: { deliveryListCode: row.code }
            });
        },
        // 补录操作
        handleAddRecord(row) {
            this.$tools.message.warning('开发中...');
        },
        // 标签切换
        handleTabClick(tab) {
            if (tab.name === '已完成' && !this.$refs.tableRef2.queried) {
                this.$refs.tableRef2.handleQuery();
            }
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__content {
    padding: 3px 0 0 0;
}
</style>
