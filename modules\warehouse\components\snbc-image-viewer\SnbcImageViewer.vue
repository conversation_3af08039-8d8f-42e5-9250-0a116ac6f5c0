<template>
    <el-image
        class="screen-out"
        ref="imageViewerRef"
        :src="src"
        :preview-src-list="srcList"
    >
    </el-image>
</template>
<script>
export default {
    name: 'SnbcImageViewer',
    data() {
        return {
            // 图片源
            src: '',
            // 图片预览数组
            srcList: []
        };
    },
    methods: {
        // 开启预览
        show(src, srcList) {
            this.src = src;
            this.srcList = srcList;
            this.$nextTick(() => {
                this.$refs.imageViewerRef.clickHandler();
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.screen-out {
    position: fixed;
    top: -10000px;
}
</style>
