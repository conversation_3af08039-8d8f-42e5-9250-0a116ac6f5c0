<template>
  <el-dropdown trigger="click" class="international" @command="handleSetLanguage">
    <div>
      <svg-icon class-name="international-icon" :icon-class="isChinese?'chinese':'language'"/>
    </div>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item :disabled="language==='zh'" command="zh">
        中文
      </el-dropdown-item>
      <el-dropdown-item :disabled="language==='en'" command="en">
        English
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
    name: 'LangSelect',
	data(){
		return {
			isChinese:true,
		}
	},
    computed: {
        language() {
            return this.$store.getters.language;
        }
    },
    methods: {
        handleSetLanguage(lang) {
            this.$i18n.locale = lang;
			if(lang === 'en'){
				this.isChinese = false;
			}else{
				this.isChinese = true;
			}
            this.$store.dispatch('app/setLanguage', lang);
            this.$message({
                message: this.$t('common.changeLanguage'),
                type: 'success'
            });
        }
    }
};
</script>
