<!-- 仓库指标日明细查询 -->
<template>
    <div class="view">
        <div class="content query-label-line2">
            <snbc-base-table ref="tableRef" :table-config="tableConfig" />
        </div>
    </div>
</template>

<script>
import Vue from 'vue';
import functions from 'frame/mixins/functions.js';
import elAttrs from 'warehouse/common/el-attrs/index.js';
import textRender from 'warehouse/common/text-render/index.js';
import objectFactory from 'warehouse/common/object-factory/index.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import commonItems from 'warehouse/common/form-items/common-items.js';
import warehouseItems from 'warehouse/common/form-items/warehouse-items.js';
import pickerOptions from 'warehouse/common/picker-options/index.js';

const { cloneDeep } = Vue.prototype.$tools;

// 查询条件参数
const queryParams = {
    warehouseCode: '',
    requestDate: pickerOptions.getDateRange('前一天'),
    orderRule: 'desc',
    orderMetric: 'totalStock'
};
// 分页参数
const pageParams = objectFactory('pageParams');

// 支持内存缓存的页面参数
const allParams = {
    queryParams: cloneDeep(queryParams),
    // 分页参数
    pageParams: cloneDeep(pageParams)
};

// 统计周期
const requestDate = {
    ...commonItems.dateRange,
    name: '统计周期',
    modelKey: 'requestDate',
    elDatePickerAttrs: {
        'unlink-panels': true,
        'value-format': 'yyyy-MM-dd',
        'picker-options': {
            shortcuts: [
                pickerOptions.yesterdayOption,
                pickerOptions.currentWeekOption,
                pickerOptions.lastWeekOption,
                pickerOptions.currentMonthOption,
                pickerOptions.currentQuarterOption,
                pickerOptions.currentYearOption
            ]
        }
    }
};
// 查询区域配置项
const queryConfigItems = [
    warehouseItems.warehouseSelect,
    requestDate,
    warehouseItems.orderMetricDaily,
    warehouseItems.orderRule
];
export default {
    name: 'IndicatorDaily',
    components: {
        SnbcBaseTable
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            tableConfig: {
                queryParams: allParams.queryParams,
                pageParams: allParams.pageParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: this.$service.warehouse.statistics.getDailyList,
                // 导出操作
                headerButtons: [
                    {
                        ...elAttrs.exportBtnAttrs,
                        permissionCode: 'WAREHOUSE_INDICATOR_DAILY_EXPORT',
                        handleClick: this.handleExport
                    }
                ],
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 60 },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '服务站区仓负责人',
                        prop: 'warehouseMaster',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '统计日期',
                        prop: 'statisticsDate',
                        show: true,
                        minWidth: 120,
                        render(value) {
                            return textRender.dateRender(value);
                        }
                    },
                    {
                        label: '在库总数',
                        prop: 'totalStock',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '新机在库',
                        prop: 'newStock',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '撤机在库',
                        prop: 'removedStock',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '干线在途',
                        prop: 'inboundTransit',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '调拨在途',
                        prop: 'allocationTransit',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '其他在库',
                        prop: 'otherStock',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '入库数量',
                        prop: 'inboundQuantity',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '出库数量',
                        prop: 'outboundQuantity',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '使用面积',
                        prop: 'assetOccupyArea',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '面积利用率',
                        prop: 'areaUtilization',
                        show: true,
                        minWidth: 100,
                        render(value) {
                            return textRender.unitRender(value, '%');
                        }
                    },
                    {
                        label: '平均库龄',
                        prop: 'ageAverage',
                        show: true,
                        minWidth: 100
                    }
                ],
                hooks: {
                    tableListHook: this.tableListHook
                }
            }
        };
    },
    beforeCreate() {
        if (!this.$store.getters.toggleView) {
            allParams.queryParams = cloneDeep(queryParams);
            allParams.pageParams = cloneDeep(pageParams);
        }
    },
    created() {
        // 非tag切换进入，重置页面参数至默认状态
        if (!this.$store.getters.toggleView) {
            this.$tools.setQueryParams.call(this);
        }
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.queryList();
    },
    methods: {
        // 列表数据处理
        tableListHook(list) {
            list.map((item) => {
                return item;
            });
        },
        // 导出操作
        async handleExport() {
            const { exportDailyStatistics } = this.$service.warehouse.statistics;
            const params = this.$tools.cloneDeep(this.tableConfig.queryParams);
            const stream = await exportDailyStatistics(params);
            this.$tools.downloadExprotFile(stream, '区仓指标日明细', 'xlsx').catch((e) => {
                this.$tools.message.err(e || '导出失败');
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
