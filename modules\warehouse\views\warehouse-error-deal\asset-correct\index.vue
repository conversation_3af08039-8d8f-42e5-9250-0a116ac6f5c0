<template>
    <div class="view">
        <div class="content">
            <snbc-base-table
                v-show="tabsConfig.activeName === '待处理'"
                ref="tableRef1"
                :table-config="tableConfig1"
            >
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
            </snbc-base-table>
            <snbc-base-table
                v-show="tabsConfig.activeName === '查询'"
                ref="tableRef2"
                :table-config="tableConfig2"
            >
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
            </snbc-base-table>
            <asset-correct-form ref="assetCorrectRef" />
            <asset-correct-details ref="assetCorrectDetailsRef" />
        </div>
    </div>
</template>

<script>
import AssetCorrectDetails from './components/details.vue';
import AssetCorrectForm from './components/AssetCorrectForm.vue';
import functions from 'frame/mixins/functions.js';
import SnbcTableTabs from 'warehouse/components/snbc-table/SnbcTableTabs.vue';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import commonItems from 'warehouse/common/form-items/common-items.js';

const applyCode = {
    ...commonItems.input,
    name: '非法数据修正编号',
    modelKey: 'applyCode'
};
const applyName = {
    ...commonItems.input,
    name: '非法数据修正名称',
    modelKey: 'applyName'
};
const assetCode = {
    ...commonItems.input,
    name: '产品序列号',
    modelKey: 'assetCode'
};
const productName = {
    ...commonItems.input,
    name: '产品名称',
    modelKey: 'productName'
};
const associatedTaskType = {
    ...commonItems.select,
    name: '关联任务类型',
    modelKey: 'associatedTaskType',
    elOptions: [
        { label: '公司发货 ', value: '公司发货' },
        { label: '盘点提案处理', value: '盘点提案处理' },
        { label: '调拨', value: '调拨' },
        { label: '日常工作', value: '日常工作' }
    ]
};
const associatedTaskCode = {
    ...commonItems.input,
    name: '关联任务编号',
    modelKey: 'associatedTaskCode'
};
const warehouseCode = {
    component: 'SnbcFormWarehouseSelect',
    name: '区仓',
    modelKey: 'warehouseCode'
};
export default {
    name: 'DamageTask',
    components: {
        AssetCorrectDetails,
        SnbcBaseTable,
        SnbcTableTabs,
        AssetCorrectForm
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 查询参数
            queryParams: {
                applyCode: '',
                applyName: '',
                warehouseCode: '',
                assetCode: '',
                productName: '',
                associatedTaskType: '',
                content: '',
                associatedTaskCode: ''
            },

            // 查询区域配置项
            queryConfig: {
                items: [
                    applyName,
                    applyCode,
                    assetCode,
                    warehouseCode,
                    productName,
                    associatedTaskType,
                    associatedTaskCode
                ]
            },
            elTableColumns: [
                { label: '序号', prop: 'index', show: true, minWidth: 50 },
                {
                    label: '非法数据修正编号',
                    prop: 'applyCode',
                    show: true,
                    minWidth: 180,
                    renderMode: 'button',
                    elButtonAttrs: {
                        type: 'text'
                    },
                    handleClick: this.handleView
                },
                {
                    label: '非法数据修正名称',
                    prop: 'applyName',
                    show: true,
                    minWidth: 120
                },
                {
                    label: '产品名称',
                    prop: 'productName',
                    show: true,
                    minWidth: 120
                },
                {
                    label: '产品序列号',
                    prop: 'assetCode',
                    show: true,
                    minWidth: 120
                },
                {
                    label: '区仓编号',
                    prop: 'warehouseCode',
                    show: true,
                    minWidth: 120
                },
                {
                    label: '区仓名称',
                    prop: 'warehouseName',
                    show: true,
                    minWidth: 80
                },
                {
                    label: '关联任务类型',
                    prop: 'associatedTaskType',
                    show: true,
                    minWidth: 160
                },
                {
                    label: '关联任务编号',
                    prop: 'associatedTaskCode',
                    show: true,
                    minWidth: 160
                },
                {
                    label: '任务状态',
                    prop: 'assetCorrectState',
                    show: true,
                    minWidth: 120
                },
                {
                    label: '创建时间',
                    prop: 'createTime',
                    show: true,
                    minWidth: 120
                }
            ],
            // 标签页配置
            tabsConfig: {
                activeName: '待处理',
                tabItems: ['待处理', '查询'],
                handleTabClick: this.handleTabClick
            },
            // 待提交Table配置
            tableConfig1: {
                queryParams: {},
                // 查询区域配置项
                queryConfig: {},
                elTableColumns: [],
                queryApi: this.$service.warehouse.assetCorrect.list,
                operations: [
                    {
                        name: '编辑',
                        type: 'warning',
                        handleClick: this.handleEdit,
                        handleShow: (row) =>
                            ['待提交', '驳回'].includes(row.assetCorrectState)
                    },
                    {
                        name: '提交',
                        handleClick: this.handleSubmit,
                        handleShow: (row) =>
                            ['待提交', '驳回'].includes(row.assetCorrectState)
                    },
                    {
                        name: '审核',
                        type: 'primary',
                        handleClick: this.handleAudit,
                        handleShow: (row) =>
                            ['审核中'].includes(row.assetCorrectState)
                    }
                ]
            },
            // 已提交Table配置
            tableConfig2: {
                queryParams: {},
                // 查询区域配置项
                queryConfig: {},
                elTableColumns: [],
                queryApi: this.$service.warehouse.assetCorrect.listSearch,
                hooks: {
                    tableListHook: this.tableListHook
                }
            }
        };
    },
    computed: {},
    mounted() {
        this.tableConfig1.elTableColumns = this.elTableColumns;
        this.tableConfig2.elTableColumns = this.elTableColumns;
        this.tableConfig1.queryParams = this.queryParams;
        this.tableConfig2.queryParams = this.queryParams;
        this.tableConfig1.queryConfig = this.queryConfig;
        this.tableConfig2.queryConfig = this.queryConfig;
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef1.handleQuery();
    },
    methods: {
        // 标签切换
        handleTabClick(tab) {
            if (tab.name === '查询') {
                this.$refs.tableRef2.handleQuery();
            } else {
                this.$refs.tableRef1.handleQuery();
            }
        },
        // 查看操作
        async handleView(row) {
            const res = await this.$service.warehouse.assetCorrect.detail(
                row.applyCode
            );
            const { code, message } = res;
            if (code !== '000000') {
                this.$tools.message.err(
                    message || '获取非法数据修正详情失败！'
                );
                return;
            }
            this.$refs.assetCorrectDetailsRef.openDialog(res.result);
        },
        // 编辑操作
        async handleSubmit(row) {
            try {
                const res = await this.$service.warehouse.assetCorrect.detail(
                    row.applyCode
                );
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(
                        message || '获取非法数据修正详情失败！'
                    );
                    return;
                }
                this.$refs.assetCorrectRef.openDialog(res.result, 'edit', true);
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 编辑操作
        async handleEdit(row) {
            try {
                const res = await this.$service.warehouse.assetCorrect.detail(
                    row.applyCode
                );
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(
                        message || '获取非法数据修正详情失败！'
                    );
                    return;
                }
                this.$refs.assetCorrectRef.openDialog(res.result, 'edit');
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 审核
        async handleAudit(row) {
            try {
                const res = await this.$service.warehouse.assetCorrect.detail(
                    row.applyCode
                );
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(
                        message || '获取非法数据修正详情失败！'
                    );
                    return;
                }
                this.$refs.assetCorrectRef.openDialog(res.result, 'audit');
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        }
    }
};
</script>

<style lang="scss" scoped></style>
