<template>
    <el-dialog title="物料选择" class="custom-dialog" :visible.sync="dialogVisible" v-bind="elDialogAttrs">
        <snbc-base-table ref="tableRef" :table-config="tableConfig" />
    </el-dialog>
</template>
<script>
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import ElAttrs from 'warehouse/common/el-attrs/index.js';

const { input } = FormItems;
const { elDialogAttrs } = ElAttrs;
// 产品名称
const productName = {
    ...input,
    name: '产品名称',
    modelKey: 'productName',
    elInputAttrs: {
        disabled: true
    }
};
// 物料编码
const materialCode = {
    ...input,
    name: '物料编码',
    modelKey: 'materialCode'
};
// 物料描述
const materialDesc = {
    ...input,
    name: '物料描述',
    modelKey: 'materialDesc'
};

// 页面Table配置
const commonTableConfig = {
    queryParams: {
        productName: '',
        materialCode: '',
        materialDesc: ''
    },
    queryConfig: {
        items: [productName, materialCode, materialDesc],
        elFormAttrs: {
            'label-width': '70px'
        }
    },
    elTableColumns: [
        { label: '序号', prop: 'index', show: true, minWidth: 50 },
        { label: '物料编码', prop: 'materialCode', show: true, minWidth: 120 },
        { label: '物料描述', prop: 'materialDesc', show: true, minWidth: 300 },
        { label: '公司仓库', prop: 'subInventoryName', show: true, minWidth: 120 },
        { label: '数量', prop: 'number', show: true, minWidth: 80 }
    ]
};
export default {
    name: 'ChooseMaterialModal',
    components: {
        SnbcBaseTable
    },
    data() {
        const hooks = {
            queryParamsHook: this.queryParamsHook,
            tableListHook: this.tableListHook
        };
        return {
            elDialogAttrs: {
                ...elDialogAttrs,
                width: '1200px'
            },
            dialogVisible: false,
            tableConfig: {
                headerTitle: '',
                ...this.$tools.cloneDeep(commonTableConfig),
                queryApi: this.$service.warehouse.basedata.searchMaterialInfoByProduct,
                hooks,
                // 无分页
                hasPage: false,
                operations: [
                    { name: '选择', type: 'primary', handleClick: this.handleChoose, handleShow: this.selectable }
                ]
            },
            // 基本信息
            info: {
                id: '',
                customerName: '',
                customerCode: '',
                productName: ''
            },
            // 已经选择的物料列表
            detailPOList: []
        };
    },
    methods: {
        openDialog(data) {
            // 已经选择的物料列表
            this.detailPOList = data.detailPOList || [];
            // 获取当前产品名称、客户名称、客户编码信息
            Object.keys(this.info).forEach((key) => {
                this.info[key] = data[key];
            });
            // 产品名称回显
            this.tableConfig.queryParams = {
                ...this.$tools.cloneDeep(commonTableConfig.queryParams),
                productName: data.productName
            };
            // 目标区仓
            this.directionWarehouseCode = data.targetWarehouseCode;
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.$refs.tableRef.list = [];
                this.$refs.tableRef.handleQuery();
            });
        },
        hideDialog() {
            this.dialogVisible = false;
        },
        // 列表查询参数hook方法
        queryParamsHook(params) {
            this.tableConfig.queryParams.productName = this.info.productName;
            params.productName = this.info.productName;
            params.destinationBin = this.directionWarehouseCode;
        },
        tableListHook(list) {
            let count = 0;
            list.map((item, index) => {
                item.index = index + 1;
                count += item.number;
                return item;
            });
            this.$set(this.tableConfig, 'headerTitle', `${this.info.productName}：${count}台`);
        },
        // 选择某一个物料，选择后关闭弹窗
        handleChoose(row) {
            this.$emit('choose', { ...this.info, ...row });
            this.hideDialog();
        },
        // 相同仓库&相同物料场景不允许选择
        selectable(row) {
            const repeat = this.detailPOList.some((item) => {
                return item.materialCode === row.materialCode && item.comWarehouseName === row.subInventoryName;
            });
            return !repeat;
        }
    }
};
</script>
