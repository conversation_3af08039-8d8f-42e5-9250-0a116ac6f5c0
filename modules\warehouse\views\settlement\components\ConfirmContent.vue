<template>
    <div class="container">
        <h1>威海新北洋技术服务公司</h1>
        <h2>仓库调配单</h2>
        <div class="form">
            <div v-for="item in descriptions" :key="item.label">{{ item.label }}: {{ item.value }}</div>
        </div>
        <div>
            <print-table-list :config="tableConfig" :list="list" />
        </div>
        <footer>
            <div>供应商：（盖章）{{ content.supplier || '无' }}</div>
            <div>审批：（盖章）威海新北洋技术服务有限公司</div>
        </footer>
    </div>
</template>
<script>
import PrintTableList from './PrintTableList.vue';
import printMixins from '../print-mixins';

const common = [
    { label: '协议号', key: 'none', value: '' },
    { label: '订单号', key: 'none', value: '' },
    { label: '下达日期', key: 'none', value: '' },
    { label: '供应商', key: 'supplier', value: '' },
    { label: '税率', key: 'none', value: '6%' },
    { label: '币种', key: 'none', value: 'RMB' },
    { label: '区仓', key: 'warehouseName', value: '' },
    { label: '城市', key: 'city', value: '' },
    { label: '仓库地址', key: 'address', value: '' },
    { label: '联系人', key: 'contact', value: '' },
    { label: '电话', key: 'contactTel', value: '' },
    { label: '仓库类型', key: 'repositoryType', value: '' }
];

// 面积计算
const areaDescriptions = [
    ...common,
    { label: '区仓合同面积', key: 'areaContract', value: '' },
    { label: '公摊面积', key: 'areaShared', value: '' },
    { label: '费用合计', key: 'feeTotal', value: '' },
    { label: '计划租赁面积', key: 'areaLease', value: '' },
    { label: '平均使用面积', key: 'areaUsage', value: '' },
    { label: '区仓利用率', key: 'areaUsageRatePrediction', value: '' },
    { label: '结算面积', key: 'areaSettlement', value: '' },
    { label: '结算费用', key: 'feeSettlement', value: '' },
    { label: '区仓结算利用率', key: 'areaUsageRateSettlement', value: '' },
    { label: '结算说明', key: 'settlementInstructions', value: '' }
];

// 流量计算
const trafficDescriptions = [
    ...common,
    { label: '资产总数', key: 'assetCount', value: '' },
    { label: '存放天数', key: 'storageDays', value: '' },
    { label: '费用合计', key: 'feeTotal', value: '' },
    { label: '结算费用', key: 'feeSettlement', value: '' },
    { label: '结算说明', key: 'settlementInstructions', value: '' }
];

const commonColumns = [
    {
        label: '序号',
        show: true,
        prop: 'index',
        elTableColumnAttrs: {
            width: 80
        }
    },
    {
        label: '客户',
        prop: 'customerName',
        show: true,
        elTableColumnAttrs: {
            width: 200
        }
    },
    {
        label: '资产类型',
        prop: 'assetType',
        show: true,
        elTableColumnAttrs: {
            width: 200
        }
    }
];

const trafficColumns = [
    ...commonColumns,
    {
        label: '资产数量',
        prop: 'assetCount',
        show: true,
        elTableColumnAttrs: {
            width: 160
        }
    },
    {
        label: '存放天数',
        prop: 'storageDays',
        show: true,
        elTableColumnAttrs: {
            width: 160
        }
    },
    {
        label: '价格',
        prop: 'unitPrice',
        show: true,
        elTableColumnAttrs: {
            width: 160
        }
    },
    {
        label: '费用',
        prop: 'fee',
        show: true,
        elTableColumnAttrs: {
            width: 160
        }
    }
];

const areaColumns = [
    ...commonColumns,
    {
        label: '使用面积',
        prop: 'areaAverage',
        show: true,
        elTableColumnAttrs: {
            width: 200
        }
    },
    {
        label: '价格',
        prop: 'unitPrice',
        show: true,
        elTableColumnAttrs: {
            width: 200
        }
    },
    {
        label: '合计仓储服务费',
        prop: 'fee',
        show: true,
        elTableColumnAttrs: {
            width: 200
        }
    }
];

export default {
    name: 'ReconciliationContent',
    components: {
        PrintTableList
    },
    mixins: [printMixins],
    props: {
        content: {
            tyep: Object,
            default() {
                return {};
            }
        }
    },
    computed: {
        descriptions() {
            this.init(trafficColumns, areaColumns);
            const arr = this.content.settlementMethod === '面积' ? areaDescriptions : trafficDescriptions;
            return arr.map((item) => {
                if (item.key in this.content) {
                    item.value = this.content[item.key];
                }
                return item;
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.container {
    width: 1150px;
    height: 1627px;
    display: flex;
    flex-direction: column;
    padding: 40px;
    padding-bottom: 100px;
    h1 {
        text-align: center;
        margin-block: 0.4em;
        font-size: 40px;
    }
    h2 {
        text-align: center;
        font-size: 32px;
        margin-block: 0.4em;
        line-height: 1;
    }
    .form {
        margin: 40px 0;
        display: inherit;
        flex-wrap: wrap;
        div {
            &:last-child {
                width: 100%;
            }
            line-height: 60px;
            font-size: 20px;
            width: 33%;
        }
    }
    footer {
        display: inherit;
        margin-top: auto;
        margin-bottom: 40px;
        justify-content: space-between;
        width: 100%;
        div {
            line-height: 1;
            font-size: 20px;
            text-align: left;
            width: 40%;
        }
    }
}
</style>
