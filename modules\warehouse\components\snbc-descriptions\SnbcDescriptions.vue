<template>
    <div>
        <el-descriptions v-bind="elDescriptionsAttrs">
            <el-descriptions-item
                v-for="(item, index) in items"
                :label="item.label"
                :key="index"
            >
                <el-link
                    v-if="item.type === 'image'"
                    type="primary"
                    @click="handleViewImage(item.value)"
                >
                    {{ item.value ? '查看图片' : '' }}
                </el-link>
                <template v-else>{{ item.value }}</template>
            </el-descriptions-item>
        </el-descriptions>
        <snbc-image-viewer ref="snbcImageViewerRef" />
    </div>
</template>
<script>
import SnbcImageViewer from 'warehouse/components/snbc-image-viewer/SnbcImageViewer.vue';

export default {
    name: 'SnbcDescriptions',
    components: {
        SnbcImageViewer
    },
    props: {
        items: {
            type: Array,
            default() {
                return [];
            }
        },
        config: {
            type: Object,
            default() {
                return {
                    elDescriptionsAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // 默认配置
            defaultElDescriptionsAttrs: {
                title: '',
                size: 'small',
                border: true,
                column: 2
            }
        };
    },
    computed: {
        // 组件属性
        elDescriptionsAttrs() {
            return {
                ...this.defaultElDescriptionsAttrs,
                ...(this.config.elDescriptionsAttrs || {})
            };
        }
    },
    methods: {
        // 图片预览
        handleViewImage(value) {
            if (value) {
                const srcList = value.split(',');
                this.$refs.snbcImageViewerRef.show(srcList[0], srcList);
            }
        }
    }
};
</script>
<style lang="scss" scoped></style>
