/*
 * @Author: lijing
 * @Date: 2021-03-12 13:10:45
 * @LastEditTime: 2021-03-24 14:14:59
 * @LastEditors: Please set LastEditors
 * @Description: 实现字符串AES,DES,3DES,base64,RSA,MD5加解密
 * @FilePath: \si-wtf-frame-master\node_modules\wtf-core-vue\src\utils\crypto.js
 */
import CryptoJS from 'crypto-js';
import JsEncrypt from 'jsencrypt';
import Settings from '../settings';
let secretKey = ''; // des密钥 key
let iv = ''; // 十六位十六进制数作为密钥偏移量 des密钥 keyiv
let rsaPubKey = ''; // rsa 公钥
const cryptoTyepArr = ['AES', 'DES', '3DES', 'BASE64'];

/**
  * 加密
  * @params data 加密字符串
  * @params type 加密类型
  * Crypto.encrypt(data)
 */
const initKey = (rsaPub<PERSON><PERSON><PERSON>, desK<PERSON>, desKeyIV) => {
    if (rsaPublicKey) {
        rsaPubKey = rsaPublicKey;
    } else {
        console.error('未配置RSA公钥，请在项目src/settings.js文件中配置rsaPubKey字段');
    }

    if (desKey) {
        secretKey = CryptoJS.enc.Utf8.parse(desKey);
    } else {
        console.error('未配置DES的key，请在项目src/settings.js文件中配置desKey字段');
    }

    if (desKeyIV) {
        iv = CryptoJS.enc.Utf8.parse(desKeyIV);
    } else {
        console.error('未配置DES的keyiv，请在项目src/settings.js文件中配置desKeyIV字段');
    }
};

/**
  * 加密
  * @params data 加密字符串
  * @params type 加密类型
  * Crypto.encrypt(data)
 */
const encrypt = (data, type) => {
    if (data === null || data === undefined || data === '') {
        return data;
    }
    type = type.toUpperCase();
    cryptoStrError(data);
    cryptoTypeError(type);
    if (data && typeof data === 'object') {
        data = JSON.stringify(data);
    }
    return setEncrypt(data, type);
};
/**
  * 解密
  * @params data 解密字符串
  * @params type 解密类型
  * Crypto.decrypt(data)
 */
const decrypt = (data, type) => {
    if (data === null || data === undefined || data === '') {
        return data;
    }
    type = type.toUpperCase();
    cryptoStrError(data);
    cryptoTypeError(type);
    if (data && typeof data === 'object') {
        data = JSON.stringify(data);
    }
    return getDecrypt(data, type);
};
/**
 * @description: 用于aes 3des des base64加密
 * @param {*} data 加密字符串
 * @param {*} type 加密类型
 * @return {*}
 */
const setEncrypt = (data, type) => {
    let str;
    switch (type) {
        case 'DES':
            str = setDES(data);
            break;
        case '3DES':
            str = set3DES(data);
            break;
        case 'AES':
            str = setAES(data);
            break;
        case 'BASE64':
            str = setBase64(data);
            break;
    }
    return str;
};
/**
 * @description: 用于aes 3des des base64解密
 * @param {*} data 解密字符串
 * @param {*} type 解密类型
 * @return {*}
 */
const getDecrypt = (data, type) => {
    let str;
    switch (type) {
        case 'DES':
            str = getDES(data);
            break;
        case '3DES':
            str = get3DES(data);
            break;
        case 'AES':
            str = getAES(data);
            break;
        case 'BASE64':
            str = getBase64(data);
            break;
    }
    return str;
};
/**
 * @description: md5 是不可逆的，用于校验数据的完整性
 * @param {*} data 加密字符串
 * @return {*}
 */
const encryptByMd5 = (data) => {
    cryptoStrError(data);
    if (data && typeof data === 'object') {
        data = JSON.stringify(data);
    }
    const encrypted = CryptoJS.MD5(data).toString();
    return encrypted;
};

/**
 * @description: RSA加密 需要服务端提供公私钥，公钥用于加密，私钥用于解密
 * @param {*} data 加密字符串
 * @return {*}
 */
const encryptByRsa = (data) => {
    cryptoStrError(data);
    if (data && typeof data === 'object') {
        data = JSON.stringify(data);
    }
    const jse = new JsEncrypt();
    jse.setPublicKey(rsaPubKey);
    const encrypted = jse.encrypt(data);
    return encrypted;
};

/**
  * DES解密,使用方法：
  * @params data 加密字符串
  * Crypto.getDES(data)
 */
function getDES(data) {
    var decrypted = CryptoJS.DES.decrypt(data, secretKey, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    // 转换为 utf8 字符串
    decrypted = CryptoJS.enc.Utf8.stringify(decrypted);
    return decrypted;
}
/**
  * DES加密,使用方法：
  * @params data 加密字符串
  * Crypto.setDES(data)
 */
function setDES(data) {
    let encrypted = CryptoJS.DES.encrypt(data, secretKey, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    encrypted = encrypted.ciphertext.toString();
    return encrypted;
}
/**
  * 3DES加密,使用方法：
  * @params data 加密字符串
  * Crypto.set3DES(data)
 */
function set3DES(data) {
    let encrypted = CryptoJS.TripleDES.encrypt(data, secretKey, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    encrypted = encrypted.toString();
    return encrypted;
}
/**
  * 3DES解密,使用方法：
  * @params data 加密字符串
  * Crypto.get3DES(data)
 */
function get3DES(data) {
    var decrypted = CryptoJS.TripleDES.decrypt(data, secretKey, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    // 转换为 utf8 字符串
    decrypted = CryptoJS.enc.Utf8.stringify(decrypted);
    return decrypted;
}
/**
  * AES加密,使用方法：
  * @params data 加密字符串
  * Crypto.setAES(data)
 */
function getAES(data) {
    let decrypted = CryptoJS.AES.decrypt(data, secretKey, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });

    // 转换为 utf8 字符串
    decrypted = CryptoJS.enc.Utf8.stringify(decrypted);
    return decrypted;
}
/**
  * AES解密,使用方法：
  * @params data 加密字符串
  * Crypto.setAES(data)
 */
function setAES(data) {
    let encrypted = CryptoJS.AES.encrypt(data, secretKey, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    encrypted = encrypted.toString();
    return encrypted;
}
/**
  * base64解码,使用方法：
  * @params data 解码字符串
  * Crypto.getBase64(data)
 */
function getBase64(data) {
    let decrypted = CryptoJS.enc.Base64.parse(data);
    decrypted = decrypted.toString(CryptoJS.enc.Utf8);
    return decrypted;
}

/**
  * base64编码,使用方法：
  * @params data 编码字符串
  * Crypto.setBase64(data)
 */
function setBase64(data) {
    let encrypted = CryptoJS.enc.Utf8.parse(data);
    encrypted = CryptoJS.enc.Base64.stringify(encrypted);
    return encrypted;
}

/**
 * @description: 加密类型异常提示
 * @param {*} type 加密类型
 * @return {*} cryptoError
 */
function cryptoTypeError(type) {
    if (!cryptoTyepArr.includes(type)) {
        console.error('Parameter type "' + type + '" is invalid');
        return;
    }
}
/**
 * @description: 加密字符串异常处理
 * @param {*} str 加密字符串
 * @return {*}
 */
function cryptoStrError(str) {
    if (!str) {
        console.error('Parameter string "' + str + '" is invalid');
        return;
    }
}
export {
    initKey,
    encryptByMd5,
    encrypt,
    decrypt,
    encryptByRsa
};