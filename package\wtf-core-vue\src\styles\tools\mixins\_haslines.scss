/// Has Lines
/// @param {number} $gutter [20px] - 间距
/// @param {string} $color [#ccc] - 线条颜色
/// @param {number} $minWidth [20px] - 线条宽度
/// @param {number} $height [1px] - 线条粗细

@mixin haslines($gutter:20px, $color:#ccc, $minWidth:20px, $height:1px) {
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    &:before,
    &:after {
        content: '';
        flex-grow: 1;
        background-color: $color;
        display: inline-block;
        vertical-align: middle;
        height: $height;
        min-width: $minWidth;
    }
    &:before {
        margin-right: $gutter;
    }
    &:after {
        margin-left: $gutter;
    }
}
