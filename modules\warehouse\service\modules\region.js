/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        region: {
            /**
             * 获取省市区全部数据
             *
             * @returns {Promise} http
             */
            getRegionData() {
                return http({
                    baseDomain: basePath.orderApi.base,
                    url: '/provider/provider_area/get_all_area_info',
                    loading: false,
                    method: 'get'
                });
            },
            /**
             * 获取省份数据
             *
             * @returns {Promise} http
             */
            getProvince() {
                return http({
                    baseDomain: basePath.orderApi.base,
                    url: '/provider/provider_area/search_province_list',
                    loading: false,
                    method: 'get'
                });
            },
            /**
             * 根据省份名称获取城市数据
             *
             * @param {String} provinceName 省份名称
             * @returns {Promise} http
             */
            getCityByProvinceName(provinceName) {
                return http({
                    baseDomain: basePath.orderApi.base,
                    url: '/provider/provider_area/search_city_list_byname',
                    loading: false,
                    method: 'get',
                    params: {
                        provinceName
                    }
                });
            },
            /**
             * 根据城市名称获取地区数据
             *
             * @param {String} cityName 城市名称
             * @returns {Promise} http
             */
            getAreaByCityName(cityName) {
                return http({
                    baseDomain: basePath.orderApi.base,
                    url: '/provider/provider_area/search_area_list_byname',
                    loading: false,
                    method: 'get',
                    params: {
                        cityName
                    }
                });
            }
        }
    };

    return service;
};
