digraph G {
	graph [mindist=0.1];
	csf [shape=doublecircle,label="Common\nSpreadsheet\nFormat\n(JS Object)"];
	subgraph XL {
		node  [style=filled,color=green];
		xlsx  [label="XLSX\nXLSM"];
		xlsb  [label="XLSB\nBIFF12"];
		xlml  [label="SSML\n(2003/4)"];
		xls2  [label="XLS\nBIFF2"];
		xls3  [label="XLS\nBIFF3"];
		xls4  [label="XLS\nBIFF4"];
		xls5  [label="XLS\nBIFF5"];
		xls8  [label="XLS\nBIFF8"];
	}

	subgraph OLD {
		node  [style=filled,color=cyan];
		ods   [label="ODS"];
		fods  [label="FODS"];
		uos   [label="UOS"];
		html  [label="HTML\nTable"];
		csv   [label="CSV"];
		txt   [label="TXT\nUTF-16"];
		dbf   [label="DBF"];
		dif   [label="DIF"];
		slk   [label="SYLK"];
		prn   [label="PRN"];
		rtf   [label="RTF"];
		wk1   [label="WK1/2\n123"];
		wk3   [label="WK3/4"];
		wqb   [label="WQ*\nWB*"];
		qpw   [label="QPW"];
		eth   [label="ETH"];
	}

	subgraph WORKBOOK {
		edge [color=blue];
		csf -> xlsx
		xlsx -> csf
		csf -> xlsb
		xlsb -> csf
		csf -> xlml
		xlml -> csf
		csf -> xls5
		xls5 -> csf
		csf -> xls8
		xls8 -> csf
		ods -> csf
		csf -> ods
		fods -> csf
		csf -> fods
		uos -> csf
		wk3 -> csf
		qpw -> csf
	}
	subgraph WORKSHEET {
		edge [color=aquamarine4];
		xls2 -> csf
		csf -> xls2
		xls3 -> csf
		xls4 -> csf
		csf -> slk
		slk -> csf
		csf -> dif
		wk1 -> csf
		wqb -> csf
		dif -> csf
		csf -> rtf
		prn -> csf
		csf -> prn
		csv -> csf
		csf -> csv
		txt -> csf
		csf -> txt
		dbf -> csf
		csf -> dbf
		html -> csf
		csf -> html
		csf -> eth
		eth -> csf
	}
}
