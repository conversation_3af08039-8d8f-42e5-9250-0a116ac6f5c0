import axios from 'axios';
import store from '../store';
import i18n from '../lang';
import router from '../router';
import {
    urlParamsToObj,
    jsonHandler,
    getDataSignature,
    getSignature
} from './signature';
// create an axios instance
const service = axios.create({
    baseURL: '' // url = base url + request url
    // withCredentials: true, // send cookies when cross-domain requests
    // timeout: 5000 // request timeout
});

// 配置axios默认值
if (store.state.settings.timeout) {
    service.defaults.timeout = store.state.settings.timeout;
}

// request interceptor
service.interceptors.request.use(
    (config) => {
        // do something before request is sent
        if (!config.baseDomain) {
            console.error('请设置请求的参数baseDomain为url的根域名');
        } else {
            config.baseURL = config.baseDomain;
        }

        if (store.getters.token) {
            // let each request carry token
            // ['X-Token'] is a custom headers key
            // please modify it according to the actual situation
            const token = store.state.settings.token;
            config.headers[token.token_name] = `${token.token_prefix || ''}${
                store.getters.token
            }${token.token_suffix || ''}`;
        }
        //  区分是否是文件上传类型，根据不同类型添加不同的验签（根据项目实际情况进行调整）
        if (
            config.headers['vfSignature'] === undefined ||
            config.headers['vfSignature'] === null
        ) {
            if (config.headers['Content-Type']) {
                config.headers['vfSignature'] =
                    '994d8349260e4a0d9c956519322b4aed';
                config.headers['signature'] = getSignature();
            } else {
                var urlParams = urlParamsToObj(config.url);
                var signature_params = jsonHandler(config.params);
                var signature_data = jsonHandler(config.data);
                var signature_url = jsonHandler(urlParams);
                // 添加到请求头
                config.headers['signature'] = getDataSignature(
                    signature_params,
                    signature_data,
                    signature_url
                );
            }
        }

        var urlParams = urlParamsToObj(config.url);
        var signature_params = jsonHandler(config.params);
        var signature_data = jsonHandler(config.data);
        var signature_url = jsonHandler(urlParams);
        // 添加到请求头
        config.headers['signature'] = getDataSignature(
            signature_params,
            signature_data,
            signature_url
        );

        return config;
    },
    (error) => {
        // do something with request error
        return Promise.reject(error);
    }
);

// response interceptor
service.interceptors.response.use(
    /**
     * If you want to get http information such as headers or status
     * Please return  response => response
     */

    /**
     * Determine the request status by custom code
     * Here is just an example
     * You can also judge the status by HTTP Status Code
     */
    (response) => {
        if (responseHandleFun && typeof responseHandleFun === 'function') {
            const flag = responseHandleFun(response, i18n, router, store);
            if (flag) {
                return response.data;
            } else {
                return Promise.reject(
                    new Error(response.statusText || 'Error')
                );
            }
        } else {
            return response;
        }
    },
    (error) => {
        if (responseHandleFun && typeof responseHandleFun === 'function') {
            responseHandleFun(error.response, i18n, router, store);
        }
        return Promise.reject(error);
    }
);
let responseHandleFun = null;
function responseHandle(fun) {
    if (fun && typeof fun === 'function') {
        responseHandleFun = fun;
    }
}
service['responseHandle'] = responseHandle;
export default service;
