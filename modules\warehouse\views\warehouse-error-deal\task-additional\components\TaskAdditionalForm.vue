<template>
    <el-dialog
        class="custom-dialog"
        :visible.sync="dialogVisible"
        width="1200px"
        v-bind="elDialogAttrs"
    >
        <snbc-tabs :tabs="tabsConfig" class="container">
            <template #1>
                <snbc-form ref="snbcFormRef" :form="form" :config="formConfig">
                    <template #form-body>
                        <template v-for="(item, index) in formItems">
                            <snbc-form-item
                                v-if="
                                    (item.modelKey !== 'taskAdditionalCode' ||
                                        mode != 'add') &&
                                    (item.modelKey !== 'associatedTaskCode' ||
                                        form.associatedTaskType !=
                                            '日常工作') &&
                                    (!['edit', 'add'].includes(mode) ||
                                        item.modelKey !== 'content') &&
                                    (item.modelKey !== 'srcWarehouseCode' ||
                                        form.associatedTaskType !== '公司发货')
                                "
                                :prop="item.modelKey"
                                :key="index"
                                :config="item"
                            />
                        </template>
                        <snbc-card
                            v-if="
                                form.taskAdditionalType === '入库' &&
                                mode != 'audit'
                            "
                            :title="'已有入库记录'"
                        >
                            <template #card-body
                                ><el-table :data="assetInRecordList" border>
                                    <el-table-column
                                        prop="assetCode"
                                        label="产品序列号"
                                        min-width="180"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                        prop="happenDate"
                                        label="时间"
                                        min-width="180"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                        prop="productName"
                                        min-width="120"
                                        label="产品名称"
                                    >
                                    </el-table-column>
                                </el-table>
                            </template>
                        </snbc-card>
                        <snbc-card
                            v-if="
                                form.taskAdditionalType === '出库' &&
                                mode != 'audit'
                            "
                            :title="'已有出库记录'"
                        >
                            <template #card-body
                                ><el-table :data="assetOutRecordList" border>
                                    <el-table-column
                                        prop="assetCode"
                                        label="产品序列号"
                                        min-width="180"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                        prop="happenDate"
                                        label="时间"
                                        min-width="180"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                        prop="productName"
                                        min-width="120"
                                        label="产品名称"
                                    >
                                    </el-table-column>
                                </el-table>
                            </template>
                        </snbc-card>
                        <snbc-card
                            v-if="
                                form.taskAdditionalType === '出库' &&
                                mode != 'audit'
                            "
                            :title="'参考信息：入库记录'"
                        >
                            <template #card-body
                                ><el-table :data="assetInRecordList" border>
                                    <el-table-column
                                        prop="assetCode"
                                        label="产品序列号"
                                        min-width="180"
                                        :formatter="formatterCell"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                        prop="happenDate"
                                        label="时间"
                                        min-width="180"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                        prop="productName"
                                        min-width="120"
                                        label="产品名称"
                                    >
                                    </el-table-column> </el-table
                            ></template>
                        </snbc-card>
                        <snbc-card
                            v-if="
                                form.taskAdditionalType === '入库' &&
                                mode != 'audit'
                            "
                            :title="'参考信息：出库记录'"
                        >
                            <template #card-body
                                ><el-table :data="assetOutRecordList" border>
                                    <el-table-column
                                        prop="assetCode"
                                        label="产品序列号"
                                        min-width="180"
                                        :formatter="formatterCell"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                        prop="happenDate"
                                        label="时间"
                                        min-width="180"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                        prop="productName"
                                        min-width="120"
                                        label="产品名称"
                                    >
                                    </el-table-column> </el-table
                            ></template>
                        </snbc-card>
                        <snbc-card :title="'补录记录'">
                            <template #card-body>
                                <el-button
                                    v-if="mode != 'audit'"
                                    size="small"
                                    style="float: right; margin-bottom: 8px"
                                    type="primary"
                                    @click="add"
                                >
                                    新增
                                </el-button>
                                <el-table :data="form.associatedRecords" border>
                                    <el-table-column
                                        prop="assetCode"
                                        label="产品序列号"
                                        min-width="180"
                                    >
                                        <template #default="scope">
                                            <span
                                                v-show="!isshow[scope.$index]"
                                                >{{ scope.row.assetCode }}</span
                                            >
                                            <el-input
                                                v-show="isshow[scope.$index]"
                                                v-model="scope.row.assetCode"
                                                @blur="handleBlur(scope.row)"
                                            />
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="operateTime"
                                        label="时间"
                                        min-width="200"
                                    >
                                        <template #default="scope">
                                            <span v-show="!isshow[scope.$index]"
                                                >{{ scope.row.operateTime }}
                                            </span>
                                            <el-date-picker
                                                value-format="yyyy-MM-dd HH:mm:ss"
                                                type="datetime"
                                                v-show="isshow[scope.$index]"
                                                v-model="scope.row.operateTime"
                                            />
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="operateType"
                                        label="类型"
                                        min-width="100"
                                    >
                                        <template #default="scope">
                                            <span
                                                v-show="!isshow[scope.$index]"
                                                >{{
                                                    scope.row.operateType
                                                }}</span
                                            >
                                            <el-input
                                                :disabled="true"
                                                v-show="isshow[scope.$index]"
                                                v-model="scope.row.operateType"
                                            />
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="taskAdditionalType"
                                        min-width="120"
                                        label="补录类型"
                                    >
                                        <template #default="scope">
                                            <span
                                                v-show="!isshow[scope.$index]"
                                                >{{
                                                    scope.row.taskAdditionalType
                                                }}</span
                                            >
                                            <el-input
                                                :disabled="true"
                                                v-show="isshow[scope.$index]"
                                                value="人工补录"
                                                v-model="
                                                    scope.row.taskAdditionalType
                                                "
                                            />
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="productName"
                                        min-width="120"
                                        label="产品名称"
                                    >
                                        <template #default="scope">
                                            <span
                                                v-show="!isshow[scope.$index]"
                                                >{{
                                                    scope.row.productName
                                                }}</span
                                            >
                                            <el-input
                                                v-show="isshow[scope.$index]"
                                                v-model="scope.row.productName"
                                            />
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        min-width="80"
                                        label="操作"
                                        v-if="mode === 'add' || mode === 'edit'"
                                    >
                                        <template slot-scope="scope">
                                            <el-button
                                                size="small"
                                                @click="
                                                    handleDelete(
                                                        scope.$index,
                                                        scope.row
                                                    )
                                                "
                                                >删除</el-button
                                            >
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </template>
                        </snbc-card>
                    </template>
                </snbc-form>
            </template>
            <template #2>
                <el-timeline>
                    <el-timeline-item
                        v-for="item in form.additionalLogs"
                        :key="item.key"
                        :timestamp="item.operateTime"
                        :hide-timestamp="true"
                        color="#409eff"
                    >
                        <el-card>
                            <p>时间：{{ item.operateTime }}</p>
                            <p>操作：{{ item.operateType }}</p>
                            <p>操作人：{{ item.operateUserName }}</p>
                            <p>内容：{{ item.operateContent }}</p>
                        </el-card>
                    </el-timeline-item>
                </el-timeline>
                <div class="no-data" v-if="!form.additionalLogs.length">
                    暂无数据
                </div>
            </template>
        </snbc-tabs>
        <span
            v-if="['edit', 'add'].includes(mode)"
            slot="footer"
            class="dialog-footer"
        >
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleSave" v-if="!submitFlag"
                >保 存</el-button
            >
            <el-button type="primary" @click="handleSubmit" v-if="submitFlag"
                >提 交</el-button
            >
        </span>
        <span v-if="mode === 'audit'" slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handlePass">通 过</el-button>
            <el-button type="warning" @click="handleReject">驳 回</el-button>
        </span>
    </el-dialog>
</template>

<script>
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import SnbcTabs from 'warehouse/components/snbc-tabs/SnbcTabs.vue';
import SnbcFormItem from 'warehouse/components/snbc-form/SnbcFormItem.vue';
import SnbcForm from 'warehouse/components/snbc-form/SnbcForm.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import FormRules from 'warehouse/common/form-rules/index.js';
import ElAttrs from 'warehouse/common/el-attrs/index.js';

const { inputRequired, maxLength, selectRequired } = FormRules;

const { input, select, textarea, warehouseSelect } = FormItems;

const { elDialogAttrs } = ElAttrs;

const taskAdditionalCode = {
    ...input,
    name: '补录申请编码',
    modelKey: 'taskAdditionalCode',
    elInputAttrs: { disabled: true }
};
const taskAdditionalName = {
    ...input,
    name: '补录申请名称',
    modelKey: 'taskAdditionalName',
    elInputAttrs: {}
};
const taskAdditionalType = {
    ...select,
    name: '补录类型',
    modelKey: 'taskAdditionalType',
    elOptions: [
        { label: '入库 ', value: '入库' },
        { label: '出库', value: '出库' }
    ],
    elSelectAttrs: {
        disabled: true
    }
};
const taskAdditionalReason = {
    ...input,
    name: '申请原因',
    modelKey: 'taskAdditionalReason',
    elInputAttrs: {}
};
const associatedTaskCode = {
    ...textarea,
    name: '关联任务编号',
    modelKey: 'associatedTaskCode',
    elInputAttrs: {
        disabled: true
    }
};
const associatedTaskType = {
    ...select,
    name: '关联任务类型',
    modelKey: 'associatedTaskType',
    elOptions: [
        { label: '公司发货 ', value: '公司发货' },
        { label: '盘点提案处理', value: '盘点提案处理' },
        { label: '调拨', value: '调拨' },
        { label: '日常工作', value: '日常工作' }
    ],
    elSelectAttrs: {
        disabled: true
    }
};
const warehouseCode = {
    ...warehouseSelect,
    name: '目的区仓',
    modelKey: 'warehouseCode',
    elSelectAttrs: {
        disabled: true
    }
};
const srcWarehouseCode = {
    ...warehouseSelect,
    name: '来源区仓',
    modelKey: 'srcWarehouseCode',
    elSelectAttrs: {
        disabled: true
    }
};
const associatedTaskName = {
    ...input,
    name: '关联任务名',
    modelKey: 'associatedTaskName',
    elInputAttrs: {
        disabled: true
    }
};
const source = {
    ...select,
    name: '来源',
    modelKey: 'source',
    elOptions: [
        { label: '盘点自动补录 ', value: '盘点自动补录' },
        { label: '人工补录', value: '人工补录' }
    ],
    elSelectAttrs: {
        disabled: true
    }
};

const remark = {
    ...textarea,
    name: '备注',
    modelKey: 'remark',
    elInputAttrs: {
        disabled: true
    }
};
const content = {
    ...textarea,
    name: '审核内容',
    modelKey: 'content'
};

const rules = {
    taskAdditionalName: [inputRequired('任务名称'), maxLength(64)],
    associatedTaskType: [selectRequired('关联任务类型')],
    content: [inputRequired('审核内容'), maxLength(500)],
    applyReason: [inputRequired('申请原因'), maxLength(500)],
    warehouseCode: [inputRequired('区仓名称'), maxLength(16)],
    taskAdditionalType: [selectRequired('补录类型')],
    damageTaskCode: [inputRequired('任务编码'), maxLength(32)],
    srcWarehouseCode: [inputRequired('来源仓'), maxLength(64)],
    associatedTaskName: [inputRequired('关联任务名称'), maxLength(64)],
    associatedTaskCode: [inputRequired('关联任务编码'), maxLength(32)],
    taskAdditionalReason: [inputRequired('申请原因'), maxLength(500)],
    source: [inputRequired('来源')]
};

export default {
    name: 'TaskAdditionalForm',
    components: {
        SnbcForm,
        SnbcFormItem,
        SnbcTabs,
        SnbcCard
    },
    data() {
        return {
            submitFlag: false,
            isshow: [],
            tableData: [],
            assetOutRecordList: [],
            assetInRecordList: [],
            options: [],
            taskDetails: 1,
            // 标题 view | edit
            title: '',
            // 弹窗模式 view | edit
            mode: '',
            // 弹窗显示
            dialogVisible: false,
            elDialogAttrs: {},
            form: {},
            formConfig: {
                elFormAttrs: {
                    rules,
                    'label-width': '140px'
                }
            },
            // 标签页配置
            tabsConfig: [
                {
                    id: 1,
                    label: '任务补录信息'
                },
                {
                    id: 2,
                    label: '执行日志'
                }
            ]
        };
    },
    computed: {
        formItems() {
            return [
                taskAdditionalCode,
                taskAdditionalName,
                taskAdditionalType,
                taskAdditionalReason,
                associatedTaskCode,
                associatedTaskType,
                warehouseCode,
                srcWarehouseCode,
                associatedTaskName,
                source,
                remark,
                content
            ].map((item) => {
                if (
                    [
                        'taskAdditionalName',
                        'taskAdditionalReason',
                        'associatedTaskCode',
                        'associatedTaskName',
                        'remark'
                    ].includes(item.modelKey)
                ) {
                    item.elInputAttrs.disabled = this.mode === 'audit';
                }
                if (
                    [
                        'source',
                        'warehouseCode',
                        'srcWarehouseCode',
                        'associatedTaskType',
                        'taskAdditionalType'
                    ].includes(item.modelKey)
                ) {
                    item.elSelectAttrs.disabled =
                        this.mode === 'audit' ||
                        ['公司发货', '调拨'].includes(
                            this.form.associatedTaskType
                        );
                }
                if (
                    ['associatedTaskCode', 'associatedTaskName'].includes(
                        item.modelKey
                    )
                ) {
                    item.elInputAttrs.disabled =
                        this.mode === 'audit' ||
                        ['公司发货', '调拨'].includes(
                            this.form.associatedTaskType
                        );
                }
                return {
                    modelObj: this.form,
                    elFormItemAttrs: {
                        label: item.name,
                        ...(item.elFormItemAttrs || {})
                    },
                    ...item
                };
            });
        }
    },
    methods: {
        // 通过资产编码获取产品名称
        async getProductNameByAssetCode(assetCode) {
            if (!assetCode) return;
            const {
                code,
                result: { assetInfoPO },
                message
            } = await this.$service.warehouse.assets.getOperationRecordDetails(
                assetCode
            );
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
            }
            if (!assetInfoPO) {
                this.$tools.message.err('未获取到产品名称');
            }
            return assetInfoPO;
        },
        async handleBlur(row) {
            const assetObj = await this.getProductNameByAssetCode(
                row.assetCode
            );
            Object.assign(row, { productName: assetObj.productName });
        },
        // 新增
        add() {
            this.form.associatedRecords.push({
                taskAdditionalCode: this.form.taskAdditionalCode,
                warehouseCode: this.form.warehouseCode,
                assetCode: '',
                productName: '',
                operateTime: '',
                operateType: '人工补录',
                taskAdditionalType: this.form.taskAdditionalType,
                srcWarehouseCode: this.form.srcWarehouseCode || undefined
            });
            this.$set(
                this.isshow,
                this.form.associatedRecords.length - 1,
                true
            );
        },
        handleDelete(index, data) {
            this.form.associatedRecords.splice(index, 1);
        },
        async getInOutRecord(data) {
            const res =
                await this.$service.warehouse.taskAdditional.getInOutRecord(
                    data
                );
            const { code, message, result } = res;
            if (code !== '000000') {
                this.$tools.message.err(message || '获取出入库信息失败！');
                return;
            }
            this.assetOutRecordList = result.assetOutRecordList;
            this.assetInRecordList = result.assetInRecordList;
        },
        async openDialog(row, mode, submitFlag) {
            this.submitFlag = false;
            this.submitFlag = submitFlag;
            this.getInOutRecord(row.baseInfo.associatedTaskCode);
            this.mode = mode;
            const map = {
                edit: '编辑任务补录任务',
                view: '查看任务补录任务',
                audit: '审核任务补录任务',
                add: '新增任务补录任务'
            };
            this.title = map[mode];
            this.form = row.baseInfo;
            this.form.associatedRecords = row.additionalRecords;
            this.form.additionalLogs = row.additionalLogs;
            this.elDialogAttrs = {
                ...elDialogAttrs,
                title: this.title
            };
            this.dialogVisible = true;
        },
        formatterCell(row, column, value) {
            return value.replace(/\w{6,10}/, '****');
        },
        hideDialog() {
            this.dialogVisible = false;
        },
        // 提交操作
        async handleSubmit() {
            await this.$tools.validateForm(this.$refs.snbcFormRef.getFormRef());
            await this.$tools.confirm('确认提交审核吗？');
            try {
                this.form.taskAdditionalState = '审核中';
                const res =
                    await this.$service.warehouse.taskAdditional.createCommit(
                        this.form
                    );
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('提交成功');
                this.$parent.$refs.tableRef1 &&
                    this.$parent.$refs.tableRef1.queryList();
                this.hideDialog();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 编辑保存
        async handleSave() {
            this.form.taskAdditionalState = '待提交';
            await this.$tools.validateForm(this.$refs.snbcFormRef.getFormRef());
            await this.$tools.confirm('确认保存吗？');
            try {
                if (this.mode === 'add') {
                    const res =
                        await this.$service.warehouse.taskAdditional.createDraft(
                            this.form
                        );
                    const { code, message } = res;
                    if (code !== '000000') {
                        this.$tools.message.err(message || '系统异常');
                        return;
                    }
                    this.$tools.message.suc('保存成功');
                    this.$parent.$refs.tableRef1 &&
                        this.$parent.$refs.tableRef1.queryList();
                    this.hideDialog();
                }
                if (this.mode === 'edit') {
                    const res =
                        await this.$service.warehouse.taskAdditional.edit(
                            this.form
                        );
                    const { code, message } = res;
                    if (code !== '000000') {
                        this.$tools.message.err(message || '系统异常');
                        return;
                    }
                    this.$tools.message.suc('保存成功');
                    this.$parent.$refs.tableRef1 &&
                        this.$parent.$refs.tableRef1.queryList();
                    this.hideDialog();
                }
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 驳回
        async handleReject() {
            await this.$tools.validateForm(this.$refs.snbcFormRef.getFormRef());
            await this.$tools.confirm('确认驳回吗？');
            try {
                const examineObj = {
                    logDomain: {
                        operateContent: this.form.content,
                        operateRole: this.form.nodeRole,
                        taskAdditionalCode: this.form.taskAdditionalCode,
                        operateState: '驳回'
                    },
                    flowId: this.form.flowId,
                    nodeRole: this.form.nodeRole,
                    nodeIndex: this.form.nodeIndex
                };
                const res =
                    await this.$service.warehouse.taskAdditional.examine(
                        examineObj
                    );
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('驳回成功');
                this.$parent.$refs.tableRef1 &&
                    this.$parent.$refs.tableRef1.queryList();
                this.hideDialog();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 通过
        async handlePass() {
            await this.$tools.validateForm(this.$refs.snbcFormRef.getFormRef());
            await this.$tools.confirm('确认通过吗？');
            try {
                const examineObj = {
                    logDomain: {
                        taskAdditionalCode: this.form.taskAdditionalCode,
                        operateState: '通过',
                        operateContent: this.form.content,
                        operateRole: this.form.nodeRole
                    },
                    flowId: this.form.flowId,
                    nodeRole: this.form.nodeRole,
                    nodeIndex: this.form.nodeIndex
                };
                const res =
                    await this.$service.warehouse.taskAdditional.examine(
                        examineObj
                    );
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('通过成功');
                this.$parent.$refs.tableRef1 &&
                    this.$parent.$refs.tableRef1.queryList();
                this.hideDialog();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 取消操作-隐藏弹窗
        handleCancel() {
            this.hideDialog();
        }
    }
};
</script>

<style lang="scss" scoped>
.no-data {
    line-height: 60px;
    text-align: center;
    color: #909399;
}
.container {
    height: 500px;
    overflow-y: auto;
}
</style>
