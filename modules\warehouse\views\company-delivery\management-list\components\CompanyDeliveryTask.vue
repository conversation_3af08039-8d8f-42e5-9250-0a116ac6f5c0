<template>
    <div>
        <el-dialog class="custom-dialog query-label-line2" :visible.sync="dialogVisible" v-bind="elDialogAttrs">
            <template v-if="detail">
                <!-- 发货任务基本信息 -->
                <snbc-card title="发货任务基本信息">
                    <template #card-body>
                        <snbc-form ref="snbcFormRef" :form="form" :config="formConfig">
                            <template #form-body>
                                <template v-for="(item, index) in formItems">
                                    <el-col :span="item.span || 12" :key="item.modelKey">
                                        <snbc-form-item
                                            v-if="item.modelKey !== 'taskNameSuffix'"
                                            :key="index"
                                            :config="item"
                                        />
                                        <!-- 公司发货任务名称 带插槽 -->
                                        <snbc-form-item
                                            v-if="item.modelKey === 'taskNameSuffix'"
                                            :key="index"
                                            :config="item"
                                        >
                                            <template #prepend>
                                                {{ form.taskNamePrefix }}
                                            </template>
                                        </snbc-form-item>
                                    </el-col>
                                </template>
                            </template>
                        </snbc-form>
                    </template>
                </snbc-card>
                <!-- 任务明细信息 查看 -->
                <snbc-card v-if="mode === 'view'" class="margin-top-10" title="任务明细">
                    <template #card-body>
                        <el-alert
                            class="m-b-10"
                            title="公司搬运订单需指定“物料编码”、“公司仓库”以及“ERP搬运单号”。公司销售订单仅关注销售的产品名称即可，其他信息无需关注。"
                            type="error"
                            :closable="false"
                        />
                        <snbc-table-list :config="detailTableConfig" :list="detail.detailPOList" />
                    </template>
                </snbc-card>
                <!-- 缺少物料信息 -->
                <snbc-card v-if="mode === 'edit'" class="margin-top-10" title="缺少物料">
                    <template #card-body>
                        <snbc-table-list :config="applyTableConfig" :list="detail.stockRequestDetailList" />
                    </template>
                </snbc-card>
                <!-- 任务明细信息 编辑 -->
                <snbc-card
                    v-if="mode === 'edit'"
                    class="margin-top-10"
                    title="任务明细"
                    :buttonName="buttonName"
                    @action="handleAdd"
                >
                    <template #card-body>
                        <el-table :data="detail.detailPOList" v-bind="elAttrs.elTableAttrs">
                            <el-table-column v-bind="elAttrs.elTableColumnAttrs" label="产品名称" min-width="200">
                                <template slot-scope="scope">
                                    <!-- 销售订单允许变更产品名称 -->
                                    <el-select
                                        v-model="scope.row.productName"
                                        placeholder="请选择"
                                        :disabled="detail.orderType === 'carry'"
                                    >
                                        <el-option
                                            v-for="productName in productNameOptions"
                                            :key="productName"
                                            :label="productName"
                                            :value="productName"
                                            :disabled="isDisabledProduct(productName)"
                                        />
                                    </el-select>
                                </template>
                            </el-table-column>
                            <!-- 搬运单展示物料编码 -->
                            <el-table-column
                                v-if="detail.orderType === 'carry'"
                                v-bind="elAttrs.elTableColumnAttrs"
                                label="物料编码"
                                width="200"
                            >
                                <template slot-scope="scope">
                                    {{ scope.row.materialCode }}
                                </template>
                            </el-table-column>
                            <el-table-column v-bind="elAttrs.elTableColumnAttrs" label="计划发货数量" min-width="250">
                                <template slot-scope="scope">
                                    <el-input-number
                                        v-model="scope.row.productNumber"
                                        :min="1"
                                        :max="maxProductNum(scope.row)"
                                        controls-position="right"
                                    ></el-input-number>
                                </template>
                            </el-table-column>
                            <!-- 搬运单展示公司仓库数据 -->
                            <el-table-column
                                v-if="detail.orderType === 'carry'"
                                v-bind="elAttrs.elTableColumnAttrs"
                                label="公司仓库"
                                width="200"
                            >
                                <template slot-scope="scope">
                                    {{ scope.row.comWarehouseName }}
                                    {{ scope.row.number === undefined ? '' : `:${scope.row.number}` }}
                                </template>
                            </el-table-column>
                            <el-table-column v-bind="elAttrs.operateColumnAttrs" width="160">
                                <template slot-scope="scope">
                                    <!-- 搬运单允许物料替换 -->
                                    <el-button
                                        v-if="detail.orderType === 'carry'"
                                        size="mini"
                                        type="primary"
                                        @click="showReplaceModal(scope.$index)"
                                    >
                                        物料替换
                                    </el-button>
                                    <el-button size="mini" type="danger" @click="handleRemove(scope.$index)">
                                        删除
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </template>
                </snbc-card>
                <span v-if="mode === 'edit'" slot="footer" class="dialog-footer">
                    <el-button @click="hideDialog">取 消</el-button>
                    <el-button type="primary" @click="handleSave">保 存</el-button>
                </span>
            </template>
        </el-dialog>
        <ChooseMaterialModal ref="chooseMaterialModalRef" @choose="handleReplace" />
    </div>
</template>

<script>
import SnbcTableList from 'warehouse/components/snbc-table/SnbcTableList.vue';
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import SnbcForm from 'warehouse/components/snbc-form/SnbcForm.vue';
import SnbcFormItem from 'warehouse/components/snbc-form/SnbcFormItem.vue';
import FormRules from 'warehouse/common/form-rules/index.js';
import elAttrs from 'warehouse/common/el-attrs/index.js';
import CompanyDeliveryTaskItems from 'warehouse/common/form-items/company-delivery-items.js';
import ChooseMaterialModal from './ChooseMaterialModal.vue';

const { inputRequired, maxLength } = FormRules;

// 备货申请任务编号
const stockRequestCode = {
    ...CompanyDeliveryTaskItems.stockRequestCode,
    elInputAttrs: {
        disabled: true
    }
};
// 任务来源
const taskSource = {
    ...CompanyDeliveryTaskItems.taskSource,
    elInputAttrs: {
        disabled: true
    }
};
// 客户名称
const customerName = {
    ...CompanyDeliveryTaskItems.customerName,
    elInputAttrs: {
        disabled: true
    }
};
// 目标区仓
const targetWarehouse = {
    ...CompanyDeliveryTaskItems.targetWarehouse,
    elInputAttrs: {
        disabled: true
    }
};
// 公司发货任务名称后缀
const taskNameSuffix = {
    ...CompanyDeliveryTaskItems.companyDeliveryTaskName,
    span: 24,
    modelKey: 'taskNameSuffix',
    elInputAttrs: {
        disabled: true
    }
};
// 期望到货日期
const expectArrivalTime = {
    ...CompanyDeliveryTaskItems.expectArrivalTime,
    elInputAttrs: {
        disabled: true
    }
};
// 备注
const remark = {
    ...CompanyDeliveryTaskItems.remark,
    span: 24,
    elInputAttrs: {
        disabled: true
    }
};
const rules = {
    taskNameSuffix: [inputRequired('公司发货任务名称'), maxLength(64)],
    remark: [maxLength(255)]
};

// 表单项
const formItems = [
    stockRequestCode,
    taskSource,
    customerName,
    targetWarehouse,
    expectArrivalTime,
    taskNameSuffix,
    remark
];

// 表单数据
const formData = {
    stockRequestCode: '',
    taskSource: '',
    customerName: '',
    targetWarehouse: '',
    taskNameSuffix: '',
    expectArrivalTime: '',
    remark: ''
};

// 表单校验规则
const formConfig = {
    elFormAttrs: { rules }
};

export default {
    name: 'CompanyDeliveryTask',
    components: {
        SnbcTableList,
        SnbcForm,
        SnbcFormItem,
        SnbcCard,
        ChooseMaterialModal
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 统一UI风格
            elAttrs,
            elDialogAttrs: elAttrs.elDialogAttrs,
            // 弹窗模式 view | edit
            mode: '',
            // 弹窗显示
            dialogVisible: false,
            // 详情数据
            detail: null,
            // 表单数据
            form: { ...formData },
            // 表单校验规则
            formConfig,
            // 产品选项数据
            productNameOptions: [],
            // 缺少物料信息表格配置
            applyTableConfig: {
                elTableColumns: [
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '核定数量',
                        prop: 'requestNumber',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '未分解数量',
                        prop: 'notPlannedNumber',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: 'ERP公司库存',
                        prop: 'erpNumber',
                        show: true,
                        minWidth: 100
                    }
                ]
            },
            // 任务明细信息表格配置
            detailTableConfig: {
                elTableColumns: [
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 150
                    },
                    {
                        label: '物料编码',
                        prop: 'materialCode',
                        show: true,
                        minWidth: 150,
                        render(value) {
                            return value || '~';
                        }
                    },
                    {
                        label: '计划发货数量',
                        prop: 'productNumber',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: 'ERP搬运单号',
                        prop: 'erpCarryOrderNo',
                        show: true,
                        minWidth: 100,
                        render(value) {
                            return value || '~';
                        }
                    },
                    {
                        label: '公司仓库',
                        prop: 'comWarehouseName',
                        show: true,
                        minWidth: 100,
                        render(value) {
                            return value || '~';
                        }
                    }
                ]
            }
        };
    },
    computed: {
        // 表单项
        formItems() {
            return formItems.map((item) => {
                // 任务名称和备注，可以编辑
                if (['taskNameSuffix', 'remark'].includes(item.modelKey)) {
                    item.elInputAttrs.disabled = this.mode === 'view';
                }
                return {
                    modelObj: this.form,
                    elFormItemAttrs: {
                        label: item.name,
                        ...(item.elFormItemAttrs || {})
                    },
                    ...item
                };
            });
        },
        // 最大申请数量，上限未缺少物料中对应产品的为计划数量
        maxProductNum() {
            return (row) => {
                const target = this.detail.stockRequestDetailList.find((item) => item.productName === row.productName);
                // 未计划数量
                let notPlannedNumber = 0;
                if (target && target.notPlannedNumber) {
                    notPlannedNumber = target.notPlannedNumber;
                }
                // 相同产品线不同物料场景，未计划数量需要再减去其他物料申请数量
                const list = this.detail.detailPOList.filter(
                    (item) => item.productName === row.productName && item !== row
                );
                if (list.length > 0) {
                    const otherCount = list.reduce((val, item) => {
                        return val + item.productNumber;
                    }, 0);
                    notPlannedNumber -= otherCount;
                }
                // 未计划数量设置为上限值
                let maxNum = notPlannedNumber;
                // 仓库数量较小时设置区仓物料数量为上限值
                if (typeof row.number === 'number' && row.number >= 0 && row.number < target.notPlannedNumber) {
                    maxNum = row.number;
                }
                return maxNum;
            };
        },
        // 产品名称禁用项，已经选择的产品名称不可重复选择
        isDisabledProduct() {
            return (productName) => {
                return this.detail.detailPOList.map((item) => item.productName).includes(productName);
            };
        },
        // 弹窗任务明细，按钮名称
        buttonName() {
            return this.detail.orderType === 'carry' ? '' : '新增';
        }
    },
    methods: {
        // 展示弹窗
        openDialog(row, mode) {
            this.mode = mode;
            this.detail = null;
            this.elDialogAttrs = {
                ...elAttrs.elDialogAttrs,
                title: `${mode === 'view' ? '查看' : '编辑'}公司发货任务`,
                width: '1000px'
            };
            this.getTaskInfo(row.id);
            this.dialogVisible = true;
        },
        // 隐藏弹窗
        hideDialog() {
            this.dialogVisible = false;
        },
        // 查询任务详情
        async getTaskInfo(id) {
            try {
                const res = await this.$service.warehouse.comDeliveryTask.getInfoById(id);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.detail = res.result;
                this.form = { ...this.detail };
                this.productNameOptions = this.detail.stockRequestDetailList
                    .filter((item) => {
                        return item.notPlannedNumber > 0;
                    })
                    .map((item) => {
                        return item.productName;
                    });
            } catch (error) {
                this.$tools.message.err('系统异常');
            }
        },
        // 保存操作
        async handleSave() {
            // 基本信息表单校验
            try {
                const formRef = this.$refs.snbcFormRef.getFormRef();
                await this.$tools.validateForm(formRef);
            } catch (error) {
                const msg = '发货任务基本信息填写存在有误，请检查';
                this.$tools.message.warning(msg);
                return;
            }
            // 任务明细校验
            const index = this.detail.detailPOList.findIndex((item) => !item.productName || !item.productNumber);
            if (index > -1) {
                const msg = `任务明细第${index + 1}条数据填写不完整，请检查`;
                this.$tools.message.warning(msg);
                return;
            }
            if (this.detail.detailPOList.length === 0) {
                const msg = `请先添加任务明细`;
                this.$tools.message.warning(msg);
                return;
            }
            await this.$tools.confirm('确认保存码？');
            // 保存参数
            const params = {
                id: this.detail.id,
                stockRequestCode: this.detail.stockRequestCode,
                taskNamePrefix: this.form.taskNamePrefix,
                taskNameSuffix: this.form.taskNameSuffix,
                remark: this.form.remark,
                detailList: this.detail.detailPOList
            };
            const { updateTask } = this.$service.warehouse.comDeliveryTask;
            try {
                const res = await updateTask(params);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('保存成功');
                this.$emit('fresh');
                this.hideDialog();
            } catch (error) {
                this.$tools.message.err('系统异常');
            }
        },
        // 移除一项
        async handleRemove(index) {
            const { productName, productNumber } = this.detail.detailPOList[index];
            if (productName || productNumber) {
                await this.$tools.confirm('确认删除？');
            }
            this.detail.detailPOList.splice(index, 1);
        },
        // 新增任务明细
        async handleAdd() {
            this.detail.detailPOList.push({
                productName: '',
                productNumber: undefined
            });
        },
        // 物料替换弹窗展示
        showReplaceModal(index) {
            const { detailPOList, targetWarehouseCode, customerCode } = this.detail;
            const { id, productName, productNumber } = detailPOList[index];
            this.$refs.chooseMaterialModalRef.openDialog({
                id,
                customerCode,
                targetWarehouseCode,
                productName,
                productNumber,
                detailPOList,
                customerName: this.detail.customerName
            });
        },
        // 替换选择的物料
        handleReplace(row) {
            const { id, materialCode, subInventoryName, number } = row;
            const target = this.detail.detailPOList.find((item) => item.id === id);
            this.$set(target, 'materialCode', materialCode);
            this.$set(target, 'number', number);
            this.$set(target, 'comWarehouseName', subInventoryName);
            // 当选择的替换物料的库存小于该产品的当前申请数量时，设置申请数量为值为公司库存值
            if (number < target.productNumber) {
                this.$set(target, 'productNumber', number);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.m-b-10 {
    margin-bottom: 10px;
}
</style>
