<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig">
                <template #table-info-top>
                    <el-descriptions class="summary-info" :column="5" border>
                        <el-descriptions-item label="在库总数">10000</el-descriptions-item>
                        <el-descriptions-item label="新机在库">1000</el-descriptions-item>
                        <el-descriptions-item label="撤机在库">800</el-descriptions-item>
                        <el-descriptions-item label="干线入库在途">600</el-descriptions-item>
                        <el-descriptions-item label="调拨出库在途">500</el-descriptions-item>
                    </el-descriptions>
                </template>
            </snbc-base-table>
        </div>
    </div>
</template>

<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import FormItems from 'warehouse/common/form-items/index.js';

const { warehouseMultiSelect } = FormItems;

export default {
    name: 'WarehouseChartStatistics',
    components: {
        SnbcBaseTable
    },
    mixins: [functions],
    data() {
        const {
            getSummaryStatistics: listApi
        } = this.$service.warehouse.assets;
        return {
            listApi,
            tableConfig: {
                queryParams: {
                    warehouseSelect: []
                },
                queryConfig: {
                    items: [warehouseMultiSelect]
                },
                queryApi: listApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 120
                    }
                ],
                operations: [
                    {
                        name: '历史在库',
                        type: 'primary',
                        handleClick: this.handleHistory
                    },
                    {
                        name: '图表',
                        type: 'warning',
                        handleClick: this.handleChart
                    }
                ]
            }
        };
    },
    computed: {},
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.handleQuery();
    },
    methods: {
        // 历史在库操作
        handleHistory(row) {
            this.$router.push({ path: '/app/assets/history-statistics', query: { id: row.id }});
        },
        // 图表查看操作
        handleChart(row) {
            this.$router.push({ path: '/app/assets/chart-statistics', query: { id: row.id }});
        }
    }
};
</script>

<style lang="scss" scoped>
    .summary-info {
        margin-bottom: 10px;
        ::v-deep .el-descriptions-item__label {
            color: #000000;
        }
        ::v-deep .el-descriptions-item__content {
            font-weight: bold;
            font-size: 18px;
            color: #409eff;
        }
    }
</style>
