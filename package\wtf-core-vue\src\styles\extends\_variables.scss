/*----------------------------------------------------------------
// 功能说明：用于视图相关页面开发样式-变量定义
//
// 规范说明：
//   变量命名遵循：模块+属性+状态
//   格式：$--[模块]_[子模块]--[属性]_[子属性]--[状态]
//
// 例如：
//   视图页的按钮边框颜色：$--view_button--border_color:#ffffff;
//   视图页的按钮（主要按钮）背景颜色：$--view_button--bg--primary:#111111;
//
//----------------------------------------------------------------*/

@import '../element-variables.scss';

$--color--primary: $--color-primary;

// 视图页-区域--背景颜色
$--container--bg: #f4f5fa;
// 视图页-内容--背景颜色
$--view--bg: #ffffff;
// header 样式
$--header--bg: #f5f6fa;
$--border-color: #f5f6fa;

// 默认内容边距
$--padding-width: 20px;

// 视图页-内边距-默认宽度
$--view--padding-width: $--padding-width;
// 视图页-内容块间隔-默认宽度
$--view--split-width: 10px;

// 视图页-左侧--默认宽度
$--view_left--width: 300px;

// 按钮样式
$--button--bg--default: #fff;
$--button--bg--primary: linear-gradient(0deg, $--color--primary, #09c);
$--button--border-color: $--color--primary;

// 文本框
$--input--border--default: 1px solid #ececec;
$--input--border--hover: 1px solid #09c;
$--input--border--focus: 1px solid #09c;
