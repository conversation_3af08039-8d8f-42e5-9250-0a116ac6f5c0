<template>
    <el-table
        class="snbc-table"
        :data="tableData"
        v-bind="elTableAttrs"
        :cell-class-name="'snbc-table-cell'"
        :row-class-name="rowClassName"
        :span-method="spanMethod"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
    >
        <el-table-column
            v-if="config.selectionAble"
            type="selection"
            width="55"
            :selectable="selectable"
        />
        <el-table-column
            v-for="column in elTableColumns"
            :key="column.prop"
            :label="column.label"
            :prop="column.prop"
            v-bind="column.elTableColumnAttrs"
        >
            <template slot-scope="scope">
                <!-- 单元格内容渲染 -->
                <template v-if="!column.renderMode">
                    <span :class="cellClassName(column, scope.row)">{{
                        column.render
                            ? column.render(scope.row[column.prop], scope.row)
                            : scope.row[column.prop]
                    }}</span>
                </template>
                <!-- 单元格内容渲染为按钮 -->
                <el-button
                    v-else-if="column.renderMode === 'button'"
                    v-bind="
                        column.elButtonAttrsFn
                            ? column.elButtonAttrsFn(scope.row)
                            : column.elButtonAttrs || {}
                    "
                    @click.native.prevent="
                        column.handleClick && column.handleClick(scope.row)
                    "
                    >{{ scope.row[column.prop] }}</el-button
                >
                <!-- 单元格内容渲染为标签 -->
                <el-tag
                    v-else-if="column.renderMode === 'tag'"
                    v-bind="
                        column.elTagAttrsFn
                            ? column.elTagAttrsFn(scope.row)
                            : column.elTagAttrs || {}
                    "
                    >{{ scope.row[column.prop] }}</el-tag
                >
            </template>
        </el-table-column>
        <el-table-column
            v-if="operations.length > 0"
            fixed="right"
            align="center"
            label="操作"
            :width="operationColumnWidth"
        >
            <template slot-scope="scope">
                <template v-for="(operation, index) in operations">
                    <el-button
                        v-if="showOperation(operation, scope.row)"
                        :key="index"
                        :type="operation.type"
                        size="small"
                        @click.native.prevent="operation.handleClick(scope.row)"
                    >
                        {{ operation.name }}
                    </el-button>
                </template>
            </template>
        </el-table-column>
    </el-table>
</template>
<script>
export default {
    name: 'SnbcTableList',
    props: {
        /**
         * 列表配置项集合
         */
        config: {
            type: Object,
            default() {
                return {
                    // table属性
                    elTableAttrs: {},
                    // table事件
                    elTableListeners: {},
                    // table列
                    elTableColumns: [],
                    // table行操作
                    operations: [],
                    // 操作列宽度
                    operationColumnWidth: 0,
                    // 多选操作
                    selectionAble: false,
                    // 行样式-function
                    rowClassName: null,
                    // 合并行或列的计算方法-function
                    spanMethod: null,
                    // 表格行是否可选计算方法
                    selectable: null
                };
            }
        },
        /**
         * 列表数据
         */
        list: {
            type: Array,
            default() {
                return [];
            }
        }
    },
    data() {
        return {
            // el-table组件默认属性
            defaultElTableAttrs: {
                'border': true,
                'width': '100%',
                'header-cell-style': { background: '#F5F6FA' }
            },
            // el-table-column默认属性
            defaultElTableColumnAttrs: {
                'sortable': false,
                'align': 'center',
                'show-overflow-tooltip': true
            }
        };
    },
    computed: {
        // 列表数据
        tableData() {
            return this.list;
        },
        // el-table应用属性
        elTableAttrs() {
            return {
                ...this.defaultElTableAttrs,
                ...(this.config.elTableAttrs || {})
            };
        },
        // el-table-column应用属性
        elTableColumns() {
            return (this.config.elTableColumns || [])
                .map((column) => {
                    return {
                        ...column,
                        elTableColumnAttrs: {
                            'min-width': column.minWidth,
                            ...this.defaultElTableColumnAttrs,
                            ...(column.elTableColumnAttrs || {})
                        }
                    };
                })
                .filter((item) => item.show);
        },
        // 操作列配置
        operations() {
            return this.config.operations || [];
        },
        // 操作列宽度计算
        operationColumnWidth() {
            return (
                this.config.operationColumnWidth ||
                this.operations.reduce((total, column) => {
                    return total + (column.width || 80);
                }, 0)
            );
        },
        // 操作按钮展示控制
        showOperation() {
            return (operation, row) => {
                if (operation.handleShow) {
                    return operation.handleShow(row);
                }
                return true;
            };
        }
    },
    methods: {
        // 排序条件发生变化
        handleSortChange(column, prop, order) {
            this.$emit('sort-change', column, prop, order);
        },
        // 当选择项发生变化时触发
        handleSelectionChange(selections) {
            this.$emit('selection-change', selections);
        },
        // 表格行样式类
        rowClassName({ row, rowIndex }) {
            if (this.config.rowClassName) {
                return this.config.rowClassName({ row, rowIndex }) || '';
            }
        },
        // 表格单元格样式类
        cellClassName(column, row) {
            if (column.cellClassName) {
                return column.cellClassName(column, row);
            }
            return '';
        },
        // 合并行或列的计算方法
        spanMethod({ row, column, rowIndex, columnIndex }) {
            if (this.config.spanMethod) {
                return (
                    this.config.spanMethod({
                        row,
                        column,
                        rowIndex,
                        columnIndex
                    }) || {
                        rowspan: 0,
                        colspan: 0
                    }
                );
            }
        },
        // 仅对 type=selection 的列有效，Function 的返回值用来决定这一行的 CheckBox 是否可以勾选
        selectable(row, index) {
            if (this.config.selectable) {
                return this.config.selectable(row, index);
            }
            return true;
        }
    }
};
</script>
<style lang="scss" scoped>
.snbc-table {
    ::v-deep .el-table__row,
    ::v-deep .has-gutter {
        height: 40px !important;
    }
    ::v-deep .el-button {
        padding: 0 10px;
        height: 28px;
    }
    ::v-deep .el-table__header .el-table__cell {
        padding: 0;
        height: 40px !important;
    }
}
::v-deep .snbc-table-cell {
    padding: 0;
    button {
        user-select: text;
    }
}
</style>
