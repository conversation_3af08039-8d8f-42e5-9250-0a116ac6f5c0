import Vue from 'vue';
import VueI18n from 'vue-i18n';
import Cookies from 'js-cookie';
import enLocale from './en';
import zhLocale from './zh';
import elementEnLocale from 'element-ui/lib/locale/lang/en'; // element-ui lang
import elementZhLocale from 'element-ui/lib/locale/lang/zh-CN'; // element-ui lang

Vue.use(VueI18n);

const messages = {
    en: {
        ...enLocale,
        ...elementEnLocale
    },
    zh: {
        ...zhLocale,
        ...elementZhLocale
    }
};

export function getLanguage() {
	//  获取cookie中的langKey，如果存在，则展示存储的语言类型
	if (Cookies.get('langKey')) {
		let chooseLanguage =  Cookies.get('langKey');
		if(chooseLanguage === 'cn'){
			chooseLanguage = 'zh';
		}
		return chooseLanguage;

	}else{
		const chooseLanguage = Cookies.get('language');
		if (chooseLanguage) return chooseLanguage;
	
		// if has not choose language
		const language = (navigator.language || navigator.browserLanguage).toLowerCase();
		const locales = Object.keys(messages);
		for (const locale of locales) {
			if (language.indexOf(locale) > -1) {
				return locale;
			}
		}
		return 'en';
	}
}

const i18n = new VueI18n({
    // set locale
    // options: en | zh | es
    locale: getLanguage(),
    // set locale messages
    messages
});

Vue.prototype.generateTitle = (title) => {
    const hasKey = i18n.te('route.' + title);

    if (hasKey) {
        // $t :this method from vue-i18n, inject in @/lang/index.js
        const translatedTitle = i18n.t('route.' + title);

        return translatedTitle;
    }
    return title;
};

export function loadModuleMessage(moduleName, moduleMessage) {
    if (!moduleName) {
        console.error(
            `%c加载i18n的模块名称不能为空，请配置package.json中的moduleName字段的值`,
            'background: #4192d9; padding: 5px; color: #fff; border-radius: 5px'
        );
        return;
    }
    if (!moduleMessage) {
        console.error(
            `%c加载i18n的值不能为空`,
            'background: #4192d9; padding: 5px; color: #fff; border-radius: 5px'
        );
        return;
    }
    for (const lang_key in messages) {
        if (moduleMessage[lang_key]) {
            // 合并路由
            if (moduleMessage[lang_key]['route']) {
                // 检验路由是否重复，重复提示
                var repeatRoutes = this.$tools.getObjectRepeatItems(moduleMessage[lang_key]['route'], messages[lang_key]['route']);
                // 合并路由信息
                messages[lang_key]['route'] = {
                    ...messages[lang_key]['route'],
                    ...moduleMessage[lang_key]['route']
                };
            }
            // 删除项目模块路由信息
            delete moduleMessage[lang_key]['route'];

            // 合并httpCode国际化提示信息
            if (moduleMessage[lang_key]['httpCode']) {
                // 合并
                messages[lang_key]['httpCode'] = {
                    ...messages[lang_key]['httpCode'],
                    ...moduleMessage[lang_key]['httpCode']
                };
            }
            // 删除项目模块httpCode信息
            delete moduleMessage[lang_key]['httpCode'];

            const newObject = Object.assign(messages[lang_key], moduleMessage[lang_key]);
            i18n.setLocaleMessage(lang_key, newObject);
        }
    }
}

export default i18n;
