import moment from 'moment';

/**
 * 起止日期取值
 *
 * @param {String} type 日期模式
 * @returns {Array} 起止日期
 */
export function getDateRange(type) {
    if (type === '前一天') {
        const day = moment().subtract(1, 'days');
        const start = day.toDate();
        const end = day.toDate();
        return [start, end];
    }
    if (type === '本周') {
        const start = moment().startOf('weeks').toDate();
        const end = moment().endOf('weeks').toDate();
        return [start, end];
    }
    if (type === '上周') {
        const day = moment().subtract(7, 'days');
        const start = day.startOf('weeks').toDate();
        const end = day.endOf('weeks').toDate();
        return [start, end];
    }
    if (type === '本月') {
        const start = moment().startOf('month').toDate();
        const end = moment().endOf('month').toDate();
        return [start, end];
    }
    if (type === '本季度') {
        const start = moment().startOf('quarter').toDate();
        const end = moment().endOf('quarter').toDate();
        return [start, end];
    }
    if (type === '前一月') {
        const month = moment(new Date()).subtract(1, 'months');
        const start = month.startOf('month').toDate();
        const end = month.endOf('month').toDate();
        return [start, end];
    }
    if (type === '前一季度') {
        const quarter = moment().quarter(moment().quarter() - 1);
        const start = quarter.startOf('quarter').toDate();
        const end = quarter.endOf('quarter').toDate();
        return [start, end];
    }
    if (type === '上半年') {
        const year = new Date().getFullYear();
        const start = new Date(year, 0, 1);
        const end = new Date(year, 5, 30);
        return [start, end];
    }
    if (type === '下半年') {
        const year = new Date().getFullYear();
        const start = new Date(year, 6, 1);
        const end = new Date(year, 11, 31);
        return [start, end];
    }
    if (type === '本年度') {
        const year = new Date().getFullYear();
        const start = new Date(year, 0, 1);
        const end = new Date(year, 11, 31);
        return [start, end];
    }
    if (type === '上一年度') {
        const year = new Date().getFullYear() - 1;
        const start = new Date(year, 0, 1);
        const end = new Date(year, 11, 31);
        return [start, end];
    }
}

export const lastMonthOption = {
    text: '前一月',
    onClick(picker) {
        picker.$emit('pick', getDateRange('前一月'));
    }
};
export const lastQuarterOption = {
    text: '前一季度',
    onClick(picker) {
        picker.$emit('pick', getDateRange('前一季度'));
    }
};
export const firstHalfYearOption = {
    text: '上半年',
    onClick(picker) {
        picker.$emit('pick', getDateRange('上半年'));
    }
};
export const secondHalfYearOption = {
    text: '下半年',
    onClick(picker) {
        picker.$emit('pick', getDateRange('下半年'));
    }
};
export const currentYearOption = {
    text: '本年度',
    onClick(picker) {
        picker.$emit('pick', getDateRange('本年度'));
    }
};
export const lastYearOption = {
    text: '上一年度',
    onClick(picker) {
        picker.$emit('pick', getDateRange('上一年度'));
    }
};
export const yesterdayOption = {
    text: '前一天',
    onClick(picker) {
        picker.$emit('pick', getDateRange('前一天'));
    }
};
export const currentWeekOption = {
    text: '本周',
    onClick(picker) {
        picker.$emit('pick', getDateRange('本周'));
    }
};
export const lastWeekOption = {
    text: '上周',
    onClick(picker) {
        picker.$emit('pick', getDateRange('上周'));
    }
};
export const currentMonthOption = {
    text: '本月',
    onClick(picker) {
        picker.$emit('pick', getDateRange('本月'));
    }
};
export const currentQuarterOption = {
    text: '本季度',
    onClick(picker) {
        picker.$emit('pick', getDateRange('本季度'));
    }
};

export default {
    getDateRange,
    lastMonthOption,
    lastQuarterOption,
    firstHalfYearOption,
    secondHalfYearOption,
    currentYearOption,
    lastYearOption,
    yesterdayOption,
    currentWeekOption,
    lastWeekOption,
    currentMonthOption,
    currentQuarterOption
};
