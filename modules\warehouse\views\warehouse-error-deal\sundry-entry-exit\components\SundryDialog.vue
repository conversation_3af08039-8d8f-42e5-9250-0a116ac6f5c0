<template>
    <el-dialog
        class="custom-dialog"
        :visible.sync="dialogVisible"
        v-bind="elDialogAttrs"
        :width="'900px'"
        @close="hideDialog"
        :destroy-on-close="true"
    >
        <slot name="dialog-body" />
        <span v-if="hasFooter" slot="footer" class="dialog-footer">
            <el-button
                v-if="config.operations.length && config.operations.length < 2"
                type="danger"
                @click="hideDialog"
                >取消</el-button
            >
            <el-button
                v-for="operation in config.operations"
                :key="operation.name"
                :type="operation.type"
                @click.native.prevent="operation.handleClick"
                >{{ operation.name }}</el-button
            >
        </span>
    </el-dialog>
</template>
<script>
export default {
    name: 'SundryDialog',
    props: {
        /**
         * SnbcBaseDialog组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    elDialogAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // 弹窗显示
            dialogVisible: false,
            // 弹窗默认属性
            defaultElDialogAttrs: {
                title: '信息弹窗',
                width: '800px'
            }
        };
    },
    computed: {
        // 实际弹窗属性
        elDialogAttrs() {
            return {
                ...this.defaultElDialogAttrs,
                ...(this.config.elDialogAttrs || {})
            };
        },
        // 弹窗底部按钮操作区
        hasFooter() {
            if (
                this.config.hasFooter === false ||
                !this.config.operations ||
                !this.config.operations.length
            ) {
                return false;
            }
            return true;
        },
        // 取消操作时自动隐藏弹窗
        autoHideOnCancel() {
            if (this.config.autoHideOnCancel === false) {
                return false;
            }
            return true;
        }
    },
    methods: {
        // 打开弹窗
        openDialog() {
            this.dialogVisible = true;
        },
        // 隐藏弹窗
        hideDialog() {
            this.$parent.handleDialogClose();
            this.$nextTick(() => {
                this.dialogVisible = false;
            });
        }
    }
};
</script>
<style lang="scss" scoped></style>
