import Vue from 'vue';
import Element from 'element-ui';
import Cookies from 'js-cookie';
import App from './App';
import Meta from 'vue-meta';
import methods from './methods';
import permission from './router/permission.js';
import store from './store';
import i18n from './lang';
import './utils/error-log'; // error log
import './icons'; // icon

import './styles/index.scss'; // global css
import 'normalize.css/normalize.css'; // a modern alternative to CSS resets
import './styles/element-variables.scss';

// 调试先这样写，加快编译速度
import './styles/extends/_base.scss';
import './styles/extends/_layout.scss';
import './styles/extends/_modules_element-ui.scss';
import './styles/extends/_state.scss';
import './styles/extends/index.scss';
// 引用font-awesome图标库
import 'font-awesome/css/font-awesome.css';

import Components from './components';
import * as filters from './filters';
import directives from './directive';
// 导入静态变量
import { constant } from './methods/module-constant';
import router, { setLayout, defaultRouterHandler } from './router';

// 存储模块对象
const addedModules = new Set();

Vue.config.devtools = true;
Vue.config.productionTip = false;

Vue.use(Element, {
    size: Cookies.get('size') || 'medium', // set element-ui default size
    i18n: (key, value) => i18n.t(key, value)
});

Vue.use(Components, {
    size: Cookies.get('size') || 'medium', // set Components default size
    i18n: (key, value) => i18n.t(key, value)
});

// register global utility filters
Object.keys(filters).forEach((key) => {
    Vue.filter(key, filters[key]);
});

Vue.use(directives);

class WtfCoreVue {
    constructor(config) {
        if (config.layoutName) {
            setLayout(config.layoutName);
        }

        if (config.settings) {
            store.dispatch('settings/changeSetting', config.settings);
        }

        // ====设置element组件全局默认属性值===============
        // 弹窗组件--设置点击背景不允许关闭
        Element.Dialog.props.closeOnClickModal.default = false;
        Element.Tooltip.props.openDelay.default = 300;
        Element.Tooltip.props.transition.default = '';
        Element.Form.props.labelWidth.default = '120px';
        Element.Input.props.clearable.default = true;
        // 设置配置的默认多语言
        const langLocal = (config.settings.lang || {}).default;
        if (langLocal) {
            i18n.locale = langLocal;
            store.dispatch('app/setLanguage', langLocal);
        }

        this.context = {};
        this.context.config = config;
        this.context.Vue = Vue;
        this.context.i18n = i18n;
        this.context.router = router;
        this.context.store = store;
        this.context.Vue.use(Meta);

        methods(this.context);

        // 循环处理模块
        for (const moduleInit of addedModules) {
            moduleInit(this.context);
        }
        // 静态变量绑定到Vue原型上
        this.context.Vue.prototype.$const = constant;
        // 处理无需权限过滤的路由
        defaultRouterHandler(store.state.settings.permissionSettings);
        // 处理权限
        permission(router, store);

        return new Vue({
            el: '#app',
            router: this.context.router,
            store: this.context.store,
            i18n: this.context.i18n,
            render: (h) => h(App)
        });
    }
    static add(moduleInit) {
        addedModules.add(moduleInit);
    }
    static use(plugin, options) {
        if (options) {
            Vue.use(plugin, options);
        } else {
            Vue.use(plugin, {
                size: Cookies.get('size') || 'medium', // set element-ui default size
                i18n: (key, value) => i18n.t(key, value)
            });
        }
    }
}

export default WtfCoreVue;
