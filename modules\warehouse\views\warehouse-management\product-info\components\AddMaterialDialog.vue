<template>
    <el-dialog class="custom-dialog" :visible.sync="dialogVisible" v-bind="dialogConfig" @close="handleClose">
        <template>
            <div>
                <SnbcForm ref="snbcFormRef" :form="form" :config="config">
                    <template slot="form-body">
                        <snbc-form-item v-for="(item, index) in formItems" :key="index" :config="item" />
                    </template>
                </SnbcForm>
                <div class="material-remark">
                    {{ materialRemark }}
                </div>
            </div>
        </template>
        <span slot="footer" class="dialog-footer">
            <el-button :disabled="!materialRemark" type="primary" @click="handleConfirm">绑定</el-button>
        </span>
    </el-dialog>
</template>
<script>
import SnbcForm from 'warehouse/components/snbc-form/SnbcForm.vue';
import SnbcFormItem from 'warehouse/components/snbc-form/SnbcFormItem.vue';
import FormRules from 'warehouse/common/form-rules/index.js';
import FormItems from 'warehouse/common/form-items/index.js';

const { input } = FormItems;
const { inputRequired } = FormRules;

const formItems = [
    {
        ...input,
        name: '物料编码',
        modelKey: 'materialCode'
    }
];
export default {
    name: 'AddMaterialDialog',
    components: {
        SnbcForm,
        SnbcFormItem
    },
    data() {
        return {
            dialogConfig: Object.freeze({
                'title': '物料编码绑定',
                'width': '600px',
                'append-to-body': true
            }),
            dialogVisible: false,
            productId: '',
            form: {
                materialCode: ''
            },
            config: {
                elFormAttrs: {
                    'label-width': '120px',
                    'rules': {
                        materialCode: [inputRequired('物料编码')]
                    }
                }
            },
            materialRemark: ''
        };
    },
    computed: {
        formItems() {
            return formItems.map((item) => {
                return {
                    modelObj: this.form,
                    elFormItemAttrs: {
                        label: item.name,
                        ...(item.elFormItemAttrs || {})
                    },
                    ...item,
                    handleBlur: this.handleBlur
                };
            });
        }
    },
    watch: {
        'form.materialCode': function () {
            this.materialRemark = '';
        }
    },
    methods: {
        showDialog(row) {
            this.productId = row.id;
            this.dialogVisible = true;
        },
        handleClose() {
            this.form.materialCode = '';
            this.materialRemark = '';
            this.$refs.snbcFormRef.getFormRef().resetFields();
            this.$nextTick(() => {
                this.dialogVisible = false;
            });
        },
        async handleBlur() {
            if (!this.form.materialCode) return;
            const { code, result, message } = await this.$service.warehouse.product.selectMaterialByCode({
                materialCode: this.form.materialCode
            });
            if (code !== '000000') {
                this.$message.error(message);
                return;
            }
            this.materialRemark = result.materialRemark;
        },
        // add a new relationship between product and material
        async handleConfirm() {
            await this.$tools.confirm(
                `新增绑定的物料会应用在后续新资产中，但历史资产数据不会更改，如需更改，请联系项目组。`
            );
            const { code, message } = await this.$service.warehouse.product.bindMaterialToProduct({
                productId: this.productId,
                materialCode: this.form.materialCode,
                materialRemark: this.materialRemark
            });
            if (code !== '000000') {
                this.$message.error(message);
                return;
            }
            this.$message.success('绑定成功');
            this.handleClose();
            this.$emit('handleQuery');
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .el-input--medium {
    width: 60%;
}
.material-remark {
    width: 80%;
    margin: auto;
}
</style>
