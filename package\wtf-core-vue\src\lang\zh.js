export default {
    route: {
        dashboard: '首页',
        documentation: '文档',
        guide: '引导页',
        permission: '权限测试页',
        rolePermission: '角色权限',
        pagePermission: '页面权限',
        directivePermission: '指令权限',
        icons: '图标',
        components: '组件',
        tinymce: '富文本编辑器',
        markdown: 'Markdown',
        jsonEditor: 'JSON 编辑器',
        dndList: '列表拖拽',
        splitPane: 'Splitpane',
        avatarUpload: '头像上传',
        dropzone: 'Dropzone',
        sticky: 'Sticky',
        countTo: 'Count To',
        componentMixin: '小组件',
		
        dragDialog: '拖拽 Dialog',
        dragSelect: '拖拽 Select',
        dragKanban: '可拖拽看板',
        charts: '图表',
        keyboardChart: '键盘图表',
        lineChart: '折线图',
        mixChart: '混合图表',
        example: '综合实例',
        nested: '路由嵌套',
        menu1: '菜单1',
        'menu1-1': '菜单 1-1',
        'menu1-2': '菜单 1-2',
        'menu1-2-1': '菜单 1-2-1',
        'menu1-2-2': '菜单 1-2-2',
        'menu1-3': '菜单 1-3',
        menu2: '菜单 2',
        Table: 'Table',
        dynamicTable: '动态 Table',
        dragTable: '拖拽 Table',
        inlineEditTable: 'Table 内编辑',
        complexTable: '综合 Table',
        tab: 'Tab',
        form: '表单',
        createArticle: '创建文章',
        editArticle: '编辑文章',
        articleList: '文章列表',
        errorPages: '错误页面',
        page401: '401',
        page404: '404',
        errorLog: '错误日志',
        excel: 'Excel',
        exportExcel: '导出 Excel',
        selectExcel: '导出 已选择项',
        mergeHeader: '导出 多级表头',
        uploadExcel: '上传 Excel',
        zip: 'Zip',
        pdf: 'PDF',
        exportZip: 'Export Zip',
        theme: '换肤',
        clipboardDemo: 'Clipboard',
        i18n: '国际化',
        externalLink: '外链',
        profile: '个人中心'
    },
    table: {
        dynamicTips1: '固定表头, 按照表头顺序排序',
        dynamicTips2: '不固定表头, 按照点击顺序排序',
        dragTips1: '默认顺序',
        dragTips2: '拖拽后顺序',
        title: '标题',
        importance: '重要性',
        type: '类型',
        remark: '点评',
        search: '搜索',
        add: '添加',
        export: '导出',
        reviewer: '审核人',
        id: '序号',
        date: '时间',
        author: '作者',
        readings: '阅读数',
        status: '状态',
        actions: '操作',
        edit: '编辑',
        publish: '发布',
        draft: '草稿',
        delete: '删除',
        cancel: '取 消',
        confirm: '确 定'
    },
    navbar: {
        dashboard: '首页',
        github: '项目地址',
        logOut: '退出登录',
        profile: '基本信息',
        changePWD: '修改密码',
        theme: '换肤',
        size: '布局大小',
        search: '搜索菜单',
        sizeChange: '布局大小调整成功'
    },
    documentation: {
        documentation: '文档',
        github: 'Github 地址'
    },
    permission: {
        addRole: '新增角色',
        editPermission: '编辑权限',
        roles: '你的权限',
        switchRoles: '切换权限',
        tips: '在某些情况下，不适合使用 v-permission。例如：Element-UI 的 el-tab 或 el-table-column 以及其它动态渲染 dom 的场景。你只能通过手动设置 v-if 来实现。',
        delete: '删除',
        confirm: '确定',
        cancel: '取消'
    },
    guide: {
        description: '引导页对于一些第一次进入项目的人很有用，你可以简单介绍下项目的功能。本 Demo 是基于',
        button: '打开引导'
    },
    example: {
        warning: '创建和编辑页面是不能被 keep-alive 缓存的，因为keep-alive 的 include 目前不支持根据路由来缓存，所以目前都是基于 component name 来进行缓存的。如果你想类似的实现缓存效果，可以使用 localStorage 等浏览器缓存方案。或者不要使用 keep-alive 的 include，直接缓存所有页面。详情见'
    },
    errorLog: {
        tips: '请点击右上角bug小图标',
        description: '现在的管理后台基本都是spa的形式了，它增强了用户体验，但同时也会增加页面出问题的可能性，可能一个小小的疏忽就导致整个页面的死锁。好在 Vue 官网提供了一个方法来捕获处理异常，你可以在其中进行错误处理或者异常上报。',
        documentation: '文档介绍'
    },
    excel: {
        export: '导出',
        selectedExport: '导出已选择项',
        placeholder: '请输入文件名(默认excel-list)'
    },
    zip: {
        export: '导出',
        placeholder: '请输入文件名(默认file)'
    },
    pdf: {
        tips: '这里使用   window.print() 来实现下载pdf的功能'
    },
    theme: {
        change: '换肤',
        documentation: '换肤文档',
        tips: 'Tips: 它区别于 navbar 上的 theme-pick, 是两种不同的换肤方法，各自有不同的应用场景，具体请参考文档。'
    },
    tagsView: {
        refresh: '刷新',
        close: '关闭',
        closeOthers: '关闭其它',
        closeAll: '关闭所有'
    },
    settings: {
        title: '系统布局配置',
        theme: '主题色',
        tagsView: '开启 Tags-View',
        fixedHeader: '固定 Header',
        sidebarLogo: '侧边栏 Logo'
    },
    common: {
        success: '成功',
        failed: '失败',
        downloadT: '下载',
        uploadT: '上传',
        tips: '提示',
        application: '应用：',
        searchHeader: '按条件查询',
        searchBtn: "查询",
        resetBtn: "重置",
        highSearch: "高级查询",
        queryList: "查询列表",
        add: "新增",
        edit: "编辑",
        preview: "预览",
        detail: "查看详情",
        upload: "导入",
        delete: "删除",
        download: "下载模板",
        chooseOrg: "选择组织机构",
        chooseArea: "选择地域",
        choosePower: "选择权限",
        addPerson: "创建人员",
        addUser: "创建用户",
        bindRegion: "绑定地域",
        freeze: "冻结",
        unfreeze: "解冻",
        passwordReset: "密码重置",
        enabled: "启用",
        notUse: "禁用",
        addDicType: "新增字典类型",
        sendAgain: "重新发送",
        disabled: "不刷新",
        save: "保存",
        return: "返回",
        done: "确定",
        cancel: "取消",
        choose: "请选择",
        returnTop: "返回顶部",
        filter: "过滤：",
        settings: "设置",
        profile: "修改密码",
        logout: "退出",
        help: "帮助",
        notifications: "条消息",
        seeAll: "查看更多信息",
        have: "你有",
        fixHeader: "固定头部",
        fixAside: "固定菜单",
        foldAside: "折叠菜单",
        dockAside: "头部菜单",
        boxLayout: "盒装布局",
        No: "序号",
        handle: "操作",
        uploadFile: "导入文件",
        placeholderFile: "请选择上传文件：",
        uploadAction: "上传附件",
        column: "列可选",
        close: "关闭",
        clear: "清空",
        today: "今天",
        definedList: "自定义列表",
        deleteTip: "你确定要删除该项吗？",
        deleteChild: "请先删除子菜单！",
        chooseRolePerm: "请选择角色权限！",
        chooseOneOrg: "请选择一项组织机构！",
        chooseOneOrEditOrg: "请选择/修改一项组织机构！",
        chooseOneOpt: "请选择一项操作!",
        parentMenu: "父级菜单名称",
        addChildren: "添加下一级",
        order: "调整顺序",
        dragOrder: "请拖动调整顺序!!!",
        lastStep: "上一步",
        nextStep: "下一步",
        total: "共",
        count: "",
        currentPage: "当前显示",
        welcome: "欢迎使用本平台",
        pageNoPerssion: "尊敬的用户，您暂时没有该功能权限",
        noPerssionLinkAdm: "如果需要请联系您的管理员为您开放该功能权限",
        noTokenTip: "登录失效，请退出重新登录！",
        noNetWork: "当前网络不可用，请检查你的网络设置",
        changeLanguage: "已成功切换为中文",
        interfaceFailed: "接口调用异常"
    },
    httpCode: {
        http401: "401无权限--核心",
        http404: "404找不到页面--核心!!!"
    }
};
