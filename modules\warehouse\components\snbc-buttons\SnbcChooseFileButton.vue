<template>
    <el-upload
        ref="upload"
        action="demo"
        :show-file-list="false"
        :auto-upload="false"
        :accept="accept"
        :on-change="handleChange"
    >
        <el-button size="small" type="primary">{{ name }}</el-button>
    </el-upload>
</template>
<script>
export default {
    name: 'SnbcChooseFileButton',
    props: {
        // 导入按钮
        name: {
            type: String,
            default() {
                return '导入';
            }
        },
        // 文件类型 默认excel
        accept: {
            type: String,
            default() {
                return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel';
            }
        }
    },
    data() {
        return {};
    },
    methods: {
        // 出发选择文件
        handleChooseFile() {
            this.$refs.upload.$refs['upload-inner'].handleClick();
        },
        // 文件变化
        handleChange(file) {
            this.$emit('file', file.raw);
            this.$refs.upload.clearFiles();
        }
    }
};
</script>
<style lang="scss" scoped></style>
