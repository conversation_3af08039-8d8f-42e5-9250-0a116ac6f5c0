<template>
    <div class="view">
        <!-- 公司发货任务列表 -->
        <div class="content query-label-line2">
            <snbc-base-table ref="tableRef" :table-config="tableConfig">
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
            </snbc-base-table>
        </div>
        <!-- 公司发货任务显示/编辑弹窗 -->
        <company-delivery-task ref="companyDeliveryTaskRef" @fresh="handleQuery" />
        <TaskAdditionalForm ref="taskAdditionalFormRef"> </TaskAdditionalForm>
    </div>
</template>

<script>
import Vue from 'vue';
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcTableTabs from 'warehouse/components/snbc-table/SnbcTableTabs.vue';
import CompanyDeliveryTaskItems from 'warehouse/common/form-items/company-delivery-items.js';
import CompanyDeliveryTask from './components/CompanyDeliveryTask.vue';
import TaskAdditionalForm from 'warehouse/views/warehouse-error-deal/task-additional/components/TaskAdditionalForm';

const { cloneDeep } = Vue.prototype.$tools;

const {
    companyDeliveryTaskCode,
    companyDeliveryTaskName,
    stockRequestCode,
    targetWarehouse,
    customerName,
    companyDeliveryTaskState,
    erpOrderState
} = CompanyDeliveryTaskItems;

// 查询参数
const queryParams = {
    taskCode: '',
    taskName: '',
    stockRequestCode: '',
    targetWarehouse: '',
    customerName: '',
    // 任务状态
    taskState: '',
    // 订单状态
    erpOrderState: ''
};

// 查询区域配置项
const queryConfigItems = [
    companyDeliveryTaskCode,
    companyDeliveryTaskName,
    stockRequestCode,
    targetWarehouse,
    customerName,
    cloneDeep(companyDeliveryTaskState),
    erpOrderState
];

// 标签页配置
const tabsConfig = {
    activeName: '未推送',
    tabItems: ['未推送', '进行中', '已完成']
};

export default {
    name: 'CompanyDeliveryManagementList',
    components: {
        SnbcBaseTable,
        SnbcTableTabs,
        CompanyDeliveryTask,
        TaskAdditionalForm
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 标签页配置
            tabsConfig: Object.assign(tabsConfig, {
                handleTabClick: this.handleTabClick
            }),
            // 列表配置
            tableConfig: {
                queryParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: this.$service.warehouse.comDeliveryTask.getList,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '公司发货任务编号',
                        prop: 'taskCode',
                        show: true,
                        minWidth: 220,
                        renderMode: 'button',
                        elButtonAttrs: {
                            type: 'text'
                        },
                        handleClick: this.handleView
                    },
                    {
                        label: '公司发货任务名称',
                        prop: 'taskName',
                        show: true,
                        minWidth: 300
                    },
                    {
                        label: '目标区仓',
                        prop: 'targetWarehouse',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '客户名称',
                        prop: 'customerName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '任务来源',
                        prop: 'taskSource',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '备货申请任务编号',
                        prop: 'stockRequestCode',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '任务状态',
                        prop: 'taskStateName',
                        show: true,
                        minWidth: 120,
                        renderMode: 'tag',
                        elTagAttrsFn(row) {
                            return {
                                type: {
                                    SEND: 'success',
                                    DELETE: 'danger',
                                    NEW: 'warning'
                                }[row.taskState]
                            };
                        }
                    },
                    {
                        label: '已发货数量',
                        prop: 'shipmentNumber',
                        show: true,
                        minWidth: 100,
                        renderMode: 'button',
                        handleClick: this.handleShipments,
                        elButtonAttrs: {
                            type: 'text'
                        }
                    },
                    {
                        label: '已入库数量',
                        prop: 'arrivalNumber',
                        show: true,
                        minWidth: 100,
                        renderMode: 'button',
                        handleClick: this.handleArrival,
                        elButtonAttrs: {
                            type: 'text'
                        }
                    },
                    {
                        label: '计划发货数量',
                        prop: 'productTotalNumber',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '搬运订单状态',
                        prop: 'erpOrderState',
                        show: true,
                        minWidth: 120,
                        render(value) {
                            return value || '~';
                        }
                    },
                    {
                        label: '订单类型',
                        prop: 'orderTypeName',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '创建时间',
                        prop: 'createTime',
                        show: true,
                        minWidth: 160
                    }
                ],
                operations: [
                    {
                        name: '补录',
                        type: 'warning',
                        handleClick: this.handleAddRecord,
                        handleShow: (row) => {
                            return this.checkPermission('COMPANY_DELIVERY_ADD_RECORD') && row.taskState === 'SEND';
                        }
                    },
                    {
                        name: '编辑',
                        type: 'primary',
                        handleClick: this.handleEdit,
                        handleShow: (row) => {
                            return row.taskState === 'NEW' && this.checkPermission('COMPANY_DELIVERY_EDIT');
                        }
                    },
                    {
                        name: '推送',
                        type: 'success',
                        handleClick: this.handleSend,
                        handleShow: (row) => {
                            return row.taskState === 'NEW' && this.checkPermission('COMPANY_DELIVERY_SEND');
                        }
                    },
                    {
                        name: '删除',
                        type: 'danger',
                        handleClick: this.handleDelete,
                        handleShow: (row) => {
                            return row.taskState === 'NEW' && this.checkPermission('COMPANY_DELIVERY_DELETE');
                        }
                    }
                ],
                operationColumnWidth: 200,
                hooks: {
                    queryParamsHook: this.queryParamsHook,
                    tableListHook: this.tableListHook
                }
            }
        };
    },
    watch: {
        // 切换页签，任务状态查询条件下拉项变更
        'tabsConfig.activeName': {
            handler(newVal) {
                // 查询条件配置项
                const queryItems = this.tableConfig.queryConfig.items;
                // 任务状态查询条件配置
                const target = queryItems.find((item) => item.modelKey === 'taskState');
                // 任务状态下拉数据-完整的
                const options = companyDeliveryTaskState.elOptions;
                let elOptions = [];
                // 筛选匹配的下拉数据
                if (newVal === '未推送') {
                    elOptions = options.filter((option) => ['NEW'].includes(option.value));
                } else if (newVal === '进行中') {
                    // 已推送、已发货、入库中
                    elOptions = options.filter((option) => ['SEND', 'DELIVERED', 'STORAGE'].includes(option.value));
                } else if (newVal === '已完成') {
                    elOptions = options.filter((option) => ['DONE'].includes(option.value));
                }
                this.$set(target, 'elOptions', elOptions);
            },
            immediate: true
        }
    },
    created() {
        const { stockRequestCode: requestCode } = this.$route.query;
        this.tableConfig.queryParams.stockRequestCode = requestCode;
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.handleQuery();
    },
    methods: {
        // 标签切换，置空任务状态查询条件，根据tab禁用部分选项
        handleTabClick() {
            this.$set(this.tableConfig.queryParams, 'taskState', '');
            this.handleQuery();
        },
        // 列表查询操作
        handleQuery() {
            this.$refs.tableRef.queryList();
        },
        // 查询条件处理
        queryParamsHook(params) {
            const { activeName } = this.tabsConfig;
            if (activeName === '未推送') {
                params.taskState = 'NEW';
            }
            if (activeName === '进行中') {
                params.taskState = params.taskState || 'DOING';
            }
            if (activeName === '已完成') {
                params.taskState = 'DONE';
            }
        },
        // 列表数据处理
        tableListHook(list) {
            list.map((item) => {
                item.taskStateName = this.$tools.getOptionName(item.taskState, companyDeliveryTaskState.elOptions);
                item.shipmentNumber = item.orderTypeName === '销售订单' ? '~' : item.shipmentNumber;
                return item;
            });
        },
        // 查看操作
        handleView(row) {
            const data = this.$tools.cloneDeep(row);
            this.$refs.companyDeliveryTaskRef.openDialog(data, 'view');
        },
        // 发货清单
        handleViewRecord({ taskCode, taskName }) {
            this.$router.push({
                path: '/app/company-delivery/delivery-list',
                query: { taskCode, taskName }
            });
        },
        // 编辑操作
        handleEdit(row) {
            const data = this.$tools.cloneDeep(row);
            this.$refs.companyDeliveryTaskRef.openDialog(data, 'edit');
        },
        // 推送操作
        async handleSend(item) {
            // 销售单提示
            let msg = '推送后系统将向您邮箱发送邮件，请注意查收检查并转发给商务人员。确认推送？';
            if (item.orderType === 'carry') {
                msg = '系统将该任务推送至ERP生成搬运单，并向您邮箱发送任务明细，请查收。';
            }
            await this.$tools.confirm(msg);
            const { sendTask } = this.$service.warehouse.comDeliveryTask;
            try {
                const res = await sendTask(item.id);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc(message);
                this.handleQuery();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 删除操作
        async handleDelete(item) {
            await this.$tools.confirm('确认删除？');
            const { deleteTaskById } = this.$service.warehouse.comDeliveryTask;
            try {
                const res = await deleteTaskById(item.id);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('删除成功');
                this.handleQuery();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 补录
        handleAddRecord(item) {
            this.$refs.taskAdditionalFormRef.openDialog(
                {
                    baseInfo: {
                        ...item,
                        associatedTaskCode: item.taskCode,
                        taskAdditionalType: '入库',
                        warehouseName: item.targetWarehouse,
                        warehouseCode: item.targetWarehouseCode,
                        associatedTaskName: item.taskName,
                        source: '人工补录',
                        associatedTaskType: '公司发货'
                    },
                    additionalRecords: [],
                    additionalLogs: []
                },
                'add'
            );
        },
        // 发货数量
        handleShipments(item) {
            if (typeof item.shipmentNumber === 'number') {
                this.$router.push({
                    path: '/app/company-delivery/assets-list',
                    query: {
                        taskCode: item.taskCode
                    }
                });
            }
        },
        // 入库数量
        handleArrival(item) {
            this.$router.push({
                path: '/app/assets/operate-log',
                query: {
                    associatedTaskCode: item.taskCode,
                    operationType: '入库'
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__content {
    padding: 3px 0 0 0;
}
</style>
