<template>
    <div class="view">
        <div class="content">
            <snbc-base-table
                ref="tableRef"
                :table-config="tableConfig"
            ></snbc-base-table>
            <snbc-base-form-dialog
                ref="dialogRef"
                :config="dialogConfig"
                @submit="handleSubmit"
            />
        </div>
    </div>
</template>
<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcBaseFormDialog from 'warehouse/components/snbc-dialog/SnbcBaseFormDialog.vue';

const applyName = {
    name: '申请名称',
    component: 'SnbcFormInput',
    modelKey: 'applyName'
};

const applyCode = {
    name: '申请单号',
    component: 'SnbcFormInput',
    modelKey: 'applyCode'
};

const state = {
    name: '状态',
    component: 'SnbcFormSelect',
    modelKey: 'state',
    elOptions: [
        { label: '草稿', value: '1' },
        { label: '退回', value: '2' },
        { label: '待审核', value: '3' },
        { label: '完成', value: '4' }
    ]
};

const productName = {
    name: '产品名称',
    component: 'SnbcFormInput',
    modelKey: 'productName'
};

const assetsCode = {
    name: '产品序列号',
    component: 'SnbcFormInput',
    modelKey: 'assetsCode'
};

const queryParams = {
    applyCode: '',
    applyName: '',
    state: '',
    productName: ''
};

export default {
    name: 'CorrectManagement',
    components: {
        SnbcBaseTable,
        SnbcBaseFormDialog
    },
    minins: [functions],
    data() {
        return {
            tableConfig: {
                queryParams: { ...queryParams },
                queryConfig: {
                    items: [
                        applyName,
                        applyCode,
                        state,
                        productName,
                        assetsCode
                    ]
                },
                queryApi: this.$service.warehouse.orderAlias.list,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '申请单号',
                        prop: 'applyCode',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '申请名称',
                        prop: 'applyName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '区仓',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '产品序列号',
                        prop: 'assetsCode',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '修改类型',
                        prop: 'operationType',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '修改申请人',
                        prop: 'createName',
                        show: true,
                        minWidth: 160
                    },
                    { label: '状态', prop: 'state', show: true, minWidth: 160 }
                ],
                operations: [],
                headerButtons: [
                    {
                        name: '添加',
                        type: 'primary',
                        handleClick: this.handleClickAdd
                    }
                ]
            }
        };
    },
    computed: {},
    mounted() {
        this.init();
    },
    methods: {
        init() {
            this.isHasMenu();
            this.initNativeDrop();
            this.initTableButton();
        },
        initTableButton() {
            const operations = [
                {
                    name: '修改',
                    type: 'primary',
                    handleClick: this.handleEdit,
                    handleShow: () => true
                },
                {
                    name: '提交',
                    type: 'success',
                    handleClick: this.handleCommit,
                    handleShow: () => true
                },
                {
                    name: '审核',
                    type: 'success',
                    handleClick: this.handleCheck,
                    handleShow: () => true
                }
            ];
            operations.forEach((item) => {
                this.tableConfig.operations.push(item);
            });
        },
        handleClickAdd() {
            const data = {};
            this.$refs.dialogRef.openDialog('add', data);
        },
        handleEdit(item) {
            return 'handleEdit';
        },
        handleCommit(item) {
            return 'handleCommit';
        },
        handleCheck(item) {
            return 'handleCheck';
        }
    }
};
</script>
<style lang="scss" scoped></style>
