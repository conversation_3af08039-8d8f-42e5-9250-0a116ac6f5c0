<template>
    <div>
        <div v-if="!isDetails && !whetherSpecifyAsset" class="task-button">
            <el-button
                type="primary"
                @click="handleClick"
                :disabled="list.length >= applyDetailList.length"
                >+ 新增</el-button
            >
        </div>
        <div v-if="!isDetails && whetherSpecifyAsset" class="task-button">
            <el-upload
                action="demo"
                ref="upload"
                accept=".xlsx"
                :show-file-list="false"
                :auto-upload="false"
                :multiple="false"
                :on-change="handleUploadChange"
            >
                <el-button type="primary">上传调拨明细</el-button>
            </el-upload>
            <el-button
                type="primary"
                style="width: 100px; margin-right: 16px"
                @click="handleDownload"
                >下载模板</el-button
            >
        </div>
        <el-table :data="list" border :row-class-name="rowClassName">
            <el-table-column
                type="index"
                label="序号"
                width="80px"
            ></el-table-column>
            <el-table-column
                v-for="column in elTableColumns"
                :key="column.prop"
                :label="column.label"
                :prop="column.prop"
                v-bind="column.elTableColumnAttrs"
                :width="column.width"
            >
                <template slot-scope="scope">
                    <!-- 单元格内容渲染 -->
                    <template v-if="!column.renderMode">{{
                        scope.row[column.prop]
                    }}</template>
                    <template v-if="column.renderMode === 'input'">
                        <el-input v-model="scope.row[column.prop]"></el-input>
                    </template>
                    <!-- 客户选择 -->
                    <template v-if="column.renderMode === 'customerSelect'">
                        <el-select
                            v-model="scope.row[column.prop]"
                            :placeholder="column.placeholder || ''"
                            :filterable="true"
                            @change="handleChangeCustomerCode(scope.row)"
                        >
                            <el-option
                                v-for="item in customerOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </template>
                    <template v-if="column.renderMode === 'productSelect'">
                        <el-select
                            v-model="scope.row[column.prop]"
                            :placeholder="column.placeholder || ''"
                            @change="handleChange(scope.row)"
                            :filterable="true"
                        >
                            <el-option
                                v-for="item in customerProductMap[
                                    scope.row[column.dep]
                                ]"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </template>
                    <template v-if="column.renderMode === 'number'">
                        <el-input-number
                            v-model="scope.row[column.prop]"
                            controls-position="right"
                            :min="0"
                            :max="scope.row[column.max]"
                            :precision="0"
                            :placeholder="column.placeholder || ''"
                            :controls="false"
                        ></el-input-number>
                    </template>
                </template>
            </el-table-column>
            <el-table-column
                v-if="operations.length > 0"
                fixed="right"
                align="center"
                label="操作"
                width="120px"
            >
                <template slot-scope="scope">
                    <template v-for="(operation, index) in operations">
                        <el-button
                            :key="index"
                            :type="operation.type"
                            size="small"
                            @click.native.prevent="
                                operation.handleClick(scope.$index)
                            "
                        >
                            {{ operation.name }}
                        </el-button>
                    </template>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
import { Message } from 'element-ui';

const editColumns = [
    {
        label: '客户名称',
        prop: 'customerCode',
        show: true,
        minWidth: 240,
        renderMode: 'customerSelect',
        options: 'customerOptions',
        placeholder: '请选择'
    },
    {
        label: '产品名称',
        prop: 'productName',
        show: true,
        minWidth: 240,
        dep: 'customerCode',
        renderMode: 'productSelect',
        placeholder: '请选择'
    },
    {
        label: '申请数量',
        prop: 'applyNum',
        show: true,
        minWidth: 160
    },
    {
        label: '未计划数量',
        prop: 'unPlanNum',
        show: true,
        minWidth: 160
    },
    {
        label: '来源仓在库数量',
        prop: 'availableNum',
        show: true,
        minWidth: 160
    },
    {
        label: '调拨数量',
        prop: 'allocateNum',
        show: true,
        minWidth: 160,
        renderMode: 'number',
        max: 'max',
        placeholder: '待输入'
    }
];

const noSpecifiedColumns = [
    {
        label: '客户名称',
        prop: 'customerName',
        show: true,
        minWidth: 240
    },
    {
        label: '产品名称',
        prop: 'productName',
        show: true,
        minWidth: 240
    },
    {
        label: '申请数量',
        prop: 'applyNum',
        show: true,
        minWidth: 160
    },
    {
        label: '调拨数量',
        prop: 'allocateNum',
        show: true,
        minWidth: 160
    }
];

const specifiedColumns = [
    {
        label: '客户名称',
        prop: 'customerName',
        show: true,
        minWidth: 240
    },
    {
        label: '产品序列号',
        prop: 'assetCode',
        show: true,
        minWidth: 240
    },
    {
        label: '客户资产编码',
        prop: 'customerAssetCode',
        show: true,
        minWidth: 240
    },
    {
        label: '产品名称',
        prop: 'productName',
        show: true,
        minWidth: 240
    },
    {
        label: '所在区仓',
        prop: 'warehouseName',
        show: true,
        minWidth: 240
    }
];

const addRow = {
    productName: '',
    applyNum: '',
    unPlanNum: '',
    availableNum: '',
    allocateNum: undefined,
    max: 0,
    customerCode: '',
    customerName: ''
};

export default {
    name: 'AllocationTaskDetails',
    props: {
        isDetails: {
            type: Boolean,
            default() {
                return false;
            }
        },
        allocateTaskCode: {
            type: String,
            default() {
                return '';
            }
        },
        whetherSpecifyAsset: {
            type: Boolean,
            default() {
                return false;
            }
        },
        applyDetailList: {
            type: Array,
            default() {
                return [];
            }
        },
        taskDetails: {
            type: Array,
            default() {
                return [];
            }
        },
        sourceWarehouseCode: {
            type: String,
            default() {
                return '';
            }
        }
    },
    data() {
        return {
            elTableColumns: [],
            operations: [],
            list: [],
            // 客户的 options
            customerOptions: [],
            // 客户名称和产品列表对应关系
            customerProductMap: {},
            applyDetailMap: {},
            action: ''
        };
    },
    computed: {},
    watch: {
        applyDetailList: {
            handler: 'handleApplyDetailList',
            deep: true,
            immediate: true
        },
        taskDetails: {
            handler: 'handleTaskDetails',
            deep: true,
            immediate: true
        }
    },
    mounted() {
        this.init();
    },
    methods: {
        // 初始化 区分编辑和详情 区分指定资产和不指定资产
        init() {
            if (!this.isDetails) {
                if (!this.whetherSpecifyAsset) {
                    this.editInit();
                    return;
                }
                this.specifiedEditInit();
                return;
            }
            if (this.whetherSpecifyAsset) {
                this.elTableColumns = specifiedColumns;
                return;
            }
            this.elTableColumns = noSpecifiedColumns;
        },
        // 非指定资产编辑
        editInit() {
            this.elTableColumns = editColumns;
            this.operations = [
                {
                    name: '删除',
                    type: 'primary',
                    handleClick: this.handleDelete
                }
            ];
        },
        // 指定资产编辑
        specifiedEditInit() {
            this.elTableColumns = specifiedColumns;
            this.operations = [
                {
                    name: '删除',
                    type: 'primary',
                    handleClick: this.handleDelete
                }
            ];
        },
        // 新增一行
        handleClick() {
            this.list.push({ ...addRow });
        },
        handleChangeCustomerCode(row) {
            const { customerCode } = row;
            Object.assign(row, addRow);
            row.customerCode = customerCode;
        },
        handleDelete(index) {
            this.list.splice(index, 1);
            !this.whetherSpecifyAsset && this.formatOptions();
        },
        async handleDownload() {
            const { result } =
                await this.$service.warehouse.allocation.getAllocateDetailTemplatePath();
            window.open(result);
        },
        // 监听查询的申请明细,格式化数据 customerOptions customerProductMap
        handleApplyDetailList(newVal) {
            newVal.forEach((item) => {
                this.applyDetailMap[item.productName] = item;
                const productNameObj = {
                    value: item.productName,
                    label: item.productName,
                    ...item
                };
                if (Object.hasOwn(this.customerProductMap, item.customerCode)) {
                    this.customerProductMap[item.customerCode].push(
                        productNameObj
                    );
                } else {
                    this.customerOptions.push({
                        value: item.customerCode,
                        label: item.customerName
                    });
                    this.$set(this.customerProductMap, item.customerCode, [
                        productNameObj
                    ]);
                }
            });
        },
        // 监听查询的任务明细
        handleTaskDetails(newVal) {
            this.list = newVal.map((item) => {
                return {
                    ...item,
                    max: Math.min(item.unPlanNum || 0, item.availableNum || 0)
                };
            });
            this.formatOptions();
        },
        // 选择产品数量
        handleChange(item) {
            Object.assign(item, this.applyDetailMap[item.productName]);
            item.max = Math.min(item.unPlanNum, item.availableNum);
            this.formatOptions();
        },
        // 获取任务明细
        getTaskDetails() {
            return this.list;
        },
        // 格式化处理产品名称 options 避免可重复选择
        formatOptions() {
            Object.keys(this.customerProductMap).forEach((key) => {
                const selected = this.list.map((item) => {
                    if (item.customerCode === key) {
                        return item.productName;
                    }
                    return undefined;
                });
                this.customerProductMap[key] = this.applyDetailList
                    .filter(
                        (item) =>
                            !selected.includes(item.productName) &&
                            item.customerCode === key
                    )
                    .map((item) => {
                        return {
                            value: item.productName,
                            label: item.productName,
                            ...item
                        };
                    });
            });
        },
        async handleUploadChange({ raw }) {
            const formData = new FormData();
            formData.append('file', raw);
            const { code, result, message } =
                await this.$service.warehouse.allocation.uploadAllocateDetail(
                    formData
                );
            if (code !== '000000') {
                Message.error({
                    dangerouslyUseHTMLString: true,
                    message: message || '系统异常'
                });
                return;
            }
            this.$tools.message.suc(
                `共上传 ${result.importNum} 条，上传成功 ${result.correctNum} 条，上传失败 ${result.errorNum} 条`
            );
            if (result.correctList && Array.isArray(result.correctList)) {
                const set = new Set();
                this.list.forEach((item) => {
                    set.add(item.customerAssetCode);
                });
                result.correctList.forEach((item) => {
                    if (set.has(item.customerAssetCode)) return;
                    this.list.push(item);
                });
            }
        },
        rowClassName({ row, rowIndex }) {
            if (!this.whetherSpecifyAsset || this.isDetails) return '';
            if (
                row.warehouseCode !== this.sourceWarehouseCode ||
                !row.warehouseCode
            ) {
                return 'danger-row';
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.task-button {
    display: flex;
    flex-direction: row-reverse;
    flex-wrap: nowrap;
    margin-bottom: 16px;
}
</style>
