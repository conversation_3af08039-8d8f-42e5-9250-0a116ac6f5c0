<template>
    <el-form
        ref="formRef"
        class="query-area-form"
        :model="form"
        v-bind="elFormAttrs"
    >
        <slot name="form-body" />
    </el-form>
</template>
<script>
export default {
    name: 'SnbcForm',
    props: {
        /**
         * el-form组件model对象
         */
        form: {
            type: Object,
            default() {
                return {};
            }
        },
        /**
         * SnbcForm组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    elFormAttrs: {},
                    rules: []
                };
            }
        }
    },
    data() {
        return {
            // el-form组件默认属性设置
            defaultElFormAttrs: {
                'label-width': '80px'
            }
        };
    },
    computed: {
        // el-form组件应用属性
        elFormAttrs() {
            return {
                ...this.defaultElFormAttrs,
                ...(this.config.elFormAttrs || {})
            };
        }
    },
    methods: {
        // 获取表单实例
        getFormRef() {
            return this.$refs.formRef;
        }
    }
};
</script>
