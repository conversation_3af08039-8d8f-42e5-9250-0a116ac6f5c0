<!-- 区仓指标历史趋势查询 -->
<template>
    <div class="view">
        <div class="content query-label-line2">
            <snbc-table-query
                :query-params="tableConfig.queryParams"
                :query-config="tableConfig.queryConfig"
                @query="handleQuery"
            />
            <div class="snbc-header header">
                <div class="header-title">
                    <span class="header__title">查询结果</span>
                </div>
            </div>
            <el-card class="snbc-card padding-top-5">
                <div slot="header" class="snbc-card-header">
                    <span>区仓信息</span>
                </div>
                <el-row :gutter="10">
                    <el-col :span="8" v-for="(group, groupIndex) in indicatorGroup" :key="groupIndex">
                        <el-card class="margin-top-10 group-card" shadow="hover">
                            <el-row :gutter="10">
                                <el-col :span="6" class="flex-name">
                                    <svg-icon class-name="card-icon" :icon-class="group.icon" />
                                    <span class="group-name">
                                        {{ group.name }}
                                    </span>
                                </el-col>
                                <el-col :span="18" class="flex-content">
                                    <div v-for="(item, index) in group.list" :key="index" class="group-item">
                                        <span class="group-item-name" :style="{ width: group.nameWidth }">
                                            {{ item.name }}
                                        </span>
                                        ：
                                        <span
                                            v-if="!item.type"
                                            class="group-item-info word-width-fxed"
                                            :title="item.value"
                                        >
                                            {{ item.value }}
                                        </span>
                                        <span v-if="item.type === 'multi'" class="group-item-info">
                                            <span
                                                v-for="(valueItem, index) in item.value"
                                                :key="item.valueTip[index] + index"
                                            >
                                                <span :title="item.valueTip[index]">{{ valueItem }}</span>
                                                <span class="separate-data" v-if="index < item.value.length - 1"
                                                    >|</span
                                                ></span
                                            >
                                        </span>
                                        <el-link v-if="item.type === 'link'" type="primary" @click="handleLink(item)">
                                            {{ item.value }}
                                        </el-link>
                                    </div>
                                </el-col>
                            </el-row>
                        </el-card>
                    </el-col>
                </el-row>
            </el-card>
            <el-card class="snbc-card padding-top-5 margin-top-10">
                <div slot="header" class="snbc-card-header">
                    <span>指标对比</span>
                </div>
                <snbc-table-list :config="tableConfig" :list="tableList" />
            </el-card>
            <el-card class="snbc-card padding-top-5 margin-top-10 trend-card">
                <div slot="header" class="snbc-card-header">
                    <span>核心指标历史趋势</span>
                </div>
                <ChartAreaAndFlow ref="ChartAreaAndFlow" class="margin-top-10" />
                <ChartAreaUtilizationRate ref="ChartAreaUtilizationRate" class="margin-top-10" />
                <ChartComplianceRate ref="ChartComplianceRate" class="margin-top-10" />
                <ChartMatchRate ref="ChartMatchRate" class="margin-top-10" />
            </el-card>
        </div>
    </div>
</template>

<script>
import Vue from 'vue';
import moment from 'moment';
import functions from 'frame/mixins/functions.js';
import textRender from 'warehouse/common/text-render/index.js';
import SnbcTableQuery from 'warehouse/components/snbc-table/SnbcTableQuery.vue';
import SnbcTableList from 'warehouse/components/snbc-table/SnbcTableList.vue';
import ChartAreaAndFlow from './components/ChartAreaAndFlow.vue';
import ChartMatchRate from './components/ChartMatchRate.vue';
import ChartComplianceRate from './components/ChartComplianceRate.vue';
import ChartAreaUtilizationRate from './components/ChartAreaUtilizationRate.vue';
import commonItems from 'warehouse/common/form-items/common-items.js';
import warehouseItems from 'warehouse/common/form-items/warehouse-items.js';
import pickerOptions from 'warehouse/common/picker-options/index.js';

const { cloneDeep } = Vue.prototype.$tools;

// 查询参数
const queryParams = {
    warehouseCode: '',
    requestDate: []
};

// 支持内存缓存的页面参数
const allParams = {
    queryParams: cloneDeep(queryParams)
};
const { warehouseSelect } = warehouseItems;

// 统计周期
const requestDate = {
    ...commonItems.dateRange,
    name: '统计周期',
    modelKey: 'requestDate',
    elDatePickerAttrs: {
        'unlink-panels': true,
        'type': 'monthrange',
        'value-format': 'yyyy-MM',
        'picker-options': {
            shortcuts: [
                pickerOptions.lastMonthOption,
                pickerOptions.lastQuarterOption,
                pickerOptions.firstHalfYearOption,
                pickerOptions.secondHalfYearOption,
                pickerOptions.currentYearOption,
                pickerOptions.lastYearOption
            ],
            disabledDate: (datetime) => {
                if (requestDate.minDate) {
                    return (
                        moment(datetime).format('YYYY-MM') <=
                            moment(requestDate.minDate).subtract(12, 'months').format('YYYY-MM') ||
                        moment(datetime).format('YYYY-MM') >=
                            moment(requestDate.minDate).add(12, 'months').format('YYYY-MM')
                    );
                }
                return false;
            },
            onPick: ({ minDate }) => {
                requestDate.minDate = minDate;
            }
        }
    },
    blurHandler() {
        requestDate.minDate = null;
    }
};
// 查询区域配置项
const queryConfigItems = [warehouseSelect, requestDate];
export default {
    name: 'IndicatorHistoryTrend',
    components: {
        SnbcTableQuery,
        SnbcTableList,
        ChartAreaAndFlow,
        ChartMatchRate,
        ChartComplianceRate,
        ChartAreaUtilizationRate
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            indicator: [
                { name: '区仓名称', value: '-' },
                { name: '区仓面积', value: '-' },
                { name: '建仓时间', value: '-' },
                { name: '供应商', value: '-' },
                { name: '服务站区仓责任人', value: '-' },
                { name: '计费方式', value: '-' },
                { name: '价格', value: '-' },
                { name: '支出已支付金额', value: '-', type: 'multi', valueTip: ['支出金额', '已支付金额'] },
                { name: '在库资产总数', value: '-', type: 'link' },
                { name: '货损/丢失', value: '-' },
                {
                    name: '平均库龄',
                    value: '-',
                    type: 'multi',
                    valueTip: ['所有资产平均库龄', '新机平均库龄', '撤机平均库龄']
                },

                { name: '累计吞吐量', value: '-' },
                { name: '累计流量', value: '-' },
                { name: '货账相符率', value: '-' },
                { name: '面积利用率', value: '-' },
                { name: '出入库及时率', value: '-' }
            ],
            tableConfig: {
                queryParams: allParams.queryParams,
                queryConfig: {
                    items: queryConfigItems,
                    resetHidden: true
                },
                elTableColumns: [
                    {
                        label: '指标项',
                        prop: 'indexItem',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '全国平均水平',
                        prop: 'otherPositionAverage',
                        show: true,
                        minWidth: 100,
                        render(value, row) {
                            if (row.indexItem === '平均库龄') {
                                return textRender.unitRender(value, '天');
                            }
                            return textRender.unitRender(value, '%');
                        }
                    },
                    {
                        label: '区仓名称',
                        prop: 'specifiedBinAverage',
                        show: true,
                        minWidth: 100,
                        cellClassName: (column, row) => {
                            if (row.indexItem === '平均库龄') {
                                return this.$tools.getClassByCompareValue(
                                    row.otherPositionAverage,
                                    row.specifiedBinAverage
                                );
                            }
                            return this.$tools.getClassByCompareValue(
                                row.specifiedBinAverage,
                                row.otherPositionAverage
                            );
                        },
                        render(value, row) {
                            if (row.indexItem === '平均库龄') {
                                return textRender.unitRender(value, '天');
                            }
                            return textRender.unitRender(value, '%');
                        }
                    }
                ]
            },
            tableList: []
        };
    },
    computed: {
        // 接口请求参数
        requestParams() {
            return {
                warehouseCode: allParams.queryParams.warehouseCode,
                startStatisticsDate: allParams.queryParams.requestDate[0],
                endStatisticsDate: allParams.queryParams.requestDate[1]
            };
        },
        // 区仓分组
        indicatorGroup() {
            return [
                {
                    name: '信息汇总',
                    icon: 'qucangxinxi',
                    nameWidth: '8em',
                    list: this.indicator.filter((item) =>
                        [
                            '区仓名称',
                            '区仓面积',
                            '建仓时间',
                            '供应商',
                            '服务站区仓责任人',
                            '计费方式',
                            '价格',
                            '支出已支付金额'
                        ].includes(item.name)
                    )
                },
                {
                    name: '资产汇总',
                    icon: 'qucangzichan',
                    nameWidth: '6em',
                    list: this.indicator.filter((item) =>
                        ['在库资产总数', '货损/丢失', '累计吞吐量', '累计流量', '平均库龄'].includes(item.name)
                    )
                },
                {
                    name: '指标汇总',
                    icon: 'qucangzhibiao',
                    nameWidth: '6em',
                    list: this.indicator.filter((item) =>
                        ['货账相符率', '面积利用率', '出入库及时率'].includes(item.name)
                    )
                }
            ];
        }
    },
    beforeCreate() {
        if (!this.$store.getters.toggleView) {
            allParams.queryParams = cloneDeep(queryParams);
        }
    },
    created() {
        // 非tag切换进入，重置页面参数至默认状态
        if (!this.$store.getters.toggleView) {
            this.$tools.setQueryParams.call(this);
        }
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.handleQuery();
    },
    methods: {
        // 指标项点击跳转
        handleLink(item) {
            if (item.name === '在库资产总数') {
                this.$router.push({
                    path: '/app/assets/assets-management',
                    query: {
                        warehouseCode: queryParams.warehouseCode
                    }
                });
            }
        },
        // 查询操作
        handleQuery() {
            const { warehouseCode, startStatisticsDate } = this.requestParams;
            if (!warehouseCode || !startStatisticsDate) {
                this.$tools.message.warning('请选择区仓和统计周期后进行查询');
                return;
            }
            this.getTendencyMessage();
            this.getIndexComparison();
            this.getAccuracyRate();
            this.getAreaAndFlow();
            this.getAreaUtilizationRate();
            this.getBusinessComplianceRate();
        },
        // 区仓信息查询
        async getTendencyMessage() {
            const { unitRender } = textRender;
            this.$tools.setIndicator(this.indicator, {
                '区仓名称': unitRender('', ''),
                '区仓面积': unitRender('', '㎡'),
                '在库资产总数': unitRender('', '台'),
                '建仓时间': unitRender('', ''),
                '供应商': unitRender('', ''),
                '服务商区仓负责人': unitRender('', ''),
                '计费方式': unitRender('', ''),
                '价格': unitRender('', '元/台/天'),
                '支出已支付金额': [unitRender('', '元'), unitRender('', '元')],
                '累计吞吐量': unitRender('', '台次'),
                '累计流量': unitRender('', '台'),
                '货账相符率': unitRender('', '%'),
                '面积利用率': unitRender('', '%'),
                '出入库及时率': unitRender('', '%'),
                '货损/丢失': unitRender('', '台'),
                '平均库龄': [unitRender('', '天'), unitRender('', '天'), unitRender('', '天')]
            });
            const { getTendencyMessage } = this.$service.warehouse.statistics;
            try {
                const res = await getTendencyMessage(this.requestParams);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                const result = res.result || {};
                this.$tools.setIndicator(this.indicator, {
                    '区仓名称': unitRender(result.warehouseName, ''),
                    '区仓面积': unitRender(result.warehouseArea, '㎡'),
                    '在库资产总数': unitRender(result.assetCount, '台'),
                    '建仓时间': unitRender(result.createTime, ''),
                    '供应商': unitRender(result.warehouseSupplier, ''),
                    '服务站区仓责任人': unitRender(result.warehouseMaster, ''),
                    '计费方式': unitRender(result.billingMethod, ''),
                    '价格': unitRender(result.unitPrice, result.unitPrice ? '元/台/天' : ''),
                    '支出已支付金额': [unitRender(result.settlementAmount, '元'), unitRender(result.amountPaid, '元')],
                    '累计吞吐量': unitRender(result.throughput, '台次'),
                    '累计流量': unitRender(result.traffic, '台'),
                    '货账相符率': unitRender(result.accuracyRate, '%'),
                    '面积利用率': unitRender(result.actualAreaUtilizationRate, '%'),
                    '出入库及时率': unitRender(result.complianceRate, '%'),
                    '货损/丢失': unitRender(result.damagedLostQuantity, '台'),
                    '平均库龄': [
                        unitRender(result.averageAge, '天'),
                        unitRender(result.ageAverageNewAsset, '天'),
                        unitRender(result.ageAverageWithDrawAsset, '天')
                    ]
                });
                // 设置表格头部为具体区仓名称
                this.$set(this.tableConfig.elTableColumns[2], 'label', result.warehouseName);
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 指标对比
        async getIndexComparison() {
            this.tableList = [];
            const { getIndexComparison } = this.$service.warehouse.statistics;
            try {
                const res = await getIndexComparison(this.requestParams);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                const result = res.result || [];
                this.tableList = result;
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 货账相符率
        async getAccuracyRate() {
            const { getAccuracyRate } = this.$service.warehouse.statistics;
            this.$refs.ChartMatchRate.chart.showLoading();
            try {
                const res = await getAccuracyRate(this.requestParams);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                const result = res.result || [];
                this.$refs.ChartMatchRate.drawChart(result);
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            } finally {
                this.$refs.ChartMatchRate.chart.hideLoading();
            }
        },
        // 区仓面积和流量
        async getAreaAndFlow() {
            const { getAreaAndFlow } = this.$service.warehouse.statistics;
            this.$refs.ChartAreaAndFlow.chart.showLoading();
            try {
                const res = await getAreaAndFlow(this.requestParams);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                const result = res.result || {};
                this.$refs.ChartAreaAndFlow.drawChart(result);
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            } finally {
                this.$refs.ChartAreaAndFlow.chart.hideLoading();
            }
        },
        // 面积利用率
        async getAreaUtilizationRate() {
            const { getAreaUtilizationRate } = this.$service.warehouse.statistics;
            this.$refs.ChartAreaUtilizationRate.chart.showLoading();
            try {
                const res = await getAreaUtilizationRate(this.requestParams);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                const result = res.result || {};
                this.$refs.ChartAreaUtilizationRate.drawChart(result);
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            } finally {
                this.$refs.ChartAreaUtilizationRate.chart.hideLoading();
            }
        },
        // 出入库及时率
        async getBusinessComplianceRate() {
            const { getBusinessComplianceRate } = this.$service.warehouse.statistics;
            this.$refs.ChartComplianceRate.chart.showLoading();
            try {
                const res = await getBusinessComplianceRate(this.requestParams);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                const result = res.result || {};
                this.$refs.ChartComplianceRate.drawChart(result);
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            } finally {
                this.$refs.ChartComplianceRate.chart.hideLoading();
            }
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~warehouse/styles/indicator-card.scss';
::v-deep .trend-card .el-card__body {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}
::v-deep .group-card {
    padding: 10px;
    .el-card__body .el-row {
        height: 165px;
    }
    .group-item {
        display: inline-flex;
        width: 100%;
        .group-item-name {
            flex-shrink: 0;
        }
        .group-item-info.word-width-fxed {
            width: 76%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}

.separate-data {
    margin: 0 0.5rem;
}
</style>
