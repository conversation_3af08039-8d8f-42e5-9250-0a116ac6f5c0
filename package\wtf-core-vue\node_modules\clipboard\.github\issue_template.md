### Minimal example

> Fork this [JSFiddle](https://jsfiddle.net/zenorocha/5kk0eysw/) and reproduce your issue.

### Expected behaviour

I thought that by going to the page '...' and pressing the button '...' then '...' would happen.

### Actual behaviour

Instead of '...', what I saw was that '...' happened instead.

### Browsers affected

I tested on all major browsers and only IE 11 does not work.
