<template>
    <el-dialog
        class="custom-dialog query-label-line2"
        :visible.sync="dialogVisible"
        v-bind="elDialogAttrs"
        width="1200px"
    >
        <template>
            <snbc-card style="max-height: 500px; overflow-y: auto; display: block; width: 98%" :title="'图片显示'"
                ><template #card-body
                    ><el-image
                        style="width: 300px; height: 300px; padding-right: 8px"
                        v-for="url in urls"
                        :src="url"
                        :preview-src-list="[url]"
                        :key="url"
                    >
                    </el-image
                ></template>
            </snbc-card>
            <span slot="footer" class="dialog-footer">
                <el-button @click="hideDialog">返 回</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script>
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import elAttrs from 'warehouse/common/el-attrs/index.js';

export default {
    name: 'ShowPhoto',
    components: {
        SnbcCard
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 统一UI风格
            elAttrs,
            elDialogAttrs: elAttrs.elDialogAttrs,
            urls: [],
            // 弹窗显示
            dialogVisible: false
        };
    },

    methods: {
        // 展示弹窗
        openDialog(data, type) {
            this.elDialogAttrs = {
                ...elAttrs.elDialogAttrs,
                title: type
            };
            this.urls = data;
            this.dialogVisible = true;
        },

        // 隐藏弹窗
        hideDialog() {
            this.dialogVisible = false;
        }
    }
};
</script>

<style lang="scss" scoped></style>
