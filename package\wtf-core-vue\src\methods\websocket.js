function websocket(url, data) {
    // url: "ws://localhost:3333/users"
    let ws = new WebSocket(url);
    ws.addEventListener("open", function(e) {
    });

    ws.onopen = function() {
        console.info(
            `%cwebsocket连接成功！`,
            'background: #4192d9; padding: 5px; color: #fff; border-radius: 5px'
        )
        ws.send(data);
    };

    ws.onerror = function() {
        console.error(
            `%cwebsocket链接错误！`,
            'background: #4192d9; padding: 5px; color: #fff; border-radius: 5px'
        )
    };

    ws.onclose = function(e) {
        console.error(
            `%cwebsocket断开链接！`,
            'background: #4192d9; padding: 5px; color: #fff; border-radius: 5px'
        )
    };

    ws.onmessage = function(msg) {
        console.info(
            `%cMessage from server `,
            'background: #4192d9; padding: 5px; color: #fff; border-radius: 5px',
            JSON.parse(msg.data)
        )
        return msg;
    };
}
export default websocket;
