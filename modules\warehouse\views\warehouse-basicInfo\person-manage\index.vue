<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig" />
            <snbc-base-form-dialog
                ref="dialogRef"
                :config="dialogConfig"
                @submit="handleSubmit"
            />
            <snbc-base-form-dialog
                ref="updateDialogRef"
                :config="updateDialogConfig"
                @submit="handleResetPassword"
            />
            <snbc-base-form-dialog
                ref="updatePersonInfoRef"
                :config="updatePersonInfoConfig"
                @submit="handleSubmit"
            />
        </div>
    </div>
</template>

<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import FormItems from 'warehouse/common/form-items/basicInfo-items';
import FormRules from 'warehouse/common/form-rules';
import SnbcBaseFormDialog from 'warehouse/components/snbc-dialog/SnbcBaseFormDialog.vue';
import { updatePersonInfoItems } from './basicInfo-items';

const {
    userName,
    warehouseCodeList,
    roleCode,
    warehouseCode,
    loginAccount,
    phone,
    newPassword,
    newPasswordAgain
} = FormItems;

// 查询参数
const queryParams = {
    warehouseCode: '',
    userName: '',
    roleCode: ''
};
const { selectRequired, inputRequired, maxLength } = FormRules;
// 查询区域配置项
const queryConfigItems = [warehouseCode, userName, roleCode];
// 弹窗配置
const dialogConfigItems = [
    warehouseCodeList,
    roleCode,
    userName,
    loginAccount,
    phone
];

const updatePersonItems = updatePersonInfoItems;
const updateDialogConfigItems = [newPassword, newPasswordAgain];
// 弹窗校验规则
const rules = {
    warehouseCodeList: [selectRequired('区仓名称')],
    roleCode: [selectRequired('角色')],
    userName: [inputRequired('人员名称'), maxLength(30)],
    loginAccount: [inputRequired('登陆账号'), maxLength(30)],
    phone: [inputRequired('手机号'), maxLength(11)],
    newPassword: [
        inputRequired('密码'),
        {
            trigger: 'blur',
            message:
                '输入内容必须包含数字、英文字母、特殊符号且大于等于6位，最大长度50。',
            pattern:
                /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*])[\da-zA-Z~!@#$%^&*]{6,50}$/
        }
    ],
    newPasswordAgain: [
        inputRequired('确认密码'),
        {
            trigger: 'blur',
            message:
                '输入内容必须包含数字、英文字母、特殊符号且大于等于6位，最大长度50。',
            pattern:
                /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*])[\da-zA-Z~!@#$%^&*]{6,50}$/
        }
    ]
};

export default {
    name: 'PersonManage',
    components: {
        SnbcBaseTable,
        SnbcBaseFormDialog
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        const { getBasicInfoList: listApi } =
            this.$service.warehouse.basicInfoManagement;
        const checkAge = (rule, value, callback) => {
            if (!Number.isInteger(Number(value))) {
                callback(new Error('请输入数字值'));
            } else {
                callback();
            }
        };
        rules.phone.push({ validator: checkAge, trigger: 'blur' });
        return {
            listApi,
            tableConfig: {
                queryParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: listApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '人员名称',
                        prop: 'userName',
                        show: true,
                        minWidth: 80
                    },
                    {
                        label: '角色',
                        prop: 'roleName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '登陆账号',
                        prop: 'loginAccount',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '手机号',
                        prop: 'phone',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '管理范围',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '状态',
                        prop: 'disableFlag',
                        show: true,
                        minWidth: 80
                    }
                ],
                operations: [
                    {
                        name: '启用',
                        type: 'primary',
                        handleClick: this.handleOperate,
                        handleShow: (row) => row.disableFlag === '禁用'
                    },
                    {
                        name: '禁用',
                        type: 'primary',
                        handleClick: this.handleOperate,
                        handleShow: (row) => row.disableFlag === '启用'
                    },
                    {
                        name: '修改',
                        type: 'success',
                        handleClick: this.handleEdit
                    },
                    {
                        name: '密码重置',
                        type: 'warning',
                        handleClick: this.resetPassword
                    }
                ],
                headerButtons: [
                    {
                        name: '新增',
                        type: 'primary',
                        handleClick: this.handleAdd
                    }
                ]
            },
            // 弹窗配置
            dialogConfig: {
                rules,
                items: dialogConfigItems
            },
            updateDialogConfig: {
                rules,
                items: updateDialogConfigItems
            },
            updatePersonInfoConfig: {
                rules,
                items: updatePersonItems
            }
        };
    },
    computed: {},
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.handleQuery();
        this.getWareHouseInsertInfo();
        this.getRoleInfo();
    },
    methods: {
        // 新增
        handleAdd() {
            const data = {
                warehouseCodeList: []
            };
            this.$refs.dialogRef.baseAddDialog(data, '新增人员管理');
        },
        // 修改
        async handleEdit(row) {
            const params = {
                userId: row.userId,
                roleCode: row.roleCode
            };
            const data =
                await this.$service.warehouse.basicInfoManagement.getUserDetailInfo(
                    params
                );
            if (data.code !== '000000') {
                this.$tools.message.err(data.message || '系统异常');
                return;
            }
            if (!data.result.warehouseCodeList) {
                try {
                    const { code, message, result } =
                        await this.$service.warehouse.basicInfoManagement.getCanConfigWarehouse();
                    if (code !== '000000') {
                        this.$tools.message.err(message || '系统异常');
                        return;
                    }
                    const array = [];
                    for (let i = 0; i < result.length; i++) {
                        array.push(result[i].warehouseCode);
                    }
                    data.result.warehouseCodeList = array;
                } catch (error) {
                    console.error(error);
                    this.$tools.message.err('系统异常');
                }
            }
            this.$refs.updatePersonInfoRef.baseEditDialog(
                data.result,
                '修改人员管理'
            );
        },

        // 重置密码
        resetPassword(row) {
            const data = {
                userId: row.userId,
                roleCode: row.roleCode
            };
            this.$refs.updateDialogRef.baseAddDialog(data, '重置密码');
        },
        // 重置密码提交
        async handleResetPassword({ form }) {
            await this.$tools.confirm('确认提交？');
            if (form.newPassword !== form.newPasswordAgain) {
                this.$tools.message.err(
                    '两次密码输入不一致，请确认后重新输入。'
                );
                return;
            }
            const param = {
                password: this.$tools.encryptByRsa(form.newPassword),
                userId: form.userId,
                roleCode: form.roleCode
            };
            try {
                const res =
                    await this.$service.warehouse.basicInfoManagement.resetPassword(
                        param
                    );
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('重置成功');
                this.$refs.updateDialogRef.hideDialog();
                await this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 启用禁用
        async handleOperate(item) {
            let str = '';
            if (item.disableFlag === '禁用') {
                await this.$tools.confirm('确认启用？');
                str = '启用';
            } else if (item.disableFlag === '启用') {
                await this.$tools.confirm('确认禁用？');
                str = '禁用';
            }
            const params = {
                roleCode: item.roleCode,
                userId: item.userId
            };

            try {
                const res =
                    await this.$service.warehouse.basicInfoManagement.operatePersonState(
                        params
                    );
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc(`${str}成功`);
                await this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 新增
        async handleSubmit({ mode, form }) {
            await this.$tools.confirm('确认提交？');
            const params = this.$tools.deepClone(form);
            if (
                this.dialogConfig.items[0].elOptions.length ===
                form.warehouseCodeList.length
            ) {
                params.warehouseCodeList = [];
            }

            try {
                let res;
                if (mode === 'add') {
                    res =
                        await this.$service.warehouse.basicInfoManagement.addBasicInfo(
                            params
                        );
                } else {
                    res =
                        await this.$service.warehouse.basicInfoManagement.updateUserByUserId(
                            params
                        );
                }
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }

                if (mode === 'add') {
                    this.$tools.message.suc('新增成功');
                    this.$refs.dialogRef.hideDialog();
                } else {
                    this.$tools.message.suc('修改成功');
                    this.$refs.updatePersonInfoRef.hideDialog();
                }
                await this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 获取角色信息
        async getRoleInfo() {
            try {
                const res =
                    await this.$service.warehouse.basicInfoManagement.getCanConfigRole();
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.dialogConfig.items[1].elOptions = result.map((item) => {
                    return {
                        ...item,
                        label: item.roleName,
                        value: item.roleCode
                    };
                });
                this.updatePersonInfoConfig.items[1].elOptions = result.map(
                    (item) => {
                        return {
                            ...item,
                            label: item.roleName,
                            value: item.roleCode
                        };
                    }
                );
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 获取区仓信息
        async getWareHouseInsertInfo() {
            try {
                const { code, message, result } =
                    await this.$service.warehouse.basicInfoManagement.getCanConfigWarehouse();
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }

                this.dialogConfig.items[0].elOptions = result.map((item) => {
                    return {
                        ...item,
                        label: item.warehouseName,
                        value: item.warehouseCode
                    };
                });
                this.updatePersonInfoConfig.items[0].elOptions = result.map(
                    (item) => {
                        return {
                            ...item,
                            label: item.warehouseName,
                            value: item.warehouseCode
                        };
                    }
                );
                this.tableConfig.queryConfig.items[0].elOptions = result.map(
                    (item) => {
                        return {
                            ...item,
                            label: item.warehouseName,
                            value: item.warehouseCode
                        };
                    }
                );
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        }
    }
};
</script>

<style lang="scss" scoped></style>
