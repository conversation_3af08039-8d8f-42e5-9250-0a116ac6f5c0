<template>
    <el-dialog
        class="custom-dialog"
        :visible.sync="visible"
        :title="isDetails ? '物流信息确认' : '录入实际物流信息'"
        width="900px"
        @close="handleCancel"
    >
        <allocation-logistics-info
            class="dialog-container"
            ref="logisticsInfoRef"
            :isDetails="isDetails"
            :allocateTaskCode="allocateTaskCode"
            :mileage="mileage"
        ></allocation-logistics-info>
        <span slot="footer" class="dialog-footer">
            <template v-if="isDetails">
                <el-button type="danger" @click="handleReject">驳 回</el-button>
                <el-button type="primary" @click="handleSure"
                    >确认物流信息</el-button
                >
            </template>
            <template v-else>
                <el-button type="danger" @click="handleCancel">取 消</el-button>
                <el-button type="primary" @click="handleSave">保 存</el-button>
            </template>
        </span>
    </el-dialog>
</template>
<script>
import AllocationLogisticsInfo from './LogisticsInfo.vue';

export default {
    name: 'ActualLogisticsInfoDialog',
    components: {
        AllocationLogisticsInfo
    },
    data() {
        return {
            visible: false,
            isDetails: false,
            allocateTaskCode: '',
            mileage: 0
        };
    },
    methods: {
        showDialog(params) {
            this.isDetails = params.isDetails;
            this.allocateTaskCode = params.allocateTaskCode;
            this.mileage = params.mileage;
            this.visible = true;
        },
        // 保存
        async handleSave() {
            try {
                await this.$refs.logisticsInfoRef.validateForm();
                const form = {
                    ...this.$refs.logisticsInfoRef.getForm()
                };
                for (const [key, value] of Object.entries(form)) {
                    if (
                        typeof value === 'number' &&
                        !['mileage', 'cubicNumber'].includes(key)
                    ) {
                        form[key] = value * 100;
                    }
                }
                const { code, message } =
                    await this.$service.warehouse.allocation.inputActualLogisticsResult(
                        {
                            ...form,
                            allocateTaskCode: this.allocateTaskCode
                        }
                    );
                this.resultHandler(
                    code,
                    '物流信息保存成功',
                    message || '物流信息保存失败，请稍后重试'
                );
            } catch (e) {
                return e.message;
            }
        },
        // 取消
        handleCancel() {
            this.visible = false;
            this.allocateTaskCode = '';
            this.$refs.logisticsInfoRef.resetForm();
            this.$refs.logisticsInfoRef.$el.scrollTo(0, 0);
        },
        // 驳回
        async handleReject() {
            try {
                const rejectReason = await this.$tools.prompt(
                    '请填写驳回原因',
                    '物流信息驳回',
                    {
                        inputValidator: (msg) => {
                            return !!(msg && msg.length <= 255);
                        },
                        inputErrorMessage: '驳回原因为必填,且最多为255字符',
                        inputType: 'textarea'
                    }
                );
                const { code, message } =
                    await this.$service.warehouse.allocation.rejectActualLogisticsInfo(
                        {
                            rejectReason,
                            allocateTaskCode: this.allocateTaskCode
                        }
                    );
                this.resultHandler(
                    code,
                    '物流信息驳回成功',
                    message || '物流信息驳回失败，请稍后重试'
                );
            } catch (e) {
                return e.message;
            }
        },
        // 确认
        async handleSure() {
            try {
                await this.$tools.confirm(
                    '是否确认物流信息无误？',
                    '物流信息确认'
                );
                const { code, message } =
                    await this.$service.warehouse.allocation.confirmActualLogisticsInfo(
                        this.allocateTaskCode
                    );
                this.resultHandler(
                    code,
                    '物流信息确认成功',
                    message || '物流信息确认失败，请稍后重试'
                );
            } catch (e) {
                return e.message;
            }
        },
        // 处理返回请求
        resultHandler(code, suc, err) {
            if (code !== '000000') {
                this.$tools.message.err(err);
                return;
            }
            this.$tools.message.suc(suc);
            this.handleCancel();
            this.$parent.handleQuery();
        }
    }
};
</script>
<style lang="scss" scoped>
.dialog-container {
    max-height: 60vh;
    overflow: auto;
}
</style>
