<template>
    <div class="content">
        <el-page-header @back="goBack" content="撤机任务详情"></el-page-header>
        <snbc-card class="margin-top-10" :title="title">
            <template #card-body>
                <snbc-descriptions :items="basicInfo" :config="config" />
            </template>
        </snbc-card>
        <snbc-card class="margin-top-10" title="入库记录">
            <template #card-body>
                <snbc-table-list :config="tableConfig" :list="list" />
            </template>
        </snbc-card>
    </div>
</template>
<script>
import functions from 'frame/mixins/functions.js';
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import SnbcDescriptions from 'warehouse/components/snbc-descriptions/SnbcDescriptions';
import SnbcTableList from 'warehouse/components/snbc-table/SnbcTableList.vue';

const elDescriptionsAttrs = {
    column: 3,
    labelStyle: {
        width: '100px'
    },
    contentStyle: {
        'width': '150px',
        'word-break': 'break-all'
    }
};

const elTableColumns = [
    {
        label: '序号',
        prop: 'index',
        show: true,
        minWidth: 60
    },
    {
        label: '资产编码',
        prop: 'assetCode',
        show: true,
        minWidth: 160
    },
    {
        label: '客户资产编码',
        prop: 'customerAssetCode',
        show: true,
        minWidth: 160
    },
    {
        label: '产品类型',
        prop: 'productType',
        show: true,
        minWidth: 80
    },
    {
        label: '产品名称',
        prop: 'productName',
        show: true,
        minWidth: 160
    },
    {
        label: '状态',
        prop: 'damageFlag',
        show: true,
        minWidth: 80
    },
    {
        label: '货损任务编号',
        prop: 'damageTask',
        show: true,
        minWidth: 160
    },
    {
        label: '货损备注',
        prop: 'remark',
        show: true,
        minWidth: 200
    },
    {
        label: '入库时间',
        prop: 'createTime',
        show: true,
        minWidth: 160
    }
];

export default {
    name: 'OperateLogDetails',
    components: {
        SnbcDescriptions,
        SnbcTableList,
        SnbcCard
    },
    mixins: [functions],
    data() {
        return {
            taskCode: '',
            // 产品信息展示字段
            basicInfo: [
                { label: '区仓名称', key: 'targetWarehouseName', value: '' },
                { label: '服务外包商', key: 'orderProviderName', value: '' },
                { label: '关联订单编号', key: 'orderCode', value: '' },
                { label: '客户名称', key: 'orderCustomerName', value: '' },
                { label: '回仓柜机数量', key: 'returnCabinetNumber', value: '' },
                { label: '回仓雨柜数量', key: 'returnCanopyNumber', value: '' }
            ],
            // 列表配置
            tableConfig: {
                // 查询条件对象
                queryParams: {},
                // 查询条件配置
                queryConfig: {
                    resetHidden: true,
                    items: []
                },
                // 列表列配置
                elTableColumns: Object.freeze(elTableColumns)
            },
            // 列表数据
            list: [],
            config: {
                elDescriptionsAttrs: Object.freeze(elDescriptionsAttrs)
            }
        };
    },
    computed: {
        title() {
            return `撤机入库任务编号：${this.taskCode}`;
        }
    },
    methods: {
        init(data) {
            this.taskCode = data.taskCode;
            this.setData(data);
            this.getDetailList();
        },
        setData(data) {
            this.basicInfo.forEach((item) => {
                item.value = data[item.key];
            });
        },
        // get withdrawal details
        async getDetailList() {
            this.list = [];
            const { code, result, message } = await this.$service.warehouse.withdrawal.getDetailList({
                taskCode: this.taskCode,
                page: 1,
                size: 999
            });
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
                return;
            }
            this.list = (result?.list || []).map((item, index) => {
                item.index = index + 1;
                item.damageFlag = item.damageFlag === 1 ? '货损' : '正常';
                item.damageTask = item.damageTask || '-';
                item.remark = item.remark || '-';
                return item;
            });
        },
        goBack() {
            this.$emit('handleBack');
        }
    }
};
</script>
<style lang="scss" scoped></style>
