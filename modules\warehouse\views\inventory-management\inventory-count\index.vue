<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig" />
        </div>
    </div>
</template>

<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import FormItems from 'warehouse/common/form-items/inventory-items';

const {
    planCode,
    startTime,
    endTime,
    planState,
    warehouseCode,
    taskCode,
    materialCode
} = FormItems;

// 查询参数
const queryParams = {
    planCode: '',
    startTime: '',
    endTime: '',
    taskCode: '',
    materialCode: '',
    planState: '',
    warehouseCode: ''
};

// 查询区域配置项
const queryConfigItems = [
    planCode,
    taskCode,
    materialCode,
    startTime,
    endTime,
    planState,
    warehouseCode
];

export default {
    name: 'WarehouseInventoryInformation',
    components: {
        SnbcBaseTable
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        const { getInventoryCount: listApi } =
            this.$service.warehouse.inventoryManagement;
        return {
            listApi,
            tableConfig: {
                queryParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: listApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '盘点计划单号',
                        prop: 'planOrder',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '盘点任务单号',
                        prop: 'taskCode',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '物料编码',
                        prop: 'materialCode',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 140
                    },
                    {
                        label: '盘点开始时间',
                        prop: 'inventoryStartTime',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '盘点结束时间',
                        prop: 'inventoryEndTime',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '盘点状态',
                        prop: 'inventoryState',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '资产总数量',
                        prop: 'totalAssets',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '已完成数',
                        prop: 'haveCompleteNumber',
                        show: true,
                        minWidth: 80
                    },
                    {
                        label: '异常数量',
                        prop: 'exceptionTaskNumber',
                        show: true,
                        minWidth: 80
                    }
                ],
                operations: [
                    {
                        name: '详情',
                        type: 'primary',
                        handleClick: this.handleDetail
                    }
                ]
            }
        };
    },
    computed: {},
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.handleQuery();
    },
    methods: {
        handleDetail(row) {
            this.$router.push({
                path: '/app/inventory/inventory-manage',
                query: { taskCode: row.taskCode }
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
