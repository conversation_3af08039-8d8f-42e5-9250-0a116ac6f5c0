/**
 * 下拉项必选规则
 * @param {String} name 表单项名称
 * @returns {Object} 表单规则
 */
export const selectRequired = (name) => {
    return { required: true, message: `请选择${name}`, trigger: 'change' };
};

/**
 * 输入项必填规则
 * @param {String} name 表单项名称
 * @returns {Object} 表单规则
 */
export const inputRequired = (name) => {
    return { required: true, message: `请输入${name}`, trigger: 'change' };
};

/**
 * 数值输入项必填规则
 * @param {String} name 表单项名称
 * @returns {Object} 表单规则
 */
export const inputRequiredOnBlur = (name) => {
    return { required: true, message: `请输入${name}`, trigger: 'blur' };
};

/**
 * 输入项长度规则
 * @param {Number} max 长度上限
 * @returns {Object} 表单规则
 */
export const maxLength = (max = 64) => {
    return {
        min: 1,
        max,
        message: `长度在 1 到 ${max} 个字符`,
        trigger: 'change'
    };
};

/**
 * 省市区校验
 * @param {Number} level 省市区校验级别
 * @returns {Object} 表单规则
 */
export const regionRequired = (level = 3) => {
    return {
        validator(rule, value, callback) {
            if (!value || value.length < level) {
                if (level === 1) {
                    callback(new Error('请选择省份'));
                } else if (level === 2) {
                    callback(new Error('请选择省市'));
                } else {
                    callback(new Error('请选择省市区'));
                }
            } else {
                callback();
            }
        },
        trigger: 'change'
    };
};

/**
 * 省市多选校验
 * @param {Number} level 省市区校验级别
 * @returns {Object} 表单规则
 */
export const cityRequired = () => {
    return {
        validator(rule, value, callback) {
            if (!value || !value.length) {
                callback(new Error('请选择省市'));
            } else {
                callback();
            }
        },
        trigger: 'change'
    };
};

/**
 * 文件校验
 * @param {String} message 信息
 *
 * @returns {Object} 表单规则
 */
export const fileRequired = (message) => {
    return {
        required: true,
        message: message || '请选取文件',
        trigger: 'change'
    };
};

/**
 * 时间选择必选规则
 * @param {String} name 表单项名称
 * @returns {Object} 表单规则
 */
export const dateRequired = (name) => {
    return { required: true, message: `请选择${name}`, trigger: 'blur' };
};

/**
 * 数字输入框校验
 * @param {String} name 表单项名称
 *
 * @returns {Object} 表单规则
 */
export const numberRequired = (name) => {
    const validateNumberNot0 = (rule, value, callback) => {
        if (!value) {
            callback(new Error(`${name}不能为 0`));
            return;
        }
        callback();
    };
    return { validator: validateNumberNot0, trigger: 'blur' };
};

export default {
    inputRequired,
    selectRequired,
    maxLength,
    regionRequired,
    fileRequired,
    dateRequired,
    numberRequired,
    inputRequiredOnBlur,
    cityRequired
};
