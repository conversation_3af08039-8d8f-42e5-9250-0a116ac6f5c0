<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>target-textarea</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body>
    <!-- 1. Define some markup -->
    <textarea id="bar">hello</textarea>
    <button class="btn" data-clipboard-action="cut" data-clipboard-target="#bar">Cut</button>

    <!-- 2. Include library -->
    <script src="../dist/clipboard.min.js"></script>

    <!-- 3. Instantiate clipboard -->
    <script>
    var clipboard = new ClipboardJS('.btn');

    clipboard.on('success', function(e) {
        console.log(e);
    });

    clipboard.on('error', function(e) {
        console.log(e);
    });
    </script>
</body>
</html>
