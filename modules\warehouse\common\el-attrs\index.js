const elDialogAttrs = {
    title: '弹窗',
    width: '800px',
    center: true
};
const elTableAttrs = {
    'border': true,
    'width': '100%',
    'header-cell-style': { background: '#F5F6FA' }
};
const elTableColumnAttrs = {
    'sortable': true,
    'align': 'center',
    'show-overflow-tooltip': true
};

const operateColumnAttrs = {
    label: '操作',
    fixed: 'right',
    align: 'center'
};

const elDescriptionsAttrs = {
    title: '',
    size: 'small',
    border: true,
    column: 2
};

const exportBtnAttrs = {
    name: '导出',
    type: 'primary',
    elButtonAttrs: { icon: 'el-icon-download' }
};

export default {
    elDialogAttrs,
    elTableAttrs,
    elTableColumnAttrs,
    operateColumnAttrs,
    elDescriptionsAttrs,
    exportBtnAttrs
};
