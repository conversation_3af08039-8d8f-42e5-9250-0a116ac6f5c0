import Clipboard from './clipboard';
import drag from './el-drag-dialog';
import adaptive from './el-table';
import permission from './permission';
import waves from './waves';
import sticky from './sticky';

const directives = [
    Clipboard,
    drag,
    adaptive,
    permission,
    waves,
    sticky
];

const install = function(Vue, opts = {}) {
    // locale.use(opts.locale)
    // locale.i18n(opts.i18n)

    directives.forEach(directive => {
        directive.install(Vue);
    });
};

/* istanbul ignore if */
if (typeof window !== 'undefined' && window.Vue) {
    install(window.Vue);
}

export default {
    version: '1.0.0',
    // locale: locale.use,
    // i18n: locale.i18n,
    install
};
