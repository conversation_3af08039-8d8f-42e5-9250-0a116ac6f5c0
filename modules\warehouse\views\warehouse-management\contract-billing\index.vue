<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig" />
        </div>
        <comtract-billing-dialog ref="dialogRef" @submit="handleSubmit"></comtract-billing-dialog>
    </div>
</template>

<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import ComtractBillingDialog from './components/dialog';

const { customerSelect, select, warehouseSelect, productSelect, date } = FormItems;

// 查询参数
const queryParams = {
    customerCode: '',
    warehouseCode: '',
    productId: '',
    state: '',
    assertDate: ''
};

// 查询区域配置项
const queryConfigItems = [
    warehouseSelect,
    customerSelect,
    productSelect,
    {
        ...select,
        name: '生效状态',
        modelKey: 'state',
        elOptions: [
            { label: '是', value: '1' },
            { label: '否', value: '0' }
        ]
    },
    {
        ...date,
        name: '生效时间',
        modelKey: 'assertDate',
        elDatePickerAttrs: {
            'type': 'month',
            'value-format': 'yyyy-MM'
        }
    }
];

// 编辑弹窗配置
export default {
    name: 'WarehouseContractBilling',
    components: {
        SnbcBaseTable,
        ComtractBillingDialog
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        const {
            searchBillingRuleList: listApi,
            addBillingRule: addApi,
            updateBillingRuleById: editApi
        } = this.$service.warehouse.warehouseContractBilling;
        return {
            listApi,
            addApi,
            editApi,
            tableConfig: {
                queryParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: listApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '客户名称',
                        prop: 'customerName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '计费单价',
                        prop: 'feeEach',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '收费单位',
                        prop: 'feeRule',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '免费天数',
                        prop: 'freeDay',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '计费方式',
                        prop: 'feeType',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '生效时间',
                        prop: 'assertDate',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '生效状态',
                        prop: 'state',
                        show: true,
                        minWidth: 120
                    }
                ],
                operations: [
                    {
                        name: '编辑',
                        type: 'primary',
                        handleClick: this.handleEdit,
                        handleShow: (item) => {
                            const assertDate = new Date(item.assertDate);
                            return assertDate > Date.now() && item.state === '否';
                        }
                    }
                ],
                headerButtons: [
                    {
                        name: '新增',
                        type: 'primary',
                        handleClick: this.handleAdd
                    }
                ],
                hooks: {
                    tableListHook: this.tableListHook
                }
            }
        };
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.handleQuery();
    },
    methods: {
        // 编辑操作
        handleEdit(row) {
            const data = this.$tools.cloneDeep(row);
            this.$refs.dialogRef.baseEditDialog(data, '编辑合同计费规则');
        },
        // 新增操作
        handleAdd() {
            const data = {};
            this.$refs.dialogRef.baseAddDialog(data, '新增合同计费规则');
        },
        // 新增或编辑提交
        async handleSubmit({ mode, form }) {
            await this.$tools.confirm('确认提交？');
            const params = { ...form };
            const submitApi = mode === 'edit' ? this.editApi : this.addApi;
            try {
                const res = await submitApi(params);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.tableConfig.queryConfig.items = [];
                this.$nextTick(() => {
                    this.tableConfig.queryConfig.items = queryConfigItems;
                });
                this.$refs.dialogRef.hideDialog();
                this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        tableListHook(list) {
            list.map((item) => {
                item.state = {
                    0: '否',
                    1: '是'
                }[item.state];
                return item;
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
