/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        // 基础接口地址
        baseApis: {
            // 文件上传
            fileUploadApi: `${basePath.orderApi.base}/file_upload/upload_receipt`
        }
    };

    return service;
};
