<template>
    <div>
        <snbc-card title="对账完成凭证" v-if="settlementDocument.length">
            <template #card-body>
                <img
                    width="160px"
                    height="160px"
                    v-for="item in settlementDocument"
                    :key="item.url"
                    :src="item.url"
                    :alt="item.name"
                    class="img"
                />
            </template>
        </snbc-card>
        <snbc-card title="费用确认凭证" v-if="confirmDocument.length">
            <template #card-body>
                <img
                    class="img"
                    width="160px"
                    height="160px"
                    v-for="item in confirmDocument"
                    :key="item.url"
                    :src="item.url"
                    :alt="item.name"
                />
            </template>
        </snbc-card>
    </div>
</template>
<script>
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';

export default {
    name: 'CredentialDetail',
    components: {
        SnbcCard
    },
    props: {
        detail: {
            type: Object,
            default() {
                return {};
            }
        }
    },
    data() {
        return {
            settlementDocument: [],
            confirmDocument: []
        };
    },
    watch: {
        detail: {
            handler() {
                this.handlerDetail();
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        handlerDetail() {
            const url = this.detail.reconciliationResult || this.detail.settlementDocument;

            if (url) {
                this.settlementDocument = url.split(',').map((item) => {
                    return {
                        url: item,
                        name: item.substring(item.lastIndexOf('/') + 1)
                    };
                });
            } else {
                this.settlementDocument = [];
            }
            if (this.detail.confirmDocument) {
                this.confirmDocument = this.detail.confirmDocument.split(',').map((item) => {
                    return {
                        url: item,
                        name: item.substring(item.lastIndexOf('/') + 1)
                    };
                });
            } else {
                this.confirmDocument = [];
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.img {
    border: 1px #c2c7c9 solid;
    border-radius: 5px;
    margin: 16px;
}
</style>
