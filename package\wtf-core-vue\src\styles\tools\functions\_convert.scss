/// Converting typographic units
/// @param {Number} $value - Need to convert unit
/// @param {String} $currentUnit - Current Unit
/// @param {String} $convertUnit - Convert unit
/// @param {Number} $baseSise [null] - Base font-size for html element
/// @example
/// //input
/// .example1{
///   font-size: convert(12, px, percent, 16); // converted from pixels
/// }
///   
/// .example2{
///   font-size: convert(13, pts, ems); // converted from points
/// }
///   
/// .example3{
///   font-size: convert(2.5, ems, px); // converted from ems 
/// }
///   
/// .example4{
///   font-size: convert(234, percent, px); // converted from percentage 
/// }
/// //Output
/// .example1 {
///   font-size: 75%;
/// }
///   
/// .example2 {
///   font-size: 1.08333em;
/// }
///   
/// .example3 {
///   font-size: 40px;
/// }
///   
/// .example4 {
///   font-size: 37.44px;
/// }
@function convert($value, $currentUnit, $convertUnit, $baseSise: 16) {
    @if $convertUnit==px {
        @if $converUnit==ems {
            @return $value / $baseSise+0em;
        }
        @else if $convertUnit==percent {
            @return percentage($value / $baseSise);
        }
    }
    @else if $currentUnit==ems {
        @if $convertUnit==px {
            @return $value * $baseSise+0px;
        }
        @else if $convertUnit==percent {
            @return percentage($value);
        }
    }
    @else if $currentUnit==percent {
        @if $convertUnit==px {
            @return $value * $baseSise / 100+0px;
        }
        @else if $convertUnit==ems {
            @return $value / 100+em;
        }
    }
    @else if $currentUnit==pts {
        @if $convertUnit==px {
            @return $value * 1.3333+0px;
        }
        @else if $convertUnit==ems {
            @return $value / 12+0em;
        }
        @else if $convertUnit==percent {
            @return percentage($value / 12)
        }
    }
}