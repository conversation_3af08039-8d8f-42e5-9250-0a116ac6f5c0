/**
 * 公司发货相关表单组件
 */
import commonItems from './common-items.js';

const companyDeliveryTaskCode = {
    ...commonItems.input,
    name: '公司发货任务编号',
    modelKey: 'taskCode'
};
const companyDeliveryTaskName = {
    ...commonItems.input,
    name: '公司发货任务名称',
    modelKey: 'taskName'
};
const stockRequestCode = {
    ...commonItems.input,
    name: '备货申请任务编号',
    modelKey: 'stockRequestCode'
};
const targetWarehouse = {
    ...commonItems.input,
    name: '目标区仓',
    modelKey: 'targetWarehouse'
};

const customerName = {
    ...commonItems.input,
    name: '客户名称',
    modelKey: 'customerName'
};
const companyDeliveryTaskState = {
    ...commonItems.select,
    name: '任务状态',
    modelKey: 'taskState',
    elOptions: [
        { label: '已删除', value: 'DELETE' },
        { label: '未推送', value: 'NEW' },
        { label: '已推送', value: 'SEND' },
        { label: '已发货', value: 'DELIVERED' },
        { label: '入库中', value: 'STORAGE' },
        { label: '已完成', value: 'DONE' }
    ]
};
const taskSource = {
    ...commonItems.input,
    name: '任务来源',
    modelKey: 'taskSource'
};
const expectArrivalTime = {
    ...commonItems.input,
    name: '期望到货日期',
    modelKey: 'expectArrivalTime'
};
const remark = {
    ...commonItems.textarea,
    name: '备注',
    modelKey: 'remark'
};
const productName = {
    ...commonItems.input,
    name: '产品名称',
    modelKey: 'productName'
};
const companyDeliveryTaskItemCode = {
    ...commonItems.input,
    name: '公司发货清单编号',
    modelKey: 'code'
};
const companyDeliveryTaskItemName = {
    ...commonItems.input,
    name: '公司发货清单编号',
    modelKey: 'name'
};
const arrivalState = {
    ...commonItems.select,
    name: '到货状态',
    modelKey: 'arrivalState',
    elOptions: [
        { label: '未到货', value: '未到货' },
        { label: '部分到货', value: '部分到货' },
        { label: '已到货', value: '已到货' }
    ]
};

const assetCode = {
    ...commonItems.input,
    name: '产品序列号',
    modelKey: 'assetCode'
};
const assetCodeCustomer = {
    ...commonItems.input,
    name: '客户资产编码',
    modelKey: 'assetCodeCustomer'
};

const erpOrderState = {
    ...commonItems.select,
    name: '搬运订单状态',
    modelKey: 'erpOrderState',
    elOptions: [
        { label: '未发货', value: '未发货' },
        { label: '部分发货', value: '部分发货' },
        { label: '全部发货', value: '全部发货' }
    ]
};

export default {
    companyDeliveryTaskCode,
    companyDeliveryTaskName,
    stockRequestCode,
    targetWarehouse,
    customerName,
    companyDeliveryTaskState,
    erpOrderState,
    taskSource,
    expectArrivalTime,
    remark,
    productName,
    companyDeliveryTaskItemCode,
    companyDeliveryTaskItemName,
    arrivalState,
    assetCode,
    assetCodeCustomer
};
