/*----------------------------------------------------------------
// 功能说明：用于扩展调整element-ui组件的样式
//
//----------------------------------------------------------------*/
@import "./variables.scss";

$--tooltip--border-color: #878787 !important;

// ---------------------公共mixin定义----------------
/**
 说明：用于设置el-tooltip，上下左右样式设置
*/
@mixin tooltip__popper($placement) {
    .el-tooltip__popper[x-placement^="#{$placement}"] .popper__arrow,
    .el-tooltip__popper[x-placement^="#{$placement}-start"] .popper__arrow,
    .el-tooltip__popper[x-placement^="#{$placement}-end"] .popper__arrow,
    .el-tooltip__popper[x-placement^="#{$placement}"] .popper__arrow::after,
    .el-tooltip__popper[x-placement^="#{$placement}-start"]
        .popper__arrow::after,
    .el-tooltip__popper[x-placement^="#{$placement}-end"]
        .popper__arrow::after {
        border-#{$placement}-color: $--tooltip--border-color;
    }
}

/**
 说明：用于设置el-button，所以标准样式定义
*/
@mixin button {
    .el-button {
        padding: 0px 13px;
        height: 40px;
        border-radius: 8px;
        cursor: pointer;
    }

    .el-button--medium {
        padding: 10px 20px;
    }

    .el-button--mini {
        height: 32px;
        padding: 0px 10px;
        font-size: 1.4rem;
        padding: 7px 10px;
    }

    .el-button--default {
		color:#09c;
		border:1px solid #09c;
        &:focus,
        &:hover,
        &:active {
            background: $--button--bg--default;
            border-color: $--button--border-color;
        }
    }

    .el-button--primary {
        color: #fff;
        background: $--button--bg--primary;

        &:focus,
        &:hover,
        &:active {
            color: #fff;
            background: $--button--bg--primary;
        }
    }

    .el-input--medium .el-input__inner {
        height: 40px;
        line-height: 40px;
    }
}

// ---------------------el-tooltip样式----------------
/*解决tooltip超过页面宽度，内容显示不全问题*/
.el-tooltip__popper {
    max-width: 80%;
}
.el-tooltip__popper.is-dark {
    background: $--tooltip--border-color;
}

@include tooltip__popper("top");
// .el-tooltip__popper[x-placement^='top'] .popper__arrow,
// .el-tooltip__popper[x-placement^='top-start'] .popper__arrow,
// .el-tooltip__popper[x-placement^='top-end'] .popper__arrow,
// .el-tooltip__popper[x-placement^='top'] .popper__arrow::after,
// .el-tooltip__popper[x-placement^='top-start'] .popper__arrow::after,
// .el-tooltip__popper[x-placement^='top-end'] .popper__arrow::after {
//     border-top-color: $--tooltip--border-color;
// }
@include tooltip__popper("bottom");
// .el-tooltip__popper[x-placement^='bottom'] .popper__arrow,
// .el-tooltip__popper[x-placement^='bottom-start'] .popper__arrow,
// .el-tooltip__popper[x-placement^='bottom-end'] .popper__arrow,
// .el-tooltip__popper[x-placement^='bottom'] .popper__arrow::after,
// .el-tooltip__popper[x-placement^='bottom-start'] .popper__arrow::after,
// .el-tooltip__popper[x-placement^='bottom-end'] .popper__arrow::after {
//     border-bottom-color: $--tooltip--border-color;
// }

@include tooltip__popper("left");
// .el-tooltip__popper[x-placement^='left'] .popper__arrow,
// .el-tooltip__popper[x-placement^='left-start'] .popper__arrow,
// .el-tooltip__popper[x-placement^='left-end'] .popper__arrow,
// .el-tooltip__popper[x-placement^='left'] .popper__arrow::after,
// .el-tooltip__popper[x-placement^='left-start'] .popper__arrow::after,
// .el-tooltip__popper[x-placement^='left-end'] .popper__arrow::after {
//     border-left-color: $--tooltip--border-color;
// }

@include tooltip__popper("right");
// .el-tooltip__popper[x-placement^='right'] .popper__arrow,
// .el-tooltip__popper[x-placement^='right-start'] .popper__arrow,
// .el-tooltip__popper[x-placement^='right-end'] .popper__arrow,
// .el-tooltip__popper[x-placement^='right'] .popper__arrow::after,
// .el-tooltip__popper[x-placement^='right-start'] .popper__arrow::after,
// .el-tooltip__popper[x-placement^='right-end'] .popper__arrow::after {
//     border-right-color: $--tooltip--border-color;
// }

// ---------------------el-table样式----------------
.el-table th,
.el-table__header-wrapper tr > th,
.el-table__header tr > th {
    background: $--header--bg;
}
// 表格样式
.el-table th > .cell {
    color: #000000;
}
.el-table .el-table__row .cell {
    color: #2f2f2f;
    font-size: 14px;
}
.el-table .has-gutter {
    height: 50px !important;
}
.el-table .el-table__row {
    height: 50px !important;
}
.el-table .el-table__row:nth-child(even) {
    background: #fbfbfb;
}
.el-table td,
.el-table th.is-leaf {
    border-bottom: transparent !important;
}
// 修改表格边框颜色
.el-table--border:after,
.el-table--group:after,
.el-table:before {
    background-color: #eeeeee !important;
}

.el-table--border,
.el-table--group {
    border-color: #eeeeee !important;
}

.el-table--border th,
.el-table--border th.gutter:last-of-type {
    border-bottom: 1px solid #eeeeee !important;
}

.el-table--border td,
.el-table--border th {
    border-right: 1px solid #eeeeee !important;
}
// 调整表格行悬浮时的样式
.el-table tbody tr:hover>td { 
    background-color:#eff3f6 !important
}
// ---------------------el-input样式----------------
.view {
    .el-select--medium,
    .el-range-editor--medium,
    .el-range-editor--medium.el-input__inner,
    .el-input--medium {
        height: 40px;
        line-height: 40px;
    }

    .el-button--primary,
    .el-button--success,
    .el-button--warning,
    .el-button--danger,
    .el-button--info {
        &.is-plain {
            background: #fff;

            &:hover,
            &:active{
                background: #fff;
                color: currentColor;
            }
        }
    }

    .el-textarea.el-input--medium {
        height: auto;
    }

    //  输入框中的放大镜样式
    .el-input__suffix {
        color: #09c !important;
        font-size: 1.4rem;
    }
	// 解决 select 选择框清空按钮不居中问题
	.el-select{
		.el-input{
			.el-input__suffix{
				top:-3px;
			}
		}
	}
    .el-form {
        .el-select--medium,
        .el-range-editor--medium.el-input__inner {
            width: 100%;
        }
    }
}

.view {
    .el-pagination {
        margin-top: 30px;
        margin-bottom: 10px;
        text-align: right;

        &.is-background .el-pager li:not(.disabled).active {
            background-color: #09c;
            color: #fff;
            border-radius: 5px;
        }
        .el-input {
            height: 30px;
            line-height: 30px;
        }
    }
}

// ---------------------el-message-box样式定义----------------
.el-message-box__wrapper .el-message-box {
    padding-bottom: 20px;
    width: auto;
    min-width: 420px;
    vertical-align: top !important;
    margin-top: 15vh;

    @include button();

    .el-message-box__header {
        padding: 18px 20px;
        height: 50px;
        background: #f5f6fa;

        .el-message-box__title {
            font-weight: bold;

            &::before {
                content: "";
                display: inline-block;
                width: 5px;
                height: 15px;
                background: #09c;
                margin-right: 10px;
                position: relative;
                top: 2px;
            }
        }
    }

    .el-message-box__content {
        box-sizing: border-box;
        padding: 20px;
        padding-top: 26px;
        .el-icon-warning {
            color: #faad14;
            font-size: 2.4rem !important;
        }
        .el-message-box__status + .el-message-box__message {
            padding-right: 0;
            padding-left: 35px;
        }
    }

    .el-message-box__btns {
        margin: 0px;
        margin-top: 0;
        padding-top: 20px;
        box-sizing: border-box;
        border-top: 1px solid #f5f6fa;

        .el-button {
            min-width: 100px;
        }
    }
}

// ---------------------el-tree样式定义----------------

.el-tree {
    font-size: 1.4rem;

    // 节点选中，显示操作按钮
    .el-tree-node {
        // 当节点选中时，显示节点的操作按钮
        &.is-current {
			& > .el-tree-node__content{
				background: #eff4ff !important;
				border-radius: 0;
			}
            & > .el-tree-node__content > .block > .block__btns {
                display: inline-block;
            }
        }
        
        .el-tree-node__content {
            height: 40px;
			border-radius: 5px;
            &:hover {
				background: #f5f7fa !important;
                .block__btns {
                    display: inline-block;
                }
            }
        }
    }

    .block {
        font-size: 1.4rem;
        .block__text {
            color: #676c6f;
        }
        .block__btns {
            display: none;
        }
    }
}

// ---------------------el-card样式定义----------------
.el-card {
    padding: $--padding-width;
    display: flex;
    justify-content: space-between;
    flex-direction: column;

    .el-card__header {
        padding: 0px;
        height: 40px;
        border: 0px;
        border-bottom: 1px solid #e7e7e7;
        display: flex;
        justify-content: space-between;
        font-weight: bold;
        flex-shrink: 0;

        & > * {
            flex-grow: 1;
        }

        & > .btns {
            flex-shrink: 0;
        }
    }

    .el-card__body {
        padding: 0px;
        flex-grow: 1;
        overflow: hidden;
    }

    & .header__btns > i {
        padding: 0 7px;
    }
}

// ---------------------页面综合样式定义----------------
.container,.container-box,
.view {
    @include button();

    .el-dialog {
        width: 36%;
        border-radius: 4px;
        border:1px solid #e6ebf5;

        .el-dialog__body {
            padding: 20px;
        }

        .el-dialog__footer {
            margin: 0px;
            margin-top: 0;
            padding-top: 20px;
            box-sizing: border-box;
            border-top: 1px solid #f5f6fa;
			.el-button--default {
				color:#000000;
				border:1px solid #DFDFDF;
			}
            .el-button {
                min-width: 100px;
            }
        }
    }

    // 穿梭框类弹窗
    .el-dialog.transfer-dialog {
        .el-dialog__body {
            padding: 20px;

            &,
            & > .container {
                display: flex;
                justify-content: space-between;
                padding: 0px;

                & > .el-tree {
                    width: 100%;
                }
            }
            // 这里避免前一个padding被覆盖
            & {
                padding: 20px;
            }

            // 穿梭框-弹窗，使用双卡片分割。
            .el-card {
                width: 100%;
                height: 50vh;
                display: flex;
                justify-content: space-between;
                flex-direction: column;

                & + .el-card {
                    margin-left: 10px;
                }

                &:first-child {
                    .el-card__header {
                        border-bottom: 0px;
                    }
                }

                & > .el-card__footer,
                & > .el-card__header {
                    flex-shrink: 0;
                }

                & > .el-card__body {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    overflow: hidden;

                    & > .el-input:first-child {
                        margin-bottom: 10px;
                        flex-shrink: 0;
                    }

                    & > .el-tree {
                        overflow: auto;
                        flex-grow: 1;
                    }

                    .block__btns {
                        color: #c7c7c7;
                    }
                }
            }
        }
        .el-dialog__footer {
            border-top: 0px;
            padding-top: 0px;

        }
    }

    .el-form {
        .el-select {
            width: 100%;
        }
    }
}

.view{
    .el-tag--medium {
        height: 32px;
        line-height: 32px;
        padding: 0 11px;
        border-radius: 8px;
        font-size: 14px;
        box-sizing: border-box;
        &>i{
            margin-right: 5px;
        }
    }

    .el-tag--plain.el-tag--danger{
        border-color: #f56e71;
    }

    .el-tag--plain.el-tag--success{
        border-color: #74c55c;
    }
}
//  弹框右上角叉号不居中问题
.el-dialog{
	.el-dialog__header{
		.el-dialog__headerbtn{
			top:16px;
		}
	}
}
// select下拉框样式统一调整
.el-select-dropdown{
	.el-scrollbar{
		.el-scrollbar__wrap{
			.el-select-dropdown__list{
				.el-select-dropdown__item.selected{
					color: #09c !important;
    				font-weight: 700;
					&:after{
						position: absolute;
						right: 20px;
						font-family: element-icons;
						content: "";
						font-size: 12px;
						font-weight: 700;
						color: #09c;
    					font-weight: 700;
						-webkit-font-smoothing: antialiased;
						-moz-osx-font-smoothing: grayscale;
					}
				}
			}
		}
	}
}

