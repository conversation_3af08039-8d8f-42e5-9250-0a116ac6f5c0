import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();
    const service = {
        product: {
            /**
             * 按条件获取产品
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/basedata/product_info/page',
                    method: 'post',
                    data
                });
            },
            /**
             * 产品新增
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            add(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/basedata/product_info/add',
                    method: 'post',
                    data
                });
            },
            /**
             * 产品编辑
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            edit(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/basedata/product_info/edit',
                    method: 'post',
                    data
                });
            },
            /**
             * 按条件获取产品在库统计列表
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getProductInventoryList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board/product_inventory',
                    method: 'post',
                    data
                });
            },
            /**
             * 按条件获取产品在库统计的统计数据
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getProductInventoryTotalList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board/product_inventory_total',
                    method: 'post',
                    data
                });
            },
            /**
             * 按条件导出产品在库统计
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            exportProductInventor(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board/export_product_inventory',
                    method: 'post',
                    responseType: 'blob',
                    data
                });
            },
            /**
             * 按条件获取产品分布查询列表
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getProductDistributionList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board/product_distribution',
                    method: 'post',
                    data
                });
            },
            /**
             * 按条件获取产品分布查询列表的统计数据
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getProductDistributionTotalList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board/product_distribution_total',
                    method: 'post',
                    data
                });
            },
            /**
             * 按条件获取产品在库统计列表 - 物料维度
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getProductInventoryMaterialList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board/product_inventory_material',
                    method: 'post',
                    data
                });
            },
            /**
             * 按条件获取产品在库统计的统计数据 - 物料维度
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getProductInventoryMaterialTotalList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board/product_inventory_material_total ',
                    method: 'post',
                    data
                });
            },
            /**
             * 按条件获取产品分布查询列表 - 物料维度
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getProductDistributionMaterialList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board/product_distribution_material',
                    method: 'post',
                    data
                });
            },
            /**
             * 按条件获取产品分布查询列表的统计数据 - 物料维度
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getProductDistributionMaterialTotalList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/board/product_inventory_material_wareHouse_total',
                    method: 'post',
                    data
                });
            },
            /**
             * get the list of materials bu productId 
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getMaterialsList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/basedata/product_info/get_materials_list',
                    method: 'post',
                    data
                });
            },
            /**
             * unbind the relationship between product and material
             *
             * @param {Object} data unbind data
             * @returns {Promise} http
             */
            unbindMaterialToProduct(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/basedata/product_info/unbind_material_to_product',
                    method: 'post',
                    data
                });
            },
            /**
             * bind the relationship between product and material
             *
             * @param {Object} data unbind data
             * @returns {Promise} http
             */
            bindMaterialToProduct(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/basedata/product_info/bind_material_to_product',
                    method: 'post',
                    data
                });
            },
            /**
             * bind the relationship between product and material
             *
             * @param {Object} params unbind data
             * @returns {Promise} http
             */
            selectMaterialByCode(params) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/basedata/product_info/select_material_by_code',
                    method: 'get',
                    params
                });
            },
        }
    };
    return service;
};
