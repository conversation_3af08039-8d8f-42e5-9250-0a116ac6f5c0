<template>
    <el-dialog
        class="custom-dialog"
        :visible.sync="visible"
        title="审核拒绝"
        @close="handleCancel"
        width="500px"
    >
        <snbc-form
            class="dialog-form"
            ref="formRef"
            :form="form"
            :config="formConfig"
        >
            <template #form-body>
                <div class="form-body">
                    <snbc-form-item
                        v-for="item in formItems"
                        :key="item.modelKey"
                        :config="item"
                    />
                </div>
            </template>
        </snbc-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="danger" @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleSure">确 定</el-button>
        </div>
    </el-dialog>
</template>
<script>
import { refuseItems } from '../formItems';
import SnbcForm from 'warehouse/components/snbc-form/SnbcForm.vue';
import SnbcFormItem from 'warehouse/components/snbc-form/SnbcFormItem.vue';
import FormRules from 'warehouse/common/form-rules/index.js';

const { inputRequired, selectRequired, maxLength } = FormRules;

const rules = {
    rejectType: [selectRequired('拒绝类型')],
    rejectReason: [inputRequired('拒绝原因'), maxLength(255)]
};

export default {
    name: 'AllocationRefuseDialog',
    components: {
        SnbcForm,
        SnbcFormItem
    },
    data() {
        return {
            visible: false,
            form: {
                rejectType: '',
                rejectReason: ''
            },
            formConfig: Object.freeze({
                elFormAttrs: {
                    rules,
                    'label-width': '80px'
                }
            }),
            allocateTaskCode: ''
        };
    },
    computed: {
        formItems() {
            return refuseItems.map((item) => {
                return {
                    modelObj: this.form,
                    elFormItemAttrs: {
                        label: item.name,
                        ...(item.elFormItemAttrs || {})
                    },
                    ...item
                };
            });
        }
    },
    methods: {
        showDialog(allocateTaskCode) {
            this.allocateTaskCode = allocateTaskCode;
            this.visible = true;
        },
        handleCancel() {
            this.visible = false;
            this.$refs.formRef.getFormRef().resetFields();
        },
        async handleSure() {
            try {
                await this.$refs.formRef.getFormRef().validate();
                const { code, message } =
                    await this.$service.warehouse.allocation.auditRejectAllocateTask(
                        {
                            ...this.form,
                            allocateTaskCode: this.allocateTaskCode
                        }
                    );
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('审核拒绝成功');
                this.handleCancel();
                this.$parent.goBack();
            } catch (e) {
                return e.message;
            }
        }
    }
};
</script>
<style lang="scss" scoped></style>
