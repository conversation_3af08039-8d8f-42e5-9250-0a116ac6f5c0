export default {
    data() {
        return {
            tableConfig: {
                elTableColumns: []
            },
            list: []
        };
    },
    methods: {
        init(trafficColumns, areaColumns) {
            if (this.content.settlementMethod === '流量') {
                this.tableConfig.elTableColumns = trafficColumns;
            } else {
                this.tableConfig.elTableColumns = areaColumns;
            }
            this.list = (this.content.details || []).map((item, index) => {
                item.index = index + 1;
                return item;
            });
        }
    }
};
