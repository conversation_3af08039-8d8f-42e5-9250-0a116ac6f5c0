<template>
    <el-form-item v-bind="elFormItemAttrs">
        <el-cascader v-model="config.modelObj[config.modelKey]" v-bind="elCascaderAttrs" :options="options" />
    </el-form-item>
</template>
<script>
export default {
    name: 'SnbcFormRegion',
    props: {
        /**
         * SnbcFormRegion组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    // 值示例: ['北京市', '北京市', '东城区']
                    modelKey: '',
                    // province|city|area
                    type: 'area',
                    elFormItemAttrs: {},
                    elCascaderAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-cascader组件默认属性设置
            defaultElCascaderAttrs: {
                clearable: true
            },
            // 省市区数据
            options: []
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-cascader组件应用属性
        elCascaderAttrs() {
            return {
                ...this.defaultElCascaderAttrs,
                placeholder: `请选择${this.config.name}`,
                ...(this.config.elCascaderAttrs || {})
            };
        },
        // 区域选择类型 province|city|area
        type() {
            return this.config.type || 'area';
        }
    },
    mounted() {
        // 获取省市区数据
        this.getRegionData();
    },
    methods: {
        // 获取省市区数据
        async getRegionData() {
            // 本地是否存储省市区数据
            const regionData = this.$storage.getSessionStorage('regionData');
            if (regionData) {
                this.handleOptions(regionData);
                return;
            }
            try {
                const res = await this.$service.warehouse.region.getRegionData();
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                // 本地存储
                this.$storage.setSessionStorage('regionData', result);
                this.handleOptions(result);
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 省市区数据处理
        handleOptions(regionData) {
            // 格式化数据
            this.options = regionData.map((province) => {
                if (this.type === 'city' || this.type === 'area') {
                    province.children = province.childrenList.map((city) => {
                        if (this.type === 'area') {
                            city.children = city.childrenList.map((area) => {
                                return {
                                    ...area,
                                    label: area.nodeName,
                                    value: area.nodeName
                                };
                            });
                        }
                        return {
                            ...city,
                            label: city.nodeName,
                            value: city.nodeName
                        };
                    });
                }
                return {
                    ...province,
                    label: province.nodeName,
                    value: province.nodeName
                };
            });
        }
    }
};
</script>
