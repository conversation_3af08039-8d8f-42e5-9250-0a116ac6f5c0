/*----------------------------------------------------------------
// 功能说明：关于页面布局相关样式定义
//
//----------------------------------------------------------------*/

@import './variables';

/* 公共样式部分 */
.full-width {
    width: 100%;
}
.line {
    width: 100%;
    height: 1px;
    background: $--border-color;
    border-radius: 1px;
}

/* ----------内容块header部分的布局-------------- */
// 顶层布局，布局容器
.view,
.container {
    width: 100%;
    height: 100%;
    margin: 0px;
    padding: 0px;
}

// 适用于多视图区域切换
.container > .view {
    height: 100%;
}

// 内容头部内容
.header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    padding: 0px;
    margin-bottom: 20px;
    font-size: 1.4rem;
    font-weight: bold;
    vertical-align: middle;
    flex-shrink: 0;

    // 内容头-标题
    .header__title {
        vertical-align: middle;
        height: 40px;
        line-height: 40px;
        min-width: 80px;
        display: inline-block;
        flex-shrink: 0;
    }
    .header__body {
        flex-grow: 1;
        text-align: right;
        display: inline-block;

        & > .el-input {
            max-width: 348px;
        }
    }
    // 内容头-操作区
    .header__btns {
        text-align: right;
        display: inline-block;
        flex-shrink: 0;

        & > i,
        & > .el-button,
        & > .el-dropdown {
            margin-left: 10px;
            padding: 0px 13px;
            cursor: pointer;
        }
    }

    // 解决按钮部分内容高度不是40，导致垂直不居中问题
    .header__title+.header__btns,
    .header__body+.header__btns{
        line-height: 40px;
    }
}
// 如果header不在内容开头，则居上距离20px
.header:not(:first-child) {
    margin-top: 20px;
}

// 内容面板
.panel {
    flex-shrink: 0;
}
// 内容区域，这个是自动分配空间的
.content {
    flex-grow: 1;
    overflow: auto;
}

.footer {
    width: 100%;
    text-align: center;
    flex-shrink: 0;
    padding: 0px 20px;
}

/* ----------页面布局相关样式-------------- */

//页面布局-视图区域
.app-container,
.components-container,
.view {
    width: 100%;
    margin: 0px;
    padding: $--view--padding-width;
    background-color: $--container--bg;
    height: calc(100vh - 105px);
    overflow: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    // 页面级别的头部，一般用于子页面返回部分
    & > .header {
        display: block;
        height: 40px;
        line-height: 40px;
        background-color: $--container--bg;
        margin-bottom: 0px;
        font-weight: 700;

        & > .el-button {
            font-weight: normal;
        }
    }

    // 非左右布局，内容区域为container
    & > .container {
        padding: $--padding-width;
        overflow: auto;
        background-color: $--view--bg;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        height: auto;
        // min-height: 100%;
        flex-grow: 1;
    }

    // 子页面带返回头部，内容部分高度需要减去header高度
    & > .header + .container {
        height: calc(100% - 40px);
    }

    // 用于整体页面的脚部
    & > .footer {
        padding: $--padding-width;
        background-color: $--view--bg;
        text-align: center;
    }

    // 页面布局-内容区域
    & > .el-container {
        display: flex;
        flex: 1;
        flex-direction: row;
        box-sizing: border-box;
        width: 100%;
        margin: 0px;
        padding: 0px;
        background-color: $--container--bg;
        height: 100%;

        & > .el-aside {
            flex-shrink: 0;
            width: $--view_left--width;
            height: 100%;
            padding: $--padding-width;
            background-color: $--view--bg;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;

            // 左边内容区域，使用垂直的flex布局。支持el-aside下面在多套一层container（也可以不套）
            &,
            & > .container {
                display: flex;
                justify-content: space-between;
                flex-direction: column;

                // 左侧内容区域(含tree),高度是自适应，并且显示滚动条
                & > .content,
                & > .el-tree {
                    flex-grow: 1;
                    overflow: auto;
                }

                // 左侧布局第一个按钮或者搜索框，默认指定下边距为10px
                // & > .container > .el-button:first-child,
                // & > .container > .el-input:first-child,
                & > .el-button:first-child,
                & > .el-input:first-child {
                    margin-bottom: 10px;
                    width: 100%;
                    flex-shrink: 0;
                }
            }
        }

        // 主内容区域，支持el-mian下面在多套一层container（也可以不套）
        & > .el-main {
            &,
            & > .container {
                overflow: auto;
                height: 100%;
                margin-left: $--view--split-width;
                padding: $--padding-width;
                background-color: $--view--bg;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            }
        }
    }
}
