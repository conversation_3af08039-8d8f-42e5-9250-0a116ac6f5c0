/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();
    return {
        basicInfoManagement: {
            test(data) {
                return http({
                    baseDomain: basePath.orderApi.base,
                    url: '/xxxxxx',
                    method: 'post',
                    data
                });
            },
            /**
             *  查询人员管理
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            getBasicInfoList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/userManage/user_and_region/get_warehouse_user_list',
                    method: 'post',
                    data
                });
            },
            /**
             * 新增人员管理基础信息
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            addBasicInfo(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/userManage/user_and_region/add_user_and_region',
                    method: 'post',
                    data
                });
            },
            /**
             * 获取区仓信息
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            getWarehouseInfo() {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/warehouse/base_info/search_start_warehouse',
                    method: 'post'
                });
            },
            /**
             * 获取供应商信息
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            getSupplierInfoList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/basedata/supplier_info/search_supplierList_by_condition',
                    method: 'post',
                    data
                });
            },
            /**
             * 新增供应商信息
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            addSupplierInfo(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/basedata/supplier_info/add_supplier',
                    method: 'post',
                    data
                });
            },
            /**
             * 更新供应商信息
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            updateSupplierInfo(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/basedata/supplier_info/update_supplier_by_id',
                    method: 'post',
                    data
                });
            },
            /**
             * 删除供应商信息
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            deleteSupplier(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/basedata/supplier_info/remove_supplier_by_id`,
                    method: 'get',
                    params: {
                        id: data
                    }
                });
            },
            /**
             * 获取供应商详细信息
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            getSupplierDetailInfo(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/basedata/supplier_info/get_supplier_by_id`,
                    method: 'get',
                    params: {
                        id: data
                    }
                });
            },
            /**
             * 查询人员可配置角色信息
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            getCanConfigRole() {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/userManage/user_and_region/get_can_config_role`,
                    method: 'get'
                });
            },
            /**
             * 查询人员可配置角色信息
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            getCanConfigWarehouse() {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/userManage/user_and_region/get_can_config_warehouse_info`,
                    method: 'get'
                });
            },
            /**
             * 操作人员状态
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            operatePersonState(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/userManage/user_and_region/update_state_by_user_and_role',
                    method: 'post',
                    data
                });
            },
            /**
             * 重置密码
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            resetPassword(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/userManage/user_and_region/update_password',
                    method: 'post',
                    data
                });
            },
            /**
             * 获取人员详情
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            getUserDetailInfo(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/userManage/user_and_region/get_user_info_by_userid_role',
                    method: 'post',
                    data
                });
            },
            /**
             * 更新用户管理范围
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            updateUserByUserId(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/userManage/user_and_region/update_region_by_user_and_role',
                    method: 'post',
                    data
                });
            },
            /**
             * 获取服务客户信息
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            searchServiceCustomer(data) {
                return http({
                    baseDomain: basePath.baseapi.base,
                    url: '/service_customer/search_service_customer',
                    method: 'post',
                    data
                });
            },
        }
    };
};
