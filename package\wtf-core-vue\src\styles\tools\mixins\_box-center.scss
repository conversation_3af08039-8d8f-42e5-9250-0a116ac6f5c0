/// 元素垂直水平居中
/// @param  {[string]} $justify      [center] 水平对齐方向,[false] 水平方向不设置
/// @param  {[string]} $align      [center] 垂直对齐方向,[false] 垂直方向不设置
/// @example
/// //SCSS
/// 
/// .justify-center {
///     @include box-center;
/// }
/// 
/// //CSS
/// 
/// .justify-cnter {
///     display: flex;
///     justify-content: center;
///     align-items: center;
/// }
/// 
/// <AUTHOR>

@mixin box-center($justify:center, $align: center) {
    display:flex;

    @if($align !=false) {
        align-items: $align;
    }
    @if($justify !=false) {
        justify-content: $justify;
    }
}

/// 使用translate实现水平垂直居中
/// @param {String} $direction [both] - 水平垂直,其它值`horizontal`和`vertical`
/// @example
/// //SCSS
/// .center{
///   @include center-translate;
/// }
/// //CSS
/// .center {
///   position: absolute;
///   top: 50%;
///   left: 50%;
///   transform: translate3d(-50%, -50%, 0);
/// }
@mixin center-translate($direction: both) {
    position: absolute;
    @if $direction == both {
        top: 50%;
        left: 50%;
        transform: translate3d(-50%, -50%, 0);
    }
    @else if $direction == horizontal {
        left: 50%;
        transform: translate3d(-50%, 0, 0);
    }
    @else if $direction == vertical {
        top: 50%;
        transform: translate3d(0, -50%, 0);
    }
}

