<template>
    <div class="view">
        <div class="content">
            <snbc-base-table v-show="tabsConfig.activeName === '在库资产'" ref="tableRef1" :table-config="tableConfig">
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
                <template #table-info-top>
                    <div class="assets-title">
                        在库总数:{{ statisticalTotal.inLibraryNumber }}，入库在途总数:{{
                            statisticalTotal.inTrainsAmountTotal
                        }}，无源总数:{{ statisticalTotal.passiveInboundTotal }}
                    </div>
                </template>
            </snbc-base-table>
            <snbc-base-table
                v-show="tabsConfig.activeName === '历史资产'"
                ref="tableRef2"
                :table-config="logTableConfig"
            >
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
            </snbc-base-table>
        </div>
    </div>
</template>

<script>
import Vue from 'vue';
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcTableTabs from 'warehouse/components/snbc-table/SnbcTableTabs.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import { mapState } from 'vuex';
import objectFactory from 'warehouse/common/object-factory/index.js';

const { cloneDeep } = Vue.prototype.$tools;

const {
    assetCode,
    assetType,
    customerAssetCode,
    productName,
    outWarehouseDateRange,
    inWarehouseDateRange,
    inState,
    assetState,
    warehouseNameRegion,
    customerSelect,
    input,
    select,
    erpCustomerName
} = FormItems;

const warehouseSelect = {
    ...warehouseNameRegion
};
// 在库资产查询列表
const assetInLibItems = [
    assetCode,
    assetType,
    customerSelect,
    customerAssetCode,
    productName,
    inWarehouseDateRange,
    warehouseSelect,
    inState,
    assetState,
    {
        ...input,
        name: '物料编码',
        modelKey: 'materialCode'
    },
    erpCustomerName
];

// 历史资产查询列表
const assetLogItems = [
    assetCode,
    assetType,
    customerAssetCode,
    productName,
    warehouseSelect,
    assetState,
    outWarehouseDateRange,
    inWarehouseDateRange,
    {
        ...select,
        name: '出库方式',
        component: 'SnbcFormSelect',
        modelKey: 'outType',
        elOptions: [
            { label: '安装', value: '安装' },
            { label: '安装提货', value: '安装提货' },
            { label: '异常出库', value: '异常出库' },
            { label: '杂项', value: '杂项' },
            { label: '调拨', value: '调拨' },
            { label: '货损出库', value: '货损出库' }
        ]
    },
    erpCustomerName
];

const logQueryParams = {
    assetCode: '',
    assetType: '',
    customerAssetCode: '',
    productName: '',
    inState: '',
    outWarehouseDateRange: [],
    inWarehouseDateRange: [],
    warehouseCode: '',
    assetState: '',
    outType: '',
    erpCustomerName: ''
};

const queryParams = {
    assetCode: '',
    assetType: '',
    customerAssetCode: '',
    productName: '',
    inState: '',
    outWarehouseDateRange: [],
    inWarehouseDateRange: [],
    warehouseCode: '',
    assetState: '',
    customerCode: '',
    materialCode: '',
    erpCustomerName: ''
};

const tabsConfig = {
    activeName: '在库资产',
    tabItems: ['在库资产', '历史资产']
};

const logPageParams = objectFactory('pageParams');
const pageParams = objectFactory('pageParams');

const allParams = {
    logQueryParams: cloneDeep(logQueryParams),
    queryParams: cloneDeep(queryParams),
    tabsConfig: cloneDeep(tabsConfig),
    logPageParams: cloneDeep(logPageParams),
    pageParams: cloneDeep(pageParams)
};

export default {
    name: 'WarehouseAssetsManagement',
    components: {
        SnbcBaseTable,
        SnbcTableTabs
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        const { getInLibraryList: inLibListApi, getAssetRecordList: recordListApi } = this.$service.warehouse.assets;
        return {
            inLibListApi,
            recordListApi,
            logTableConfig: {
                queryParams: allParams.logQueryParams,
                pageParams: allParams.logPageParams,
                queryConfig: {
                    items: [...assetLogItems],
                    elFormAttrs: {
                        'label-width': '120px'
                    }
                },
                queryApi: recordListApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '产品序列号',
                        prop: 'assetCode',
                        show: true,
                        minWidth: 280,
                        renderMode: 'button',
                        elButtonAttrs: {
                            type: 'text'
                        },
                        handleClick: this.handleClickCell
                    },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '出库方式',
                        prop: 'outType',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '资产状态',
                        prop: 'assetState',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '资产类型',
                        prop: 'assetType',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '所属客户全称',
                        prop: 'erpCustomerName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '客户资产编码',
                        prop: 'customerAssetCode',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '入库时间',
                        prop: 'intoTime',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '出库时间',
                        prop: 'outTime',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '在库库龄',
                        prop: 'storageDays',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '库龄总计',
                        prop: 'storageAgeTotal',
                        show: true,
                        minWidth: 100
                    }
                ],
                rowClassName: ({ row }) => {
                    if (row.assetState !== '正常' && row.assetState) {
                        return 'danger-row';
                    }
                    return '';
                },
                headerButtons: [
                    {
                        name: '导出',
                        type: 'primary',
                        handleClick: this.exportRecord,
                        permissionCode: 'DC'
                    }
                ]
            },
            tableConfig: {
                queryParams: allParams.queryParams,
                pageParams: allParams.pageParams,
                queryConfig: {
                    items: [...assetInLibItems],
                    elFormAttrs: {
                        'label-width': '120px'
                    },
                    seniorShow: true
                },
                queryApi: inLibListApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '产品序列号',
                        prop: 'assetCode',
                        show: true,
                        minWidth: 280,
                        renderMode: 'button',
                        elButtonAttrs: {
                            type: 'text'
                        },
                        handleClick: this.handleClickCell
                    },
                    {
                        label: '客户名称',
                        prop: 'customerName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '物料编码',
                        prop: 'materialCode',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 120
                    },
                    // 1在库、2入库在途、3安装出库、4异常出库
                    {
                        label: '资产状态',
                        prop: 'assetState',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '在库状态',
                        prop: 'inState',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '资产类型',
                        prop: 'assetType',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '所属客户全称',
                        prop: 'erpCustomerName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '客户资产编码',
                        prop: 'customerAssetCode',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '入库时间',
                        prop: 'intoTime',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '在库库龄',
                        prop: 'storageDays',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '库龄总计',
                        prop: 'storageAgeTotal',
                        show: true,
                        minWidth: 100
                    }
                ],
                headerButtons: [
                    {
                        name: '导出',
                        type: 'primary',
                        handleClick: this.exportInLib,
                        permissionCode: 'DC'
                    }
                ],
                importButtons: [
                    {
                        name: '导入',
                        type: 'primary',
                        accept: '.xlsx',
                        handleChange: this.importInLib,
                        permissionCode: 'QCZCDR'
                    }
                ],
                rowClassName: ({ row }) => {
                    if (row.assetState !== '正常' && row.assetState) {
                        return 'danger-row';
                    }
                    return '';
                },
                hooks: {
                    queryParamsHook: this.getStatisticalTotalList
                }
            },
            tabsConfig: {},
            statisticalTotal: {
                // 在库总数
                inLibraryNumber: 0,
                // 入库在途总数
                inTrainsAmountTotal: 0,
                // 无源入库
                passiveInboundTotal: 0,
                // 无源出库
                passiveOutboundTotal: 0
            }
        };
    },
    computed: {
        ...mapState(['permission'])
    },
    beforeCreate() {
        if (!this.$store.getters.toggleView) {
            // 支持内存缓存的页面参数
            allParams.logQueryParams = cloneDeep(logQueryParams);
            allParams.queryParams = cloneDeep(queryParams);
            allParams.pageParams = cloneDeep(pageParams);
            allParams.logPageParams = cloneDeep(logPageParams);
            allParams.tabsConfig = cloneDeep(tabsConfig);
        }
    },
    created() {
        // 非tag切换进入，重置页面参数至默认状态
        if (!this.$store.getters.toggleView) {
            this.$tools.setQueryParams.call(this);
        }
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.tabsConfig = Object.assign(allParams.tabsConfig, {
            handleTabClick: this.handleTabClick
        });
        this.handleTabClick();
    },
    methods: {
        // 资产信息查看
        handleClickCell(row) {
            this.$router.push({
                path: '/app/assets/operate-details',
                query: { assetCode: row.assetCode }
            });
        },
        // tab 标签点击
        handleTabClick() {
            if (this.tabsConfig.activeName === '在库资产') {
                this.$refs.tableRef1.queryList();
            } else {
                this.$refs.tableRef2.queryList();
            }
        },
        // 获取统计数据，查询时调用查询统计数据
        async getStatisticalTotalList() {
            const { code, result, message } = await this.$service.warehouse.assets.getStatisticalTotalList(
                this.tableConfig.queryParams.warehouseCode || ''
            );
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
            }
            Object.assign(this.statisticalTotal, result);
        },
        // 在库资产导出
        async exportInLib() {
            const stream = await this.$service.warehouse.assets.exportInLibraryList(this.tableConfig.queryParams);
            this.$tools.downloadExprotFile(stream, `在库资产`, 'xlsx', 'xlsx').catch((e) => {
                this.$tools.message.err(e || '导出失败');
            });
        },
        // 历史资产导出
        async exportRecord() {
            const stream = await this.$service.warehouse.assets.exportAssetRecordList(this.logTableConfig.queryParams);
            this.$tools.downloadExprotFile(stream, `历史资产`, 'xlsx', 'xlsx').catch((e) => {
                this.$tools.message.err(e || '导出失败');
            });
        },
        // 在库资产导入
        async importInLib({ raw }) {
            if (!raw.name || raw.name.split('.').pop() !== 'xlsx') {
                this.$tools.message.err('请上传xlsx格式的文件');
                return;
            }
            const formData = new FormData();
            formData.append('file', raw);
            const { code, result, message } = await this.$service.warehouse.assets.importAssetExcel(formData);
            if (code !== '000000') {
                this.$tools.message.err({
                    dangerouslyUseHTMLString: true,
                    message: message || '系统异常'
                });
                return;
            }
            if (result.fileUrl) {
                window.open(result.fileUrl);
                return;
            }
            this.$tools.message.suc('导入成功');
            this.$refs.tableRef1.queryList();
        }
    }
};
</script>

<style lang="scss" scoped>
.content {
    ::v-deep .el-tabs__content {
        padding: 0;
    }
}
</style>
