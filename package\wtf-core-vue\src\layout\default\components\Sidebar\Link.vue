<template>
  <component :is="type" v-bind="linkProps(to)">
    <slot />
  </component>
</template>

<script>

export default {
    props: {
        to: {
            type: String,
            required: true
        }
    },
    computed: {
        isExternal() {
            return this.$tools.isExternal(this.to);
        },
        type() {
            if (this.isExternal) {
                return 'a';
            }
            return 'router-link';
        }
    },
    methods: {
        linkProps(to) {
            if (this.isExternal) {
                return {
                    href: to,
                    rel: 'noopener'
                };
            }
            return {
                to: {
					path:to
				}
            };
        }
    }
};
</script>
