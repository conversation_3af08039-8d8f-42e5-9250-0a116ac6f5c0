//-------------------------------------------------------------------------------------
// Sass burger (https://github.com/jorenvanhee/sass-burger)
// Copyright 2016 <PERSON><PERSON>
// Licensed under MIT (http://joren.mit-license.org/)
//-------------------------------------------------------------------------------------


// Burger parts
//
// (---) top    -> &::before
// [---] middle -> &
// (---) bottom -> &::after

/// Burger
/// @param {number} $width  [30px] - 宽度
/// @param {number} $height  [5px] - 高度
/// @param {Number} $gutter  [3px] - 间距
/// @param {string} $color  [#000] - 线条颜色
/// @param {number} $border-radius  [0] - 圆角
/// @param {number} $transition-duration  [0] - 动效持续时间
/// @example
/// //SCSS
/// .burger {
///  @include burger;
/// }
/// //Output CSS
/// .burger {
///  position: relative;
///  margin-top: 8px;
///  margin-bottom: 8px;
///  user-select: none;
/// }
/// .burger, .burger::before, .burger::after {
///   display: block;
///   width: 30px;
///   height: 5px;
///   background-color: #000;
///   outline: 1px solid transparent;
///   transition-property: background-color, transform;
///   transition-duration: 0.3s;
/// }
/// .burger::before, .burger::after {
///   position: absolute;
///   content: "";
/// }
/// .burger::before {
///   top: -8px;
/// }
/// .burger::after {
///   top: 8px;
/// }
/// <AUTHOR> Van Hee
@mixin burger($width: 30px, $height: 5px, $gutter: 3px, $color: #000, $border-radius: 0, $transition-duration: .3s) {
    $burger-height: $height !global;
    $burger-gutter: $gutter !global;

    position: relative;
    margin-top: $height + $gutter;
    margin-bottom: $height + $gutter;

    user-select: none;

    // 1. Fixes jagged edges in Firefox, see issue #10.
    &,
    &::before,
    &::after {
        display: block;
        width: $width;
        height: $height;
        background-color: $color;
        outline: 1px solid transparent; // 1
        @if $border-radius != 0 {
            border-radius: $border-radius;
        }

        transition-property: background-color, transform;
        transition-duration: $transition-duration;
    }

    &::before, &::after {
        position: absolute;
        content: "";
    }

    &::before {
        top: -($height + $gutter);
    }

    &::after {
        top: $height + $gutter;
    }
}


/// Select parts of the burger
/// @example
/// //SCSS
/// .burger {
///  @include burger-top {
///    background-color: red;
///  }
/// }
/// //Output CSS
/// .burger,
/// .burger::before,
/// .burger::after {
///  background-color: red;
/// }
@mixin burger-parts {
    &,
    &::before,
    &::after {
        @content;
    }
}

/// @example
/// //SCSS
/// .burger {
///  @include burger-top {
///    background-color: red;
///  }
/// }
/// //Output CSS
/// .burger::before {
///  background-color: red;
/// }
@mixin burger-top {
    &::before {
        @content;
    }
}
/// @example
/// //SCSS
/// .burger {
///  @include burger-middle {
///    background-color: red;
///  }
/// }
/// //Output CSS
/// .burger {
///  background-color: red;
/// }
@mixin burger-middle {
    & {
        @content;
    }
}
/// @example
/// //SCSS
/// .burger {
///  @include burger-bottom {
///    background-color: red;
///  }
/// }
/// //Output CSS
/// .burger::after {
///  background-color: red;
/// }
@mixin burger-bottom {
    &::after {
        @content;
    }
}


/// Burger animations
/// @param {String} $color - auto
@mixin burger-to-cross($color: auto) {
    & {
        background-color: transparent;
    }
    @if ($color != auto) {
        &::before, &::after {
            background-color: $color;
        }
    }
    &::before {
        transform: translateY($burger-gutter + $burger-height) rotate(45deg);
    }
    &::after {
        transform: translateY(-($burger-gutter + $burger-height)) rotate(-45deg);
    }
}