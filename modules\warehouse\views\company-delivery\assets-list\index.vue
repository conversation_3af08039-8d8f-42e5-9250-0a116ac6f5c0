<template>
    <div class="view">
        <div class="content query-label-line2">
            <snbc-base-table ref="tableRef" :table-config="tableConfig" />
        </div>
    </div>
</template>

<script>
import Vue from 'vue';
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import CommonItems from 'warehouse/common/form-items/common-items.js';
import CompanyDeliveryTaskItems from 'warehouse/common/form-items/company-delivery-items.js';
import objectFactory from 'warehouse/common/object-factory/index.js';

const { cloneDeep } = Vue.prototype.$tools;

const { input, select } = CommonItems;
const { customerName, productName, companyDeliveryTaskCode } = CompanyDeliveryTaskItems;

const assetCode = {
    ...input,
    name: '产品序列号',
    modelKey: 'assetCode'
};
const assetCodeCustomer = {
    ...input,
    name: '客户资产编码',
    modelKey: 'assetCodeCustomer'
};
const deliveryListCode = {
    ...input,
    name: '公司发货清单编号',
    modelKey: 'deliveryListCode'
};
const state = {
    ...select,
    name: '到货状态',
    modelKey: 'state',
    elOptions: [
        { label: '已入库', value: '已入库' },
        { label: '入库在途', value: '入库在途' }
    ]
};
const erpOrderType = {
    ...select,
    name: '订单类型',
    modelKey: 'erpOrderType',
    elOptions: [
        { label: '销售订单', value: 'sale' },
        { label: '搬运订单', value: 'carry' }
    ]
};

// 查询参数
const queryParams = {
    assetCode: '',
    assetCodeCustomer: '',
    customerName: '',
    deliveryListCode: '',
    productName: '',
    state: '',
    // 公司发货任务编号
    taskCode: ''
};

// 查询区域配置项
const queryConfigItems = [
    assetCode,
    assetCodeCustomer,
    customerName,
    deliveryListCode,
    productName,
    state,
    companyDeliveryTaskCode
];

const pageParams = objectFactory('pageParams');

// 支持内存缓存的页面参数
const allParams = {
    queryParams: cloneDeep(queryParams),
    // 分页参数
    pageParams: cloneDeep(pageParams)
};

export default {
    name: 'CompanyDeliveryAssetsList',
    components: {
        SnbcBaseTable
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            tableConfig: {
                queryParams: allParams.queryParams,
                pageParams: allParams.pageParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: this.$service.warehouse.comDeliveryTask.getComDeliveryBillDetail,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '产品序列号',
                        prop: 'assetCode',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '客户资产编码',
                        prop: 'assetCodeCustomer',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '公司发货任务编号',
                        prop: 'deliveryTaskCode',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '公司发货清单编号',
                        prop: 'deliveryListCode',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '客户名称',
                        prop: 'customerName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '物料编号',
                        prop: 'materialCode',
                        show: true,
                        minWidth: 140
                    },
                    {
                        label: 'ERP订单类型',
                        prop: 'erpOrderTypeName',
                        show: true,
                        minWidth: 140
                    },
                    {
                        label: '到货状态',
                        prop: 'state',
                        show: true,
                        minWidth: 140
                    },
                    {
                        label: '到货时间',
                        prop: 'arrivalTime',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '创建时间',
                        prop: 'createTime',
                        show: true,
                        minWidth: 160
                    }
                ],
                operations: [
                    {
                        name: '详情',
                        type: 'primary',
                        handleClick: this.handleView
                    }
                ],
                hooks: {
                    tableListHook: this.tableListHook
                }
            }
        };
    },
    beforeCreate() {
        if (!this.$store.getters.toggleView) {
            allParams.queryParams = cloneDeep(queryParams);
            allParams.pageParams = cloneDeep(pageParams);
        }
    },
    created() {
        // 非tag切换进入，重置页面参数至默认状态
        if (!this.$store.getters.toggleView) {
            this.$tools.setQueryParams.call(this);
        }
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.queryList();
    },
    methods: {
        // 列表数据处理
        tableListHook(list) {
            list.map((item) => {
                item.erpOrderTypeName = this.$tools.getOptionName(item.erpOrderType, erpOrderType.elOptions);
                return item;
            });
        },
        // 详情操作
        handleView(row) {
            this.$router.push({
                path: '/app/assets/operate-log',
                query: { assetCode: row.assetCode }
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
