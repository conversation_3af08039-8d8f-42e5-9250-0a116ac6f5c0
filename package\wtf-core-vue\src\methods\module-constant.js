export let constant = null;
export function loadModuleConstant(moduleName, moduleConst) {
    if (moduleConst) {
        // 合并静态变量
        if (!constant) {
            // constant = Object.assign({}, moduleConst);
            constant = {};
            constant[moduleName] = moduleConst;
        } else {
            // 检测变量重复
            // checkRouteRepeat(constant, moduleConst, moduleName);
            // 合并到 constant 对象上
            // constant = Object.assign(constant, moduleConst);
            constant[moduleName] = moduleConst;
        }
    }
}
/* 
 * 说明: 如果不做模块隔离需要提示变量重复
 * const checkRouteRepeat = (obj1, obj2, moduleName) => {
    const repeatRoutes = Object.keys(obj1).filter(prop => {
        return Object.prototype.hasOwnProperty.call(obj2, prop)
    })
    if (repeatRoutes.length > 0) {
        console.log(
            `%c${moduleName}模块: 静态常量${repeatRoutes.join(",")}，同其他模块存在重复项，请修改...`,
            'background: #ec971f; padding: 5px; color: #fff; border-radius: 5px'
        )
    }
} */
