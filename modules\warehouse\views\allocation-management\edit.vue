<template>
    <div class="view">
        <div class="content">
            <el-page-header @back="goBack" content="调拨任务编辑"></el-page-header>
            <snbc-card :title="title">
                <template #card-body>
                    <snbc-form ref="snbcFormRef" :form="form" :config="formConfig">
                        <template #form-body>
                            <div class="form-body">
                                <snbc-form-item v-for="item in formItems" :key="item.modelKey" :config="item">
                                    <template
                                        v-if="item.modelKey === 'allocateTaskName' && preAllocateTaskName"
                                        slot="prepend"
                                        >{{ preAllocateTaskName }}</template
                                    >
                                </snbc-form-item>
                            </div>
                        </template>
                    </snbc-form>
                </template>
            </snbc-card>
            <snbc-card title="申请明细" v-if="type !== 'add' && !whetherSpecifyAsset">
                <template #card-body>
                    <allocation-application-details :list="applyDetails"> </allocation-application-details>
                </template>
            </snbc-card>
            <snbc-card title="任务明细">
                <template #card-body>
                    <allocation-task-details
                        ref="taskDetailsRef"
                        :applyDetailList="applyDetails"
                        :allocateTaskCode="allocateTaskCode"
                        :whetherSpecifyAsset="whetherSpecifyAsset"
                        :taskDetails="taskDetails"
                        :sourceWarehouseCode="form.sourceWarehouseCode"
                    >
                    </allocation-task-details>
                </template>
            </snbc-card>
            <div class="button-block">
                <el-button type="danger" @click="handleCancel">取 消</el-button>
                <el-button type="primary" @click="handleSave">保 存</el-button>
                <el-button type="primary" @click="handleSaveAndIssued" v-if="type === 'edit'">保存并下发</el-button>
            </div>
        </div>
    </div>
</template>
<script>
import functions from 'frame/mixins/functions.js';
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import SnbcForm from 'warehouse/components/snbc-form/SnbcForm.vue';
import SnbcFormItem from 'warehouse/components/snbc-form/SnbcFormItem.vue';
import AllocationApplicationDetails from './components/ApplicationDetails.vue';
import AllocationTaskDetails from './components/TaskDetails.vue';
import FormRules from 'warehouse/common/form-rules/index.js';
import formItems, { addItems } from './formItems.js';
import { Message } from 'element-ui';

const { inputRequired, selectRequired, maxLength } = FormRules;

const rules = {
    sourceWarehouseCode: [selectRequired('来源区仓')],
    expectedDate: [inputRequired('期望到货日期')],
    allocateTaskName: [inputRequired('调拨任务名称'), maxLength(64)],
    taskSource: [selectRequired('任务来源')],
    whetherSpecifyAsset: [selectRequired('是否是指定资产')],
    customerName: [inputRequired('客户名称'), maxLength(255)],
    targetWarehouseCode: [selectRequired('目标区仓')],
    remark: [maxLength(255)]
};

export default {
    name: 'AllocationEdit',
    components: {
        SnbcCard,
        SnbcForm,
        SnbcFormItem,
        AllocationApplicationDetails,
        AllocationTaskDetails
    },
    mixins: [functions],
    data() {
        return {
            tabs: [
                {
                    label: '任务明细',
                    id: 'taskDetails'
                },
                {
                    label: '物流信息',
                    id: 'logisticsInfo'
                },
                {
                    label: '操作记录',
                    id: 'operateLog'
                }
            ],
            form: {
                stockRequestCode: '',
                taskSource: '',
                targetWarehouseCode: '',
                targetWarehouseName: '',
                whetherSpecifyAsset: '',
                customerName: '',
                expectedDate: '',
                sourceWarehouseCode: '',
                sourceWarehouseName: '',
                allocateTaskName: '',
                remark: ''
            },
            formConfig: {
                elFormAttrs: {
                    rules,
                    'label-width': '120px'
                }
            },
            applyDetails: [],
            taskDetails: [],
            allocateTaskCode: '',
            whetherSpecifyAsset: false,
            // add / edit
            type: 'add',
            warehouseMap: {},
            title: '调拨任务基本信息'
        };
    },
    computed: {
        formItems() {
            let items;
            if (this.type === 'add') {
                items = addItems;
            } else if (this.whetherSpecifyAsset) {
                items = formItems.slice(1);
            } else {
                items = formItems;
            }

            return items.map((item) => {
                return {
                    modelObj: this.form,
                    elFormItemAttrs: {
                        label: item.name,
                        ...(item.elFormItemAttrs || {})
                    },
                    ...item
                };
            });
        },
        preAllocateTaskName() {
            if (this.form.sourceWarehouseName && this.form.targetWarehouseName) {
                const currentDate = new Date();
                const year = currentDate.getFullYear();
                const month = currentDate.getMonth() + 1;
                const day = currentDate.getDate();
                return `${this.form.sourceWarehouseName}至${this.form.targetWarehouseName}${year}${
                    month > 9 ? month : `0${month}`
                }${day > 9 ? day : `0${day}`}调拨`;
            }
            return '';
        }
    },
    watch: {
        'form.sourceWarehouseCode': function (newVal, oldVal) {
            this.form.sourceWarehouseName = this.warehouseMap[newVal];
            if (!this.whetherSpecifyAsset && oldVal) {
                this.queryAllocateDetailNotSpecified(newVal, oldVal);
            }
        },
        'form.targetWarehouseCode': function (newVal, oldVal) {
            this.form.targetWarehouseName = this.warehouseMap[newVal];
        }
    },
    created() {
        this.isHasMenu();
        this.initNativeDrop();
        this.init();
    },
    methods: {
        // 初始化
        async init() {
            this.allocateTaskCode = this.$route.query.allocateTaskCode;
            if (this.allocateTaskCode) {
                this.title = `调拨任务编号：${this.allocateTaskCode}`;
            }
            this.whetherSpecifyAsset = !!Number(this.$route.query.whetherSpecifyAsset);
            this.form.whetherSpecifyAsset = Number(this.$route.query.whetherSpecifyAsset);
            this.type = this.$route.query.type;
            await this.getAllWarehouse();
            if (this.type === 'add') return;
            this.getAllocateTaskBaseInfo();
            // 非指定资产调拨查询
            if (!this.whetherSpecifyAsset) {
                this.queryAllocateDetailNotSpecified();
            } else {
                this.querySpecifyAllocateDetailEditPage();
            }
        },
        // 返回
        goBack() {
            this.$store
                .dispatch('tagsView/delView', {
                    path: '/app/allocation/allocation-edit',
                    name: 'AllocationEdit'
                })
                .then(() => {
                    this.$store.commit('tagsView/TOGGLE_VIEW', true);
                    this.$router.go(-1);
                });
        },
        async handleCancel() {
            try {
                await this.$tools.confirm('确认取消吗？');
                this.goBack();
            } catch (e) {
                return e.message;
            }
        },
        // 保存前的数据处理和校验
        async handleValidate() {
            await this.$refs.snbcFormRef.getFormRef().validate();
            if (!this.$refs.taskDetailsRef.list.length) {
                this.$tools.message.warning('任务明细不能为空');
                return;
            }
            const params = { ...this.form };
            // 指定资产调拨和非指定资产调拨
            if (this.whetherSpecifyAsset) {
                params.specifyAssetDetailList = this.$refs.taskDetailsRef.getTaskDetails();
                if (
                    params.specifyAssetDetailList.some((item) => {
                        return item.warehouseCode !== this.form.sourceWarehouseCode;
                    })
                ) {
                    this.$tools.message.warning('存在指定资产不属于来源区仓');
                    return;
                }
            } else {
                params.detailList = this.$refs.taskDetailsRef.getTaskDetails();
                params.detailList.forEach((item) => {
                    item.initialPlanNumber = item.allocateNum;
                });
                const isEmpty = params.detailList.some((item) => item.allocateNum <= 0);
                if (isEmpty) {
                    this.$tools.message.warning('调拨数量不能为0');
                    return;
                }
            }
            params.allocateTaskName = this.preAllocateTaskName + params.allocateTaskName;
            if (params.allocateTaskName.length > 64) {
                this.$tools.message.warning('调拨任务名称不能超过 64 个字符');
                return;
            }
            return params;
        },
        // 保存不下发或者是新增调拨任务
        async handleSave() {
            try {
                const params = await this.handleValidate();
                if (!params) return;
                await this.$tools.confirm('确认保存吗？');
                const { code, message } = await this.$service.warehouse.allocation[
                    !this.allocateTaskCode ? 'addAllocateTask' : 'editAllocateTask'
                ](params);
                if (code !== '000000') {
                    Message.error({
                        dangerouslyUseHTMLString: true,
                        message: message || '系统异常'
                    });
                    return;
                }
                this.$tools.message.suc('调拨任务保存成功');
                this.goBack();
            } catch (e) {
                return e.message;
            }
        },
        // 保存并下发
        async handleSaveAndIssued() {
            try {
                const params = await this.handleValidate();
                if (!params) return;
                await this.$tools.confirm('确认保存并下发吗？');
                const { code, message } = await this.$service.warehouse.allocation.editAllocateTask({
                    issued: true,
                    ...params
                });
                if (code !== '000000') {
                    Message.error({
                        dangerouslyUseHTMLString: true,
                        message: message || '系统异常'
                    });
                    return;
                }
                this.$tools.message.suc('调拨任务保存并下发成功');
                this.goBack();
                return;
            } catch (e) {
                return e.message;
            }
        },
        // 查询非指定调拨任务任务明细（含申请明细）
        queryAllocateDetailNotSpecified(warehouseCode, oldVal) {
            this.$service.warehouse.allocation
                .queryAllocateDetailNotSpecified(this.allocateTaskCode, warehouseCode)
                .then(({ code, result, message }) => {
                    if (code !== '000000') {
                        this.$tools.message.err(message || '系统异常');
                        return;
                    }
                    this.applyDetails = result.applyDetailList.map((item, index) => {
                        return {
                            ...item,
                            index: index + 1
                        };
                    });
                    this.taskDetails = warehouseCode && oldVal ? [] : result.taskDetailList;
                });
        },
        // 询指定调拨任务任务明细
        querySpecifyAllocateDetailEditPage() {
            this.$service.warehouse.allocation
                .querySpecifyAllocateDetailEditPage(this.allocateTaskCode)
                .then(({ code, result, message }) => {
                    if (code !== '000000') {
                        this.$tools.message.err(message || '系统异常');
                        return;
                    }
                    this.taskDetails = result;
                });
        },
        // 调拨任务基础信息
        getAllocateTaskBaseInfo() {
            this.$service.warehouse.allocation
                .getAllocateTaskBaseInfo(this.allocateTaskCode)
                .then(({ code, result, message }) => {
                    if (code !== '000000') {
                        this.$tools.message.err(message || '系统异常');
                        return;
                    }
                    Object.assign(this.form, result);
                    const taskName = this.form.allocateTaskName;
                    if (typeof taskName === 'string' && taskName.includes('调拨')) {
                        this.form.allocateTaskName = taskName.substring(taskName.indexOf('调拨') + 2);
                    }
                });
        },
        // 获取全部区仓
        async getAllWarehouse() {
            const { code, result, message } = await this.$service.warehouse.warehouseBaseInformation.getAll();
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
                return;
            }
            result.forEach(({ warehouseCode, warehouseName }) => {
                this.warehouseMap[warehouseCode] = warehouseName;
            });
        }
    }
};
</script>
<style lang="scss" scoped>
@import '../../styles/mixins.scss';
.content {
    @include form-textarea(48px);
    .button-block {
        display: flex;
        justify-content: center;
        margin-top: 16px;
    }
}
</style>
