<template>
    <div class="view">
        <div class="content">
            <snbc-base-table
                ref="tableRef"
                :table-config="tableConfig"
                @query="getTotalInfo"
            >
                <template #table-info-top>
                    <div class="assets-title">
                        产品数量:{{
                            statisticalTotal.totalProductNum
                        }}，产品总数:{{
                            statisticalTotal.totalTotalCount
                        }}，在库总数:{{ statisticalTotal.totalInStockCount }},
                        干线在途总数:{{
                            statisticalTotal.totalTransitInboundCount
                        }}, 调拨在途总数:{{
                            statisticalTotal.totalTransferInTransitCount
                        }}, 已安装总数:{{
                            statisticalTotal.totalInstalledCount
                        }}, 丢失总数:{{ statisticalTotal.totalLostCount }},
                        货损总数:{{ statisticalTotal.totalDamagedCount }}
                    </div>
                </template>
            </snbc-base-table>
        </div>
    </div>
</template>
<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import formItems from 'warehouse/common/form-items/index.js';

const { productMultiSelect } = formItems;

export default {
    name: 'ProductInLibStatistics',
    components: {
        SnbcBaseTable
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        const {
            getProductInventoryList: listApi,
            getProductInventoryTotalList: getTotal,
            exportProductInventor: exportXlsx
        } = this.$service.warehouse.product;
        return {
            listApi,
            getTotal,
            exportXlsx,
            tableConfig: {
                queryParams: {
                    productNameList: []
                },
                queryConfig: {
                    items: [
                        {
                            ...productMultiSelect,
                            modelKey: 'productNameList',
                            type: 'name'
                        }
                    ],
                    elFormAttrs: {
                        'label-width': '120px'
                    }
                },
                queryApi: listApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 140,
                        renderMode: 'button',
                        elButtonAttrs: {
                            type: 'text'
                        },
                        handleClick: this.handleClickCell
                    },
                    {
                        label: '总数',
                        prop: 'totalCount',
                        show: true,
                        minWidth: 120,
                        elTableColumnAttrs: {
                            sortable: 'custom'
                        }
                    },
                    {
                        label: '在库数量',
                        prop: 'inStockCount',
                        show: true,
                        minWidth: 160,
                        elTableColumnAttrs: {
                            sortable: 'custom'
                        }
                    },
                    {
                        label: '干线在途',
                        prop: 'transitInboundCount',
                        show: true,
                        minWidth: 120,
                        elTableColumnAttrs: {
                            sortable: 'custom'
                        }
                    },
                    {
                        label: '调拨在途',
                        prop: 'transferInTransitCount',
                        show: true,
                        minWidth: 180,
                        elTableColumnAttrs: {
                            sortable: 'custom'
                        }
                    },
                    {
                        label: '货损数量',
                        prop: 'damagedCount',
                        show: true,
                        minWidth: 120,
                        elTableColumnAttrs: {
                            sortable: 'custom'
                        }
                    },
                    {
                        label: '丢失数量',
                        prop: 'lostCount',
                        show: true,
                        minWidth: 120,
                        elTableColumnAttrs: {
                            sortable: 'custom'
                        }
                    },
                    {
                        label: '已安装数量',
                        prop: 'inStalledCount',
                        show: true,
                        minWidth: 120,
                        elTableColumnAttrs: {
                            sortable: 'custom'
                        }
                    },
                    {
                        label: '平均库龄',
                        prop: 'averageAge',
                        show: true,
                        minWidth: 120,
                        elTableColumnAttrs: {
                            sortable: 'custom'
                        }
                    }
                ],
                headerButtons: [
                    {
                        name: '导出',
                        type: 'primary',
                        handleClick: this.export
                    }
                ],
                selectionAble: true
            },
            statisticalTotal: {
                totalMaterialNum: 0,
                totalTotalCount: 0,
                totalInStockCount: 0,
                totalTransitInboundCount: 0,
                totalTransferInTransitCount: 0,
                totalInstalledCount: 0,
                totalDamagedCount: 0,
                totalLostCount: 0,
                totalProductNum: 0
            }
        };
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.handleQuery();
    },
    methods: {
        handleClickCell(item) {
            this.$router.push({
                path: '/app/product-in-lib/distribution',
                query: {
                    productName: item.productName
                }
            });
        },
        async getTotalInfo() {
            const { code, message, result } = await this.getTotal(
                this.tableConfig.queryParams
            );
            if (code !== '000000') {
                this.$tools.message.err(
                    message || '获取统计数据失败，请刷新重试'
                );
                return;
            }
            Object.assign(this.statisticalTotal, result);
        },
        async export() {
            const stream = await this.exportXlsx(this.tableConfig.queryParams);
            this.$tools
                .downloadExprotFile(stream, `产品在库统计`, 'xlsx', 'xlsx')
                .then(() => {
                    this.$tools.message.suc('导出成功');
                })
                .catch((e) => {
                    this.$tools.message.err(e || '导出失败');
                });
        }
    }
};
</script>
<style lang="scss" scoped></style>
