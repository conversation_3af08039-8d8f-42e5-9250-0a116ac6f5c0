/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        warehouseBaseInformation: {
            /**
             * 新增
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            add(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/warehouse/base_info/save/0',
                    method: 'post',
                    data
                });
            },
            /**
             * 删除
             * @param {String} warehouseCode 区仓编码
             * @returns {Promise} http请求
             */
            remove(warehouseCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/warehouse/base_info/delete`,
                    method: 'get',
                    params: {
                        warehouseCode
                    }
                });
            },
            /**
             * 编辑
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            edit(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/warehouse/base_info/save/1',
                    method: 'post',
                    data
                });
            },
            /**
             * 启用或停用
             * @param {String} warehouseCode 区仓编码
             * @param {String} warehouseState 区仓状态
             * @returns {Promise} http请求
             */
            editState(warehouseCode, warehouseState) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/warehouse/base_info/start_or_stop`,
                    method: 'get',
                    params: {
                        warehouseCode,
                        warehouseState
                    }
                });
            },
            /**
             * 获取全部区仓编码和名称
             * @returns {Promise} http请求
             */
            getAll() {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/warehouse/base_info/query_all_warehouse',
                    method: 'post'
                });
            },
            /**
             * 获取启用区仓编码和名称
             * @returns {Promise} http请求
             */
            getEnableWarehouse() {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/warehouse/base_info/search_start_warehouse',
                    method: 'post'
                });
            },
            /**
             * 查询客户下拉框数据
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            queryRegionWarehouse() {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/warehouse/base_info/query_region_warehouse',
                    method: 'post'
                });
            },
            /**
             * 列表查询
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            list(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/warehouse/base_info/query_base_info_list',
                    method: 'post',
                    data
                });
            },
            /**
             * 区仓客户列表查询
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            queryCustomerPage(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/basedata/customer_info/page`,
                    method: 'post',
                    data
                });
            },
            /**
             * 区仓客户入驻
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            addCustomer(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/basedata/customer_info/add`,
                    method: 'post',
                    data
                });
            },
            /**
             * 区仓客户编辑
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            editCustomer(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/basedata/customer_info/edit`,
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
