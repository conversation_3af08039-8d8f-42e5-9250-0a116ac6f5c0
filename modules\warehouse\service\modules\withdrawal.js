import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();
    const service = {
        withdrawal: {
            /**
             * 撤机任务列表
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/task/return_warehouse_task/list',
                    method: 'post',
                    data
                });
            },
            /**
             * 撤机任务导出
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            downloadList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/task/return_warehouse_task/download_list',
                    method: 'post',
                    responseType: 'blob',
                    data
                });
            },
            /**
             * 撤机任务详情
             *
             * @param {Object} params 查询条件
             * @returns {Promise} http
             */
            getDetail(params) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/task/return_warehouse_task_app/detail',
                    method: 'get',
                    params
                });
            },
            /**
             * 撤机任务详情列表
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getDetailList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/detail/return_warehouse_task/list',
                    method: 'post',
                    data
                });
            }
        }
    };
    return service;
};