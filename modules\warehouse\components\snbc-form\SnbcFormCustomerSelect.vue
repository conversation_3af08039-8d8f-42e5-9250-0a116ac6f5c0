<template>
    <el-form-item v-bind="elFormItemAttrs">
        <el-select v-model="config.modelObj[config.modelKey]" v-bind="elSelectAttrs" @change="handleChange">
            <select-all :config="selectAllConfig" />
            <el-option v-for="(option, index) in elOptions" :key="index" :label="option.label" :value="option.value" />
        </el-select>
    </el-form-item>
</template>
<script>
import SelectAll from './components/SelectAll.vue';

export default {
    name: 'SnbcFormCustomerSelect',
    components: { SelectAll },
    props: {
        /**
         * SnbcFormSelect组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elFormItemAttrs: {},
                    elSelectAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-select组件默认属性设置
            defaultElSelectAttrs: {
                'filterable': true,
                'clearable': true,
                'collapse-tags': true
            },
            // 下拉数据
            elOptions: []
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-select组件应用属性
        elSelectAttrs() {
            return {
                ...this.defaultElSelectAttrs,
                placeholder: `请选择${this.config.name}`,
                ...(this.config.elSelectAttrs || {})
            };
        },
        // 全选组件配置
        selectAllConfig() {
            return {
                ...this.config,
                elOptions: this.elOptions
            };
        }
    },
    created() {
        this.queryWarehouseCustomer();
    },
    methods: {
        // 区仓产品数据查询
        async queryWarehouseCustomer() {
            try {
                const res =
                    await this.$service.warehouse.warehouseSelectData.getAllCustomer();
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.elOptions = result.map((item) => {
                    return {
                        ...item,
                        label: item.customerName,
                        value: item.customerCode
                    };
                });
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 下拉数据切换操作
        handleChange() {
            this.config.changeHandler && this.config.changeHandler();
        }
    }
};
</script>
