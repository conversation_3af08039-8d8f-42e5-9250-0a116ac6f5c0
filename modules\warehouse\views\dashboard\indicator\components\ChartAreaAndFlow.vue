<template>
    <!-- 区仓面积和流量 -->
    <div ref="chartRef" class="chart-box" />
</template>
<script>
import moment from 'moment';
import echarts from 'echarts';
import resize from 'wtf-core-vue/src/components/Charts/mixins/resize';

export default {
    name: 'ChartFeeAndIncome',
    mixins: [resize],
    data() {
        return {
            // 图表实例
            chart: null
        };
    },
    mounted() {
        this.chart = echarts.init(this.$refs.chartRef);
    },
    beforeDestroy() {
        if (!this.chart) {
            return;
        }
        this.chart.dispose();
        this.chart = null;
    },
    methods: {
        // 清除图表
        clearChart() {
            this.chart.clear();
        },
        handleData(data) {
            let trafficMax = 0;
            let warehouseUseAreaMax = 0;
            data.map((item) => {
                item.traffic = Number(item.traffic);
                item.warehouseUseArea = Number(item.warehouseUseArea);
                trafficMax = item.traffic > trafficMax ? item.traffic : trafficMax;
                warehouseUseAreaMax =
                    item.warehouseUseArea > warehouseUseAreaMax ? item.warehouseUseArea : warehouseUseAreaMax;
                item.statisticsDate = moment(item.statisticsDate).format('YY年MM月');
                return item;
            });
            return {
                data,
                trafficMax,
                warehouseUseAreaMax
            };
        },
        // eslint-disable-next-line max-lines-per-function
        drawChart(result = []) {
            const { data, trafficMax, warehouseUseAreaMax } = this.handleData(result);
            this.clearChart();
            this.chart.setOption({
                color: ['#26BAD5', '#7ED321'],
                title: {
                    left: 'center',
                    top: '10px',
                    text: '区仓面积和流量'
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['区仓面积(㎡)', '流量'],
                    itemHeight: 10,
                    textStyle: {
                        color: '#000'
                    },
                    right: 10,
                    top: 10
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: 20,
                    top: 80,
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: data.map((item) => item.statisticsDate),
                    axisTick: {
                        alignWithLabel: true
                    },
                    axisLabel: {
                        interval: 0,
                        // X轴三个字一换行
                        formatter: (value, idx) => {
                            return this.$tools.formatAxisLabel(value);
                        }
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '区仓面积(㎡)',
                        min: 0,
                        max: parseInt(warehouseUseAreaMax / 10, 10) * 10 * 2,
                        splitLine: {
                            lineStyle: {
                                type: 'dashed',
                                color: '#303437'
                            }
                        }
                    },
                    {
                        type: 'value',
                        name: '流量',
                        min: 0,
                        max: trafficMax,
                        position: 'right',
                        splitLine: {
                            show: false
                        },
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: '#000000'
                            }
                        }
                    }
                ],
                series: [
                    {
                        type: 'bar',
                        yAxisIndex: 0,
                        barMaxWidth: 20,
                        data: data.map((item) => {
                            return {
                                name: item.statisticsDate,
                                value: item.warehouseUseArea
                            };
                        }),
                        itemStyle: {
                            normal: {
                                label: {
                                    show: true,
                                    position: 'top',
                                    color: '#000000'
                                }
                            }
                        },
                        name: '区仓面积(㎡)'
                    },
                    {
                        type: 'line',
                        yAxisIndex: 1,
                        data: data.map((item) => {
                            return {
                                name: item.statisticsDate,
                                value: item.traffic
                            };
                        }),
                        itemStyle: {
                            normal: {
                                label: {
                                    show: true,
                                    position: 'top',
                                    color: '#000000'
                                }
                            }
                        },
                        name: '流量'
                    }
                ]
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.chart-box {
    width: calc(50% - 5px);
    height: 300px;
    border: 1px solid #dee5e7;
    background: #e6ebf5;
}
</style>
