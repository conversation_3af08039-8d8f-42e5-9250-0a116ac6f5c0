<template>
    <snbc-card :title="title">
        <template #card-body>
            <snbc-form ref="snbcFormRef" :form="list" :config="formConfig">
                <template #form-body>
                    <template v-for="(item, index) in formItems">
                        <snbc-form-item v-if="item.modelKey !== 'taskExpandName'" :key="index" :config="item" />
                        <snbc-form-item v-if="item.modelKey === 'taskExpandName'" :key="index" :config="item">
                            <template #prepend>
                                {{ item.modelObj[item.modelValueKey] }}
                            </template>
                        </snbc-form-item>
                    </template>
                </template>
            </snbc-form>
            <snbc-table-list :list="taskDetailList" :config="tableConfig" />
        </template>
    </snbc-card>
</template>
<script>
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import SnbcForm from 'warehouse/components/snbc-form/SnbcForm.vue';
import SnbcFormItem from 'warehouse/components/snbc-form/SnbcFormItem.vue';
import SnbcTableList from 'warehouse/components/snbc-table/SnbcTableList.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import FormRules from 'warehouse/common/form-rules/index.js';

const { input, expectedArrivalDate, remark } = FormItems;
const { dateRequired, inputRequired } = FormRules;
const taskExpandName = {
    ...input,
    name: '公司发货任务名称',
    modelKey: 'taskExpandName',
    modelValueKey: 'taskName',
    elInputAttrs: {
        maxlength: 20
    }
};

const rules = {
    taskExpandName: [inputRequired('公司发货任务名称')],
    expectedArrivalDate: [dateRequired('期望到货日期')]
};
export default {
    name: 'AllocationTask',
    components: {
        SnbcCard,
        SnbcForm,
        SnbcFormItem,
        SnbcTableList
    },
    props: {
        list: {
            type: Object,
            default() {
                return {};
            }
        },
        config: {
            type: Object,
            default() {
                return {};
            }
        }
    },
    data() {
        return {
            formConfig: {
                elFormAttrs: {
                    rules,
                    'label-width': '160px'
                }
            },
            tableConfig: {
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '物料编码',
                        prop: 'materialCode',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '数量',
                        prop: 'sendNumber',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '公司仓库',
                        prop: 'subInventoryName',
                        show: true,
                        minWidth: 120
                    }
                ]
            }
        };
    },
    computed: {
        // 卡片标题展示
        title() {
            const name = {
                carry: '公司发货任务-搬运订单',
                sale: '公司发货任务-销售订单'
            }[this.list.orderType];
            return name || '公司发货任务';
        },
        formItems() {
            return [taskExpandName, expectedArrivalDate, remark].map((item) => {
                return {
                    modelObj: this.list,
                    elFormItemAttrs: {
                        label: item.name,
                        ...(item.elFormItemAttrs || {})
                    },
                    ...item
                };
            });
        },
        taskDetailList() {
            return this.list.taskDetailList.map((item, index) => {
                item.index = index + 1;
                return item;
            });
        }
    },
    methods: {
        getFormRef() {
            return this.$refs.snbcFormRef.getFormRef();
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__content {
    width: 60%;
}
</style>
