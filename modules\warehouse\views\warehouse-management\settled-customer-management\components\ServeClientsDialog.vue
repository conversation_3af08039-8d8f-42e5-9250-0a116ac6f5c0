<template>
    <SnbcBaseDialog ref="snbcBaseDialogDialog" :config="dialogConfig">
        <template slot="dialog-body">
            <SnbcBaseTable ref="tableRef" :table-config="tableConfig" />
        </template>
    </SnbcBaseDialog>
</template>
<script>
import SnbcBaseDialog from 'warehouse/components/snbc-dialog/SnbcBaseDialog.vue';
import FormItems from 'warehouse/common/form-items/index.js';

const { input } = FormItems;

export default {
    name: 'ServeClientsDialog',
    components: {
        SnbcBaseDialog,
        SnbcBaseTable: () => import('warehouse/components/snbc-table/SnbcBaseTable.vue')
    },
    data() {
        const { searchServiceCustomer: listApi } = this.$service.warehouse.basicInfoManagement;
        return {
            tableConfig: {
                queryApi: listApi,
                queryParams: {
                    abbreviations: ''
                },
                queryConfig: {
                    items: [
                        {
                            ...input,
                            name: '服务客户简称',
                            modelKey: 'abbreviations'
                        }
                    ]
                },
                elTableColumns: [
                    {
                        label: '序号',
                        prop: 'index',
                        show: true,
                        elTableColumnAttrs: {
                            width: 60
                        }
                    },
                    {
                        label: '服务客户简称',
                        prop: 'abbreviations',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '服务客户名称',
                        prop: 'name',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '关联ERP客户数量',
                        prop: 'refErpCustomerNum',
                        show: true,
                        elTableColumnAttrs: {
                            width: 160
                        }
                    }
                ],
                operations: [
                    {
                        name: '选择',
                        type: 'primary',
                        handleClick: this.handleChoose
                    }
                ],
                hooks: {
                    tableListHook(list) {
                        list.forEach((item) => {
                            if (!item.refErpCustomerNum) item.refErpCustomerNum = 0;
                        });
                    }
                }
            },
            dialogConfig: Object.freeze({
                elDialogAttrs: {
                    'title': '服务客户列表',
                    'width': '800px',
                    'append-to-body': true
                },
                hasFooter: false
            })
        };
    },
    methods: {
        showDialog() {
            this.$refs.snbcBaseDialogDialog.openDialog();
            this.$nextTick(() => {
                this.handleQuery();
            });
        },
        handleQuery() {
            this.$refs.tableRef.handleReset();
        },
        async handleChoose(row) {
            if (!row.refErpCustomerNum) {
                return this.$tools.message.err('所选服务客户的关联ERP客户数量为0，请前往“服务客户管理”中关联ERP客户');
            }
            this.$emit('choose', row);
            this.$refs.snbcBaseDialogDialog.hideDialog();
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .form-item-height {
    width: 50%;
}

::v-deep .popover {
    display: none;
}
</style>
