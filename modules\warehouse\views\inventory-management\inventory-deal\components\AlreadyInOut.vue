<template>
    <snbc-base-table ref="tableRef" :table-config="tableConfig">
        <template #tabs>
            <snbc-table-tabs :tabs-config="tabsConfig" />
        </template>
    </snbc-base-table>
</template>

<script>
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcTableTabs from 'warehouse/components/snbc-table/SnbcTableTabs.vue';

export default {
    name: 'AlreadyInOut',
    components: {
        SnbcBaseTable,
        SnbcTableTabs
    },
    props: {
        queryConfig: {
            type: Object,
            default() {
                return {};
            }
        },
        tabsConfig: {
            type: Object,
            default() {
                return {};
            }
        }
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            tableConfig: {
                queryApi: this.$service.warehouse.inventoryManagement.getHandledData,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '物料编码',
                        prop: 'materialCode',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '产品序列号',
                        prop: 'assetCode',
                        show: true,
                        minWidth: 180
                    },
                    {
                        label: '客户名称',
                        prop: 'customerName',
                        show: true,
                        minWidth: 60
                    },
                    {
                        label: '区仓',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '盘点结果',
                        prop: 'invResult',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '操作类型',
                        prop: 'sundryType',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '异常出入库单号',
                        prop: 'sundryCode',
                        show: true,
                        minWidth: 160
                    }
                ],
                hooks: {
                    queryParamsHook: this.queryParamsHook
                }
            }
        };
    },
    mounted() {
        this.tableConfig = {
            ...this.queryConfig,
            ...this.tableConfig
        };
    },
    methods: {
        queryParamsHook(params) {
            params.planCode = this.$route.query.planCode;
            params.type = 'SUNDRY';
            this.queryConfig.queryParamsHook && this.queryConfig.queryParamsHook(params);
        },
        handleQuery() {
            this.$refs.tableRef.queryList();
        }
    }
};
</script>

<style lang="scss" scoped></style>
