<template>
    <div class="view">
        <!-- 公司发货任务列表 -->
        <div class="content query-label-line2">
            <snbc-base-table ref="tableRef" :table-config="tableConfig">
                <template #table-info-top>
                    <el-descriptions class="margin-bottom-10" border :column="8">
                        <el-descriptions-item
                            v-for="(item, index) in countList"
                            :key="index"
                            v-bind="$tools.elDescItemLabelAttrs(item.name)"
                        >
                            {{ item.value }}
                        </el-descriptions-item>
                    </el-descriptions>
                </template>
            </snbc-base-table>
        </div>
    </div>
</template>

<script>
import Vue from 'vue';
import moment from 'moment';
import functions from 'frame/mixins/functions.js';
import textRender from 'warehouse/common/text-render/index.js';
import objectFactory from 'warehouse/common/object-factory/index.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import warehouseItems from 'warehouse/common/form-items/warehouse-items.js';

const { cloneDeep } = Vue.prototype.$tools;

// 当前日期前一天
const lastDay = moment().subtract(1, 'days').format('YYYY-MM-DD');
// 查询参数
const queryParams = {
    warehouseCode: ''
};
const pageParams = objectFactory('pageParams');
// 查询区域配置项
const queryConfigItems = [warehouseItems.warehouseSelect];

// 支持内存缓存的页面参数
const allParams = {
    queryParams: cloneDeep(queryParams),
    // 分页参数
    pageParams: cloneDeep(pageParams)
};

export default {
    name: 'WarehouseRealtimeReport',
    components: {
        SnbcBaseTable
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            tableConfig: {
                queryParams: allParams.queryParams,
                pageParams: allParams.pageParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: this.$service.warehouse.board.getDailyStatistics,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 60 },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 140,
                        renderMode: 'button',
                        elButtonAttrs: {
                            type: 'text'
                        },
                        handleClick: this.handleView
                    },
                    {
                        label: '供应商',
                        prop: 'warehouseSupplier',
                        show: true,
                        minWidth: 240
                    },
                    {
                        label: '服务站区仓负责人',
                        prop: 'warehouseMaster',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '面积',
                        prop: 'warehouseArea',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '当月流量',
                        prop: 'monthlyTraffic',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '计费方式',
                        prop: 'billingMethod',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '单价',
                        prop: 'unitPrice',
                        show: true,
                        minWidth: 200,
                        render(value, row) {
                            if (row.billingMethod === '面积') {
                                return textRender.unitRender(value, '元/㎡');
                            } else if (row.billingMethod === '流量') {
                                return textRender.unitRender(value, '元/台');
                            }
                            return value;
                        }
                    },
                    {
                        label: '支出（当月）',
                        prop: 'monthlyCost',
                        show: true,
                        minWidth: 200,
                        render(value) {
                            return textRender.unitRender(value, '元');
                        }
                    },
                    {
                        label: '在库资产',
                        prop: 'totalStock',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '新机',
                        prop: 'newStock',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '撤机',
                        prop: 'removedStock',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '干线入库在途',
                        prop: 'inboundTransit',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '调拨在途',
                        prop: 'allocationTransit',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '其它',
                        prop: 'otherStock',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '平均库龄',
                        prop: 'ageAverage',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '库龄>30天数量',
                        prop: 'ageMoreThanThirty',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '使用面积',
                        prop: 'assetOccupyArea',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '面积利用率',
                        prop: 'areaUtilization',
                        show: true,
                        minWidth: 160
                    }
                ],
                operations: [
                    {
                        name: '历史信息',
                        type: 'primary',
                        handleClick: this.handleHistoryReport
                    }
                ],
                operationColumnWidth: 100,
                hooks: {
                    queryParamsHook: this.queryParamsHook,
                    tableListHook: this.tableListHook
                }
            },
            countList: [
                { name: '区仓数量', value: '-' },
                { name: '在库资产', value: '-' },
                { name: '干线在途', value: '-' },
                { name: '调拨在途', value: '-' },
                { name: '区仓面积', value: '-' },
                { name: '面积利用率', value: '-' },
                { name: '平均库龄', value: '-' }
            ]
        };
    },
    beforeCreate() {
        if (!this.$store.getters.toggleView) {
            allParams.queryParams = cloneDeep(queryParams);
            allParams.pageParams = cloneDeep(pageParams);
        }
    },
    created() {
        // 非tag切换进入，重置页面参数至默认状态
        if (!this.$store.getters.toggleView) {
            this.$tools.setQueryParams.call(this);
        }
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.queryList();
        this.getDailySummary();
    },
    methods: {
        // 汇总数据查询
        async getDailySummary() {
            const params = {
                startDate: lastDay
            };
            const { getDailySummary } = this.$service.warehouse.board;
            try {
                const res = await getDailySummary(params);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '汇总数据异常');
                    return;
                }
                const {
                    ageAverage,
                    allocationTransit,
                    areaUtilization,
                    inboundTransit,
                    totalStock,
                    warehouseArea,
                    warehouseCount
                } = res.result;
                // 设置具体值
                const { unitRender } = textRender;
                this.$tools.setIndicator(this.countList, {
                    区仓数量: unitRender(warehouseCount, ''),
                    在库资产: unitRender(totalStock, '台'),
                    干线在途: unitRender(inboundTransit, '台'),
                    调拨在途: unitRender(allocationTransit, '台'),
                    区仓面积: unitRender(warehouseArea, '㎡'),
                    面积利用率: unitRender(areaUtilization, '%'),
                    平均库龄: unitRender(ageAverage, '天')
                });
            } catch (error) {
                console.error(error);
                this.$tools.message.err('汇总数据异常');
            }
        },
        // 查询条件处理
        queryParamsHook(params) {
            params.requestDate = [lastDay, lastDay];
        },
        // 列表数据处理
        tableListHook(list) {
            list.map((item) => {
                Object.assign(
                    item,
                    item.baseDomain || {},
                    item.costDomain || {},
                    item.businessDomain || {},
                    item.settleDomain || {}
                );
                return item;
            });
        },
        // 查看操作
        handleView({ warehouseCode }) {
            this.$router.push({
                path: '/app/assets/assets-management',
                query: { warehouseCode }
            });
        },
        // 历史信息
        handleHistoryReport({ warehouseCode }) {
            this.$router.push({
                path: '/app/statistics-report/warehouse-history-report',
                query: { warehouseCode }
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
