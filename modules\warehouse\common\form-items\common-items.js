const input = {
    name: '单行输入',
    component: 'SnbcFormInput',
    modelKey: 'input'
};
const number = {
    name: '数值输入',
    component: 'SnbcFormInputNumber',
    modelKey: 'number'
};
const textarea = {
    name: '多行输入',
    component: 'SnbcFormTextarea',
    modelKey: 'textarea'
};
const select = {
    name: '单选下拉',
    component: 'SnbcFormSelect',
    modelKey: 'select',
    elOptions: []
};
const region = {
    name: '省市区',
    component: 'SnbcFormRegion',
    modelKey: 'region',
    type: 'area'
};

const cityRange = {
    name: '省市',
    component: 'SnbcFormMultiCitySelect',
    modelKey: 'cityRange',
    type: 'city'
};

const date = {
    name: '日期',
    component: 'SnbcFormDatePicker',
    modelKey: 'date'
};
const dateRange = {
    name: '起止日期',
    component: 'SnbcFormDateRangePicker',
    modelKey: 'dateRange'
};
const file = {
    name: '文件上传',
    component: 'SnbcFormFileUpload',
    modelKey: 'fileList',
    elUploadAttrs: {
        action: ''
    }
};
const supplierSelect = {
    name: '供应商选择',
    component: 'SnbcFormSupplierSelect',
    modelKey: 'supplierSelect'
};

const carrierSupplierSelect = {
    name: '承运物流商',
    component: 'SnbcFormCarrierSupplierSelect',
    modelKey: 'supplierSelect'
};

const taskStateSelect = {
    name: '任务状态选择',
    component: 'SnbcFormTaskStateSelect',
    modelKey: 'taskStateSelect'
};

const warehouseNameRegion = {
    name: '区仓名称',
    component: 'SnbcFormRegionWarehouseSelect',
    modelKey: 'warehouseCode'
};

export default {
    input,
    number,
    textarea,
    select,
    region,
    date,
    dateRange,
    file,
    supplierSelect,
    taskStateSelect,
    carrierSupplierSelect,
    warehouseNameRegion,
    cityRange
};
