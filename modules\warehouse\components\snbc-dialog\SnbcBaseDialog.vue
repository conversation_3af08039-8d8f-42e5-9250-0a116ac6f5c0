<template>
    <el-dialog
        class="custom-dialog"
        :visible.sync="dialogVisible"
        v-bind="elDialogAttrs"
    >
        <slot name="dialog-body" />
        <span v-if="hasFooter" slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </span>
    </el-dialog>
</template>
<script>
export default {
    name: 'SnbcBaseDialog',
    props: {
        /**
         * SnbcBaseDialog组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    elDialogAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // 弹窗显示
            dialogVisible: false,
            // 弹窗默认属性
            defaultElDialogAttrs: {
                title: '信息弹窗',
                width: '800px'
            }
        };
    },
    computed: {
        // 实际弹窗属性
        elDialogAttrs() {
            return {
                ...this.defaultElDialogAttrs,
                ...(this.config.elDialogAttrs || {})
            };
        },
        // 弹窗底部按钮操作区
        hasFooter() {
            if (this.config.hasFooter === false) {
                return false;
            }
            return true;
        },
        // 取消操作时自动隐藏弹窗
        autoHideOnCancel() {
            if (this.config.autoHideOnCancel === false) {
                return false;
            }
            return true;
        }
    },
    methods: {
        // 打开弹窗
        openDialog() {
            this.dialogVisible = true;
        },
        // 隐藏弹窗
        hideDialog() {
            this.dialogVisible = false;
        },
        // 取消操作
        handleCancel() {
            if (this.autoHideOnCancel) {
                this.hideDialog();
            }
            this.$emit('cancel');
        },
        // 确定操作
        handleConfirm() {
            this.$emit('confirm');
        }
    }
};
</script>
<style lang="scss" scoped></style>
