{"_args": [["wtf-core-vue@1.2.3", "C:\\Users\\<USER>\\Desktop\\tranlate-demo\\si-bbpf-boss-console"]], "_from": "wtf-core-vue@^1.2.4", "_id": "wtf-core-vue@1.2.4", "_inBundle": false, "_integrity": "sha512-apaLekdeWSVxCUf4kYA1GyjoUeAXf+6Ei683/iIEyXjT51MuMFYVhD9J25hDuJHg4k0W9lN0h2bOuEyDrX9LTQ==", "_location": "/wtf-core-vue", "_phantomChildren": {"adler-32": "1.2.0", "cfb": "1.2.1", "commander": "2.17.1", "crc-32": "1.2.0", "exit-on-epipe": "1.0.1", "frac": "1.1.2"}, "_requested": {"type": "range", "registry": true, "raw": "wtf-core-vue@^1.2.4", "name": "wtf-core-vue", "escapedName": "wtf-core-vue", "rawSpec": "^1.2.4", "saveSpec": null, "fetchSpec": "^1.2.4"}, "_requiredBy": ["/"], "_resolved": "http://maven.xtjc.net/repository/npm-all/wtf-core-vue/-/wtf-core-vue-1.2.4.tgz", "_shasum": "11a5466cdfd9c844d50dd57028390fc2922ebde0", "_spec": "wtf-core-vue@^1.2.4", "_where": "C:\\Users\\<USER>\\Desktop\\git代码合并\\si-bbpf-boss-console", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mqhe2007/admincraft/issues"}, "bundleDependencies": false, "dependencies": {"@babel/polyfill": "^7.4.3", "axios": "^0.21.1", "clipboard": "2.0.4", "codemirror": "5.45.0", "core-js": "3.6.5", "crypto-js": "^4.0.0", "driver.js": "0.9.5", "dropzone": "5.5.1", "echarts": "4.2.1", "element-ui": "^2.13.2", "file-saver": "2.0.1", "font-awesome": "^4.7.0", "fuse.js": "3.4.4", "js-cookie": "2.2.0", "jsencrypt": "^3.1.0", "jsonlint": "1.6.3", "jszip": "3.2.1", "mini-css-extract-plugin": "^1.3.5", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "pinyin-pro": "3.11.0", "screenfull": "4.2.0", "script-loader": "0.7.2", "snbc.wtf.tui.editor": "1.0.0", "sortablejs": "1.8.4", "vue": "2.6.10", "vue-count-to": "^1.0.13", "vue-i18n": "7.3.2", "vue-meta": "1.5.8", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vue-template-compiler": "2.6.10", "vuedraggable": "2.20.0", "vuex": "3.1.0", "xlsx": "0.14.1"}, "deprecated": false, "description": "wtf-core-vue is a vue admin that supports modular distribution deployment and loading.", "devDependencies": {"@babel/core": "^7.3.4", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/preset-env": "^7.3.4", "@vue/cli-plugin-eslint": "^4.5.15", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "babel-loader": "^8.0.5", "clean-webpack-plugin": "^2.0.0", "cross-env": "^5.2.0", "css-loader": "^2.1.1", "cssnano": "^4.1.10", "eslint": "6.7.2", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "6.2.2", "file-loader": "^3.0.1", "html-webpack-plugin": "3.2.0", "postcss-loader": "^3.0.0", "postcss-preset-env": "^6.6.0", "prettier": "^2.4.1", "sass": "^1.26.2", "sass-loader": "^8.0.2", "svg-sprite-loader": "^4.1.3", "svgo": "^1.2.0", "vue-loader": "^15.7.0", "vue-style-loader": "^4.1.2", "webpack": "^4.29.6", "webpack-bundle-analyzer": "^4.4.0", "webpack-cli": "^3.2.3", "webpack-dev-server": "^3.2.1"}, "keywords": ["vue", "admin", "vue-admin", "vue-ui", "wtf-core-vue"], "license": "MIT", "main": "src/main.js", "name": "wtf-core-vue", "resolutions": {"webpack-dev-middleware": "3.6.0"}, "scripts": {"analyze": "cross-env NODE_ENV=production npm_config_report=true npm run build", "build": "cross-env NODE_ENV=production webpack --progress", "build:dev": "cross-env NODE_ENV=development webpack --progress", "dev": "cross-env NODE_ENV=development webpack-dev-server", "lint": "eslint --fix --ext .js,.vue src"}, "version": "1.2.4"}