<template>
    <el-form-item v-bind="elFormItemAttrs">
        <el-input v-model="config.modelObj[config.modelKey]" v-bind="elInputAttrs" @focus="handleClick" />
        <ServeClientsDialog @choose="handleChoose" ref="serveClientsDialogRef" />
    </el-form-item>
</template>
<script>
import ServeClientsDialog from 'warehouse/views/warehouse-management/settled-customer-management/components/ServeClientsDialog.vue';

export default {
    name: 'SnbcFormServeClientsSelect',
    components: {
        ServeClientsDialog
    },
    props: {
        /**
         * SnbcFormServeClientsSelect组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elFormItemAttrs: {},
                    elInputAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-input组件默认属性设置
            defaultElInputAttrs: {
                clearable: true,
                readonly: true
            }
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-input组件应用属性
        elInputAttrs() {
            return {
                ...this.defaultElInputAttrs,
                placeholder: `请选择服务客户`,
                ...(this.config.elInputAttrs || {})
            };
        }
    },
    methods: {
        handleClick() {
            this.$refs.serveClientsDialogRef.showDialog();
        },
        handleChoose(row) {
            this.config.modelObj.customerName = row.abbreviations;
        }
    }
};
</script>
