<template>
    <el-dialog
        class="custom-dialog query-label-line2"
        :visible.sync="dialogVisible"
        v-bind="elDialogAttrs"
        width="1200px"
    >
        <template>
            <snbc-card
                style="max-height: 500px; overflow-y: auto; display: block"
            >
                <template #card-body>
                    <snbc-table-list
                        :config="batchTableConfig"
                        :list="result"
                    />
                </template>
            </snbc-card>

            <span slot="footer" class="dialog-footer">
                <el-button @click="hideDialog">取 消</el-button>
                <el-button type="primary" @click="handleSave">确 定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script>
import SnbcTableList from 'warehouse/components/snbc-table/SnbcTableList.vue';
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import elAttrs from 'warehouse/common/el-attrs/index.js';

export default {
    name: 'BatchQuery',
    components: {
        SnbcTableList,
        SnbcCard
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 统一UI风格
            elAttrs,
            elDialogAttrs: elAttrs.elDialogAttrs,
            result: [],
            // 弹窗显示
            dialogVisible: false,

            // 缺少物料信息表格配置
            batchTableConfig: {
                elTableColumns: [
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '实际所在区仓',
                        prop: 'actualWarehouseName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '系统所在区仓',
                        prop: 'systemWarehouseName',
                        show: true,
                        minWidth: 100
                    },

                    {
                        label: '资产编码',
                        prop: 'assetCode',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '调拨名称',
                        prop: 'allocateName',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '出入库类型',
                        prop: 'inOutType',
                        show: true,
                        minWidth: 100
                    }
                ],
                operations: [
                    {
                        name: '删除',
                        type: 'danger',
                        width: 150,
                        handleClick: this.handleDelete
                    }
                ]
            }
        };
    },

    methods: {
        // 展示弹窗
        openDialog(result) {
            this.elDialogAttrs = {
                ...elAttrs.elDialogAttrs,
                title: '批量处理'
            };
            this.result = result;
            this.dialogVisible = true;
        },

        // 隐藏弹窗
        hideDialog() {
            this.dialogVisible = false;
        },
        async handleSave() {
            try {
                const { message, code } =
                    await this.$service.warehouse.inventoryManagement.handleMatchAsset(
                        this.result
                    );
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.hideDialog();
                this.$emit('fresh');
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        handleDelete(row) {
            this.result = this.result.filter((item) => {
                return item.assetCode !== row.assetCode;
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
