import FormItems from 'warehouse/common/form-items/index.js';

const { input, select, textarea, number, date, dateRange, warehouseSelect, carrierSupplierSelect, taskStateSelect } =
    FormItems;

const elInputNumberAttrs = {
    min: 0,
    controls: false,
    precision: 2,
    max: Math.pow(10, 6) - 0.01
};

export const logisticsFTLItems = (function () {
    const transportMode = {
        ...select,
        name: '运输方式',
        modelKey: 'transportMode',
        elOptions: [
            { label: '整车', value: '整车' },
            { label: '零担', value: '零担' }
        ]
    };

    const logisticsCode = {
        ...carrierSupplierSelect,
        name: '承运物流商',
        modelKey: 'logisticsCode',
        elSelectAttrs: {
            clearable: false
        }
    };

    const logisticsTel = {
        ...input,
        name: '联系电话',
        modelKey: 'logisticsTel'
    };

    const logisticsPerson = {
        ...input,
        name: '联系人',
        modelKey: 'logisticsPerson'
    };

    const mileage = {
        ...input,
        name: '里程（km）',
        modelKey: 'mileage',
        elInputAttrs: {
            disabled: true
        }
    };

    const carType = {
        ...select,
        name: '车型',
        modelKey: 'carType',
        elOptions: [
            { label: '4.2米', value: '4.2米' },
            { label: '6.8米', value: '6.8米' },
            { label: '9.6米', value: '9.6米' },
            { label: '13.5米', value: '13.5米' },
            { label: '17.5米', value: '17.5米' }
        ]
    };

    const trunkFee = {
        ...number,
        name: '调拨干线费（元）',
        modelKey: 'trunkFee',
        elInputNumberAttrs
    };

    const unitPrice = {
        ...input,
        name: '单价（元/km）',
        modelKey: 'unitPrice',
        elInputAttrs: {
            disabled: true
        }
    };

    const deliveryFee = {
        ...number,
        name: '提送费（元）',
        modelKey: 'deliveryFee',
        elInputNumberAttrs
    };

    const packFee = {
        ...number,
        name: '包装费（元）',
        modelKey: 'packFee',
        elInputNumberAttrs
    };

    const handingFee = {
        ...number,
        name: '装卸费（元）',
        modelKey: 'handingFee',
        elInputNumberAttrs
    };

    const otherFee = {
        ...number,
        name: '其他费用（元）',
        modelKey: 'otherFee',
        elInputNumberAttrs
    };

    const totalFee = {
        ...number,
        name: '总价（元）',
        modelKey: 'totalFee',
        elInputNumberAttrs: {
            disabled: true,
            controls: false,
            precision: 2
        }
    };

    const feeDesc = {
        ...textarea,
        name: '费用说明',
        modelKey: 'feeDesc'
    };

    return [
        transportMode,
        logisticsCode,
        logisticsTel,
        logisticsPerson,
        mileage,
        carType,
        trunkFee,
        unitPrice,
        deliveryFee,
        packFee,
        handingFee,
        otherFee,
        totalFee,
        feeDesc
    ];
})();

export const logisticsLTLItems = (function () {
    const transportMode = {
        ...select,
        name: '运输方式',
        modelKey: 'transportMode',
        elOptions: [
            { label: '整车', value: '整车' },
            { label: '零担', value: '零担' }
        ]
    };

    const logisticsCode = {
        ...carrierSupplierSelect,
        name: '承运物流商',
        modelKey: 'logisticsCode',
        elSelectAttrs: {
            clearable: false
        }
    };

    const logisticsTel = {
        ...input,
        name: '联系电话',
        modelKey: 'logisticsTel'
    };

    const logisticsPerson = {
        ...input,
        name: '联系人',
        modelKey: 'logisticsPerson'
    };

    const mileage = {
        ...input,
        name: '里程（km）',
        modelKey: 'mileage',
        elInputAttrs: {
            disabled: true
        }
    };

    const cubicNumber = {
        ...number,
        name: '立方数',
        modelKey: 'cubicNumber',
        elInputNumberAttrs
    };

    const trunkFee = {
        ...number,
        name: '调拨干线费（元）',
        modelKey: 'trunkFee',
        elInputNumberAttrs
    };

    const unitPrice = {
        ...input,
        name: '单价（元/m³）',
        modelKey: 'unitPrice',
        elInputAttrs: {
            disabled: true
        }
    };

    const deliveryFee = {
        ...number,
        name: '提送费（元）',
        modelKey: 'deliveryFee',
        elInputNumberAttrs
    };

    const packFee = {
        ...number,
        name: '包装费（元）',
        modelKey: 'packFee',
        elInputNumberAttrs
    };

    const handingFee = {
        ...number,
        name: '装卸费（元）',
        modelKey: 'handingFee',
        elInputNumberAttrs
    };

    const otherFee = {
        ...number,
        name: '其他费用（元）',
        modelKey: 'otherFee',
        elInputNumberAttrs
    };

    const totalFee = {
        ...number,
        name: '总价（元）',
        modelKey: 'totalFee',
        elInputNumberAttrs: {
            disabled: true,
            controls: false,
            precision: 2
        }
    };

    const feeDesc = {
        ...textarea,
        name: '费用说明',
        modelKey: 'feeDesc'
    };

    return [
        transportMode,
        logisticsCode,
        logisticsTel,
        logisticsPerson,
        mileage,
        cubicNumber,
        trunkFee,
        unitPrice,
        deliveryFee,
        packFee,
        handingFee,
        otherFee,
        totalFee,
        feeDesc
    ];
})();

export const settlementParty = {
    ...select,
    name: '结算方',
    modelKey: 'settlementParty',
    elOptions: [
        { label: '客户结算', value: '客户结算' },
        { label: '数码结算', value: '数码结算' },
        { label: '服务公司承担', value: '服务公司承担' }
    ]
};

export const settlementPrice = {
    ...number,
    name: '结算价格（元）',
    modelKey: 'settlementPrice'
};

// 没有字段
export const refuseItems = [
    {
        ...select,
        name: '拒绝类型',
        modelKey: 'rejectType',
        elOptions: [
            { label: '调拨数量不合理', value: '调拨数量不合理' },
            { label: '物流费用不合理', value: '物流费用不合理' }
        ]
    },
    {
        ...textarea,
        name: '拒绝原因',
        modelKey: 'rejectReason'
    }
];

export const queryObj = {
    allocateTaskName: {
        ...input,
        name: '调拨任务名称',
        modelKey: 'allocateTaskName'
    },
    allocateTaskCode: {
        ...input,
        name: '调拨任务编号',
        modelKey: 'allocateTaskCode'
    },
    sourceWarehouseCode: {
        ...warehouseSelect,
        name: '来源区仓',
        modelKey: 'sourceWarehouseCode',
        queryEnable: true
    },
    targetWarehouseCode: {
        ...warehouseSelect,
        name: '目标区仓',
        modelKey: 'targetWarehouseCode',
        queryEnable: true
    },
    taskSource: {
        ...select,
        name: '任务来源',
        modelKey: 'taskSource',
        elOptions: [
            { label: '客户需求', value: '客户需求' },
            { label: '关联公司需求', value: '关联公司需求' },
            { label: '自主发起', value: '自主发起' }
        ]
    },
    stockRequestCode: {
        ...input,
        name: '备货申请任务编号',
        modelKey: 'stockRequestCode'
    },
    taskState: {
        ...taskStateSelect,
        name: '任务状态',
        modelKey: 'taskState',
        elSelectAttrs: {
            'multiple': true,
            'collapse-tags': false
        }
    },
    expectedDateRange: {
        ...dateRange,
        name: '期望到货日期',
        modelKey: 'expectedDateRange',
        elDatePickerAttrs: {
            'value-format': 'yyyy-MM-dd'
        }
    },
    createDateRange: {
        ...dateRange,
        name: '创建时间',
        modelKey: 'createDateRange'
    }
};

export const queryItems = (function () {
    const {
        allocateTaskName,
        allocateTaskCode,
        sourceWarehouseCode,
        targetWarehouseCode,
        taskSource,
        stockRequestCode,
        expectedDateRange,
        taskState,
        createDateRange
    } = queryObj;

    return [
        allocateTaskName,
        allocateTaskCode,
        sourceWarehouseCode,
        targetWarehouseCode,
        taskSource,
        stockRequestCode,
        taskState,
        expectedDateRange,
        createDateRange
    ];
})();

export const addItems = (function () {
    const taskSource = {
        ...select,
        name: '任务来源',
        modelKey: 'taskSource',
        elOptions: [
            { label: '客户需求', value: '客户需求' },
            { label: '关联公司需求', value: '关联公司需求' },
            { label: '自主发起', value: '自主发起' }
        ]
    };

    const sourceWarehouseCode = {
        ...warehouseSelect,
        name: '来源区仓',
        modelKey: 'sourceWarehouseCode',
        ref: 'sourceWarehouseRef',
        queryEnable: true
    };

    const whetherSpecifyAsset = {
        ...select,
        name: '是否是指定资产',
        modelKey: 'whetherSpecifyAsset',
        elSelectAttrs: {
            disabled: true
        },
        elOptions: [
            { label: '否', value: 0 },
            { label: '是', value: 1 }
        ]
    };

    const targetWarehouseCode = {
        ...warehouseSelect,
        name: '目标区仓',
        modelKey: 'targetWarehouseCode',
        ref: 'targetWarehouseRef',
        queryEnable: true
    };

    const expectedDate = {
        ...date,
        name: '期望到货日期',
        modelKey: 'expectedDate',
        elDatePickerAttrs: {
            'pickerOptions': {
                disabledDate(time) {
                    return time.getTime() <= Date.now();
                }
            },
            'value-format': 'yyyy-MM-dd'
        }
    };

    const allocateTaskName = {
        ...input,
        name: '调拨任务名称',
        modelKey: 'allocateTaskName'
    };

    const remark = {
        ...textarea,
        name: '备注',
        modelKey: 'remark'
    };
    return [
        taskSource,
        sourceWarehouseCode,
        whetherSpecifyAsset,
        targetWarehouseCode,
        expectedDate,
        allocateTaskName,
        remark
    ];
})();

export default (function () {
    const stockRequestCode = {
        ...input,
        name: '备货申请任务编号',
        modelKey: 'stockRequestCode',
        elInputAttrs: {
            disabled: true
        }
    };

    const taskSource = {
        ...select,
        name: '任务来源',
        modelKey: 'taskSource',
        elInputAttrs: {
            disabled: true
        },
        elOptions: [
            { label: '客户需求', value: '客户需求' },
            { label: '关联公司需求', value: '关联公司需求' },
            { label: '自主发起', value: '自主发起' }
        ]
    };

    const sourceWarehouseCode = {
        ...warehouseSelect,
        name: '来源区仓',
        modelKey: 'sourceWarehouseCode',
        ref: 'sourceWarehouseRef',
        queryEnable: true
    };

    const whetherSpecifyAsset = {
        ...select,
        name: '是否是指定资产',
        modelKey: 'whetherSpecifyAsset',
        elSelectAttrs: {
            disabled: true
        },
        elOptions: [
            { label: '否', value: 0 },
            { label: '是', value: 1 }
        ]
    };

    const targetWarehouseCode = {
        ...warehouseSelect,
        name: '目标区仓',
        modelKey: 'targetWarehouseCode',
        elSelectAttrs: {
            disabled: true
        }
    };

    const expectedDate = {
        ...date,
        name: '期望到货日期',
        modelKey: 'expectedDate',
        elDatePickerAttrs: {
            'pickerOptions': {
                disabledDate(time) {
                    return time.getTime() <= Date.now();
                }
            },
            'value-format': 'yyyy-MM-dd'
        }
    };

    const allocateTaskName = {
        ...input,
        name: '调拨任务名称',
        modelKey: 'allocateTaskName'
    };

    const remark = {
        ...textarea,
        name: '备注',
        modelKey: 'remark'
    };
    return [
        stockRequestCode,
        sourceWarehouseCode,
        whetherSpecifyAsset,
        targetWarehouseCode,
        taskSource,
        expectedDate,
        allocateTaskName,
        remark
    ];
})();
