<template>
    <div>
        <el-dialog class="custom-dialog" :visible.sync="dialogVisible" v-bind="elDialogAttrs">
            <snbc-card title="备货基础信息">
                <template #card-body>
                    <snbc-descriptions :items="basicInfo" />
                </template>
            </snbc-card>

            <snbc-card title="备货申请明细" class="margin-top-10">
                <template #card-body>
                    <el-table
                        :data="taskDetails"
                        v-bind="elTableAttrs"
                        :expand-row-keys="expandRowKeys"
                        row-key="productName"
                    >
                        <el-table-column
                            v-for="column in taskTableConfig.applyDetailsColumns"
                            :label="column.label"
                            :key="column.prop"
                            :prop="column.prop"
                            :min-width="column.minWidth"
                        >
                            <template slot-scope="scope">
                                <!-- 单元格内容渲染 -->
                                <template v-if="!column.renderMode">{{ scope.row[column.prop] }}</template>
                                <!-- 数量显示 -->
                                <el-input
                                    :value="deliveryNum(scope.row)"
                                    disabled
                                    v-else-if="column.renderMode === 'inputNumber'"
                                ></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center" :width="340">
                            <template slot-scope="scope">
                                <el-button
                                    type="primary"
                                    size="mini"
                                    :disabled="scope.row.checkNumber === scope.row.splitNumber"
                                    @click="handleCheckNationalInventory(scope.row, scope.$index)"
                                    >公司库存:{{ scope.row.erpNumber }}</el-button
                                >
                                <el-button
                                    type="primary"
                                    size="mini"
                                    :disabled="scope.row.checkNumber === scope.row.splitNumber"
                                    @click="handleCheckAreaInventory(scope.row, scope.$index)"
                                    >各区仓库存:{{ scope.row.otherNumber }}</el-button
                                >
                                <el-button
                                    type="danger"
                                    size="mini"
                                    :disabled="scope.row.checkNumber === scope.row.splitNumber"
                                    @click="handleRemove(taskDetails, scope.$index)"
                                    >删除</el-button
                                >
                            </template>
                        </el-table-column>
                        <el-table-column type="expand">
                            <template slot-scope="props">
                                <el-table
                                    :data="props.row.sendOutGoodsList"
                                    v-bind="elTableAttrs"
                                    class="sendOutGoodsList"
                                >
                                    <el-table-column
                                        v-for="column in taskTableConfig.taskDetailsColumns"
                                        :label="column.label"
                                        :key="column.prop"
                                        :prop="column.prop"
                                        :min-width="column.minWidth"
                                    >
                                        <template slot-scope="scope">
                                            <!-- 单元格内容渲染 -->
                                            <template v-if="!column.renderMode">{{ scope.row[column.prop] }}</template>
                                            <!-- 数量显示 -->
                                            <el-input-number
                                                v-model="scope.row[column.prop]"
                                                :min="0"
                                                :max="controlSendNumber(scope.row, props.row)"
                                                v-else-if="column.renderMode === 'inputNumber'"
                                                controls-position="right"
                                            ></el-input-number>
                                        </template>
                                    </el-table-column>
                                    <el-table-column align="center" width="100">
                                        <template slot-scope="scope">
                                            <el-button
                                                type="danger"
                                                @click="handleRemove(props.row.sendOutGoodsList, scope.$index)"
                                                >删除</el-button
                                            >
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </snbc-card>
            <span slot="footer" class="dialog-footer">
                <el-button type="danger" @click="handleClose">关闭</el-button>
                <el-button type="primary" @click="handleCreate" :disabled="btnStatus">下一步</el-button>
            </span>
        </el-dialog>
        <national-inventory ref="nationalInventoryRef" @select-warehouse="handleSelectWarehouse"></national-inventory>
        <area-inventory ref="areaInventoryRef" @select-warehouse="handleSelectWarehouse"></area-inventory>
    </div>
</template>

<script>
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import SnbcDescriptions from 'warehouse/components/snbc-descriptions/SnbcDescriptions.vue';
import NationalInventory from './NationalInventory.vue';
import AreaInventory from './AreaInventory.vue';
import ElAttrs from 'warehouse/common/el-attrs/index.js';

const { elDialogAttrs, elTableAttrs, elTableColumnAttrs } = ElAttrs;
export default {
    name: 'StockUpApplicationReviewOfApplicationDetails',
    components: {
        SnbcCard,
        SnbcDescriptions,
        NationalInventory,
        AreaInventory
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            elTableAttrs,
            elTableColumnAttrs,
            elDialogAttrs: {
                ...elDialogAttrs,
                title: '分解备货申请任务',
                width: '1200px'
            },
            dialogVisible: false,
            // 基础信息
            basicInfo: [
                { label: '备货申请任务编号', prop: 'stockRequestCode', value: '' },
                { label: '备货申请任务名称', prop: 'stockRequestName', value: '' },
                { label: '申请时间', prop: 'createTime', value: '' },
                { label: '申请人', prop: 'createUserName', value: '' },
                { label: '目标区仓', prop: 'directionWarehouseName', value: '' },
                { label: '备注', prop: 'remark', value: '' }
            ],
            // 任务详情列表
            taskDetails: [],
            // 选择全国库存时数据index值
            detailsIndex: 0,
            // 传参需基本信息集合
            info: {
                directionWarehouseName: '',
                directionWarehouseCode: '',
                requestFrom: '',
                stockRequestCode: '',
                stockRequestName: '',
                remark: ''
            },
            taskTableConfig: {
                applyDetailsColumns: [
                    {
                        label: '客户名称',
                        prop: 'customerName',
                        show: true,
                        minWidth: 100
                    },
                    { label: '产品名称', prop: 'productName', minWidth: 160 },
                    { label: '核定数量', prop: 'checkNumber', minWidth: 100 },
                    {
                        label: '已分解数量',
                        prop: 'splitNumber',
                        minWidth: 100
                    },
                    {
                        label: '计划发货数量',
                        prop: 'sendNumber',
                        minWidth: 120,
                        renderMode: 'inputNumber'
                    },
                    {
                        label: '计划到货',
                        prop: 'expectedArrivalDate',
                        minWidth: 120
                    }
                ],
                taskDetailsColumns: [
                    {
                        label: '来源区仓',
                        prop: 'fromWarehouseName',
                        minWidth: 120
                    },
                    {
                        label: '物料编码',
                        prop: 'materialCode',
                        minWidth: 120
                    },
                    {
                        label: '发货数量',
                        prop: 'sendNumber',
                        minWidth: 120,
                        renderMode: 'inputNumber'
                    },
                    {
                        label: '订单类型',
                        prop: 'type',
                        minWidth: 120
                    },
                    {
                        label: '可用数量',
                        prop: 'warehouseNameAndNumber',
                        minWidth: 120
                    }
                ]
            },
            // 展开列控制
            expandRowKeys: []
        };
    },
    computed: {
        // 控制来源仓数量
        controlSendNumber() {
            return (row, data) => {
                // 获取其他来源仓发货数量
                let number = 0;
                data.sendOutGoodsList.forEach((item) => {
                    // 判断当前来源区仓
                    if (item.index !== row.index) {
                        number += item.sendNumber || 0;
                    }
                });
                // 获取可发货最大数量，该产品的核对数量-已分解数量
                const maxSendNumber = data.checkNumber - data.splitNumber - number;
                // 公司库存销售订单不限制在库数量
                if (row.orderType === 'sale') {
                    return maxSendNumber;
                }
                // 在库数量
                const { occupiedNumber } = row;
                // 返回当前来源仓可发货最大数量
                return maxSendNumber < occupiedNumber ? maxSendNumber : occupiedNumber;
            };
        },
        // 获取产品发货总数量
        deliveryNum() {
            return (data) => {
                const list = data.sendOutGoodsList || [];
                let sendNumber = 0;
                list.forEach((item) => {
                    sendNumber += item.sendNumber || 0;
                });
                return sendNumber || 0;
            };
        },
        // 控制下一步按钮显示
        btnStatus() {
            return !this.taskDetails.length;
        }
    },
    methods: {
        // 查询详情
        async showDetailsDialog(data) {
            const params = {
                stockRequestCode: data
            };
            this.stockRequestCode = data;
            try {
                const res = await this.$service.warehouse.stockUp.getDetails(params);
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                // 获取备货基本信息
                const basicInfo = result.stockRequest;
                this.basicInfo.map((item) => {
                    item.value = basicInfo[item.prop];
                    return item;
                });
                // 根据详情数据获取传参字段值
                Object.keys(this.info).forEach((key) => {
                    this.info[key] = result.stockRequest[key];
                });
                // 详情信息
                const list = result.stockRequestDetailList;
                // 添加序号
                list.map((item, index) => {
                    item.index = index + 1;
                    return item;
                });
                this.taskDetails = list;

                this.dialogVisible = true;
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        openDialog() {
            this.dialogVisible = true;
        },
        // 删除任务明细
        handleRemove(data, index) {
            data.splice(index, 1);
        },
        // 下一步操作——备货申请基本信息生成
        handleCreate() {
            // 获取分解任务list参数
            const splitDetailList = this.taskDetails.map((item) => {
                return {
                    sendNumber: this.deliveryNum(item),
                    sendOutGoodsList: (item.sendOutGoodsList || []).filter((data) => data.sendNumber > 0)
                };
            });
            // 判断是否进行分解
            const splitFlag = splitDetailList.every((item) => item.sendNumber === 0);
            // 若无分解任务则不允许进行下一步
            if (splitFlag) {
                this.$tools.message.err('请先分解任务');
                return;
            }
            const params = {
                splitDetailList,
                ...this.info
            };
            this.$router.push({
                name: 'CompanyDeliveryTaskCreate',
                params
            });
        },
        // 关闭操作
        handleClose() {
            this.dialogVisible = false;
        },

        // 全国库存
        handleCheckNationalInventory(row, index) {
            // 当前选择明细序号
            this.detailsIndex = index;
            this.$refs.nationalInventoryRef.openDialog(row, this.info.directionWarehouseCode);
        },

        // 各区仓库存
        handleCheckAreaInventory(row, index) {
            // 当前选择明细序号
            this.detailsIndex = index;
            this.$refs.areaInventoryRef.openDialog(row, this.info.directionWarehouseCode);
        },

        // 选择区仓
        handleSelectWarehouse(selections) {
            // 添加当前选择来源仓
            const sendList = (this.taskDetails[this.detailsIndex].sendOutGoodsList || [])
                .concat(selections)
                .map((item, index) => {
                    item.index = index;
                    return item;
                });
            this.$set(this.taskDetails[this.detailsIndex], 'sendOutGoodsList', sendList);
            this.expandRowKeys = [...new Set([...this.expandRowKeys, this.taskDetails[this.detailsIndex].productName])];
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .el-input-number--medium {
    width: auto;
}
.sendOutGoodsList {
    width: 72%;
    margin: 0 auto;
}
::v-deep .el-descriptions-item__content {
    max-width: 500px;
}
</style>
