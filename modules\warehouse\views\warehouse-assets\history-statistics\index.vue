<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig" />
        </div>
    </div>
</template>

<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import FormItems from 'warehouse/common/form-items/index.js';

const { warehouseMultiSelect, dateRange } = FormItems;

export default {
    name: 'WarehouseHistoryStatistics',
    components: {
        SnbcBaseTable
    },
    mixins: [functions],
    data() {
        const {
            getHistoryStatistics: listApi
        } = this.$service.warehouse.assets;
        return {
            listApi,
            tableConfig: {
                queryParams: {
                    warehouseSelect: [],
                    historyDate: []
                },
                queryConfig: {
                    items: [
                        warehouseMultiSelect,
                        { ...dateRange, modelKey: 'historyDate' }
                    ]
                },
                queryApi: listApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '日期',
                        prop: 'createTime',
                        show: true,
                        minWidth: 120,
                        renderMode: 'button',
                        elButtonAttrs: {
                            type: 'text'
                        },
                        handleClick: this.handleClickCell
                    }
                ]
            }
        };
    },
    computed: {},
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.handleQuery();
    },
    methods: {
        // 资产信息查看
        handleClickCell(row) {
            this.$router.push({ path: '/app/assets/assets-management', query: { id: row.id }});
        }
    }
};
</script>

<style lang="scss" scoped>

</style>
