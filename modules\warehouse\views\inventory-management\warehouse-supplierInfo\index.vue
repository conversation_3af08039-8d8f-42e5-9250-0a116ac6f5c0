<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig" />
            <snbc-base-form-dialog
                ref="dialogRef"
                :config="dialogConfig"
                @submit="handleSubmit"
            />
        </div>
    </div>
</template>

<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import FormItems from 'warehouse/common/form-items/basicInfo-items';
import FormRules from 'warehouse/common/form-rules';
import SnbcBaseFormDialog from 'warehouse/components/snbc-dialog/SnbcBaseFormDialog.vue';

const {
    supplierName,
    supplierContactsName,
    supplierContactsPhone,
    isLogistics,
    isInstaller,
    isWarehouse,
    supplierCode
} = FormItems;

// 查询参数
const queryParams = {
    supplierName: '',
    isWarehouse: '',
    isInstaller: '',
    isLogistics: ''
};
const { selectRequired, inputRequired, maxLength } = FormRules;
// 查询区域配置项
const queryConfigItems = [supplierName, isLogistics, isInstaller, isWarehouse];
// 弹窗配置
const dialogConfigItems = [
    supplierName,
    supplierCode,
    supplierContactsName,
    supplierContactsPhone,
    isLogistics,
    isInstaller,
    isWarehouse
];
// 弹窗校验规则
const rules = {
    supplierName: [inputRequired('请输入供应商名称'), maxLength(128)],
    supplierCode: [inputRequired('请输入供应商编码'), maxLength(32)],
    supplierContactsName: [
        inputRequired('请输入供应商联系人姓名'),
        maxLength(45)
    ],
    supplierContactsPhone: [
        inputRequired('请输入供应商联系人电话'),
        maxLength(45)
    ],
    isLogistics: [selectRequired('请选择是否是物流供应商')],
    isInstaller: [selectRequired('请选择是否是安装供应商')],
    newPassword: [selectRequired('请选择是否是区仓供应商')]
};

export default {
    name: 'SupplierManage',
    components: {
        SnbcBaseTable,
        SnbcBaseFormDialog
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        const {
            getSupplierInfoList: listApi,
            addSupplierInfo: addApi,
            updateSupplierInfo: editApi
        } = this.$service.warehouse.basicInfoManagement;
        const checkAge = (rule, value, callback) => {
            if (!Number.isInteger(Number(value))) {
                callback(new Error('请输入数字值'));
            } else {
                callback();
            }
        };
        rules.supplierContactsPhone.push({
            validator: checkAge,
            trigger: 'blur'
        });
        return {
            listApi,
            addApi,
            editApi,
            tableConfig: {
                queryParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: listApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '供应商名称',
                        prop: 'supplierName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '供应商编码',
                        prop: 'supplierCode',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '联系人姓名',
                        prop: 'supplierContactsName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '联系人电话',
                        prop: 'supplierContactsPhone',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '是否为物流供应商',
                        prop: 'isLogisticsName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '是否安装供应商',
                        prop: 'isInstallerName',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '是否是区仓仓库供应商',
                        prop: 'isWarehouseName',
                        show: true,
                        minWidth: 100
                    }
                ],
                operations: [
                    {
                        name: '修改',
                        type: 'warning',
                        handleClick: this.handleEdit
                    },
                    {
                        name: '删除',
                        type: 'danger',
                        handleClick: this.handleDelete
                    }
                ],
                headerButtons: [
                    {
                        name: '新增',
                        type: 'primary',
                        handleClick: this.handleAdd
                    }
                ],
                hooks: {
                    tableListHook: this.tableListHook
                }
            },
            // 弹窗配置
            dialogConfig: {
                rules,
                'label-width': '180px',
                'items': dialogConfigItems
            }
        };
    },
    computed: {},
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.handleQuery();
    },
    methods: {
        tableListHook(list) {
            list.map((item) => {
                item.isWarehouseName = {
                    1: '是',
                    0: '否'
                }[item.isWarehouse];
                item.isInstallerName = {
                    1: '是',
                    0: '否'
                }[item.isInstaller];
                item.isLogisticsName = {
                    1: '是',
                    0: '否'
                }[item.isLogistics];
                return item;
            });
        },
        // 删除
        async handleDelete(row) {
            await this.$tools.confirm('确认删除？');

            try {
                const res =
                    await this.$service.warehouse.basicInfoManagement.deleteSupplier(
                        row.id
                    );
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.$refs.dialogRef.hideDialog();
                await this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 编辑操作
        async handleEdit(row) {
            const data =
                await this.$service.warehouse.basicInfoManagement.getSupplierDetailInfo(
                    row.id
                );
            if (data.code !== '000000') {
                this.$tools.message.err(data.message || '系统异常');
                return;
            }
            this.$refs.dialogRef.baseEditDialog(data.result, '修改承运商');
        },
        // 新增
        handleAdd() {
            const data = {};
            this.$refs.dialogRef.baseAddDialog(data, '新增承运商');
        },

        // 编辑或提交
        async handleSubmit({ mode, form }) {
            await this.$tools.confirm('确认提交？');
            const params = form;
            let submitApi = this.addApi;
            if (mode === 'edit') {
                submitApi = this.editApi;
            }
            try {
                const res = await submitApi(params);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.$refs.dialogRef.hideDialog();
                await this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        }
    }
};
</script>

<style lang="scss" scoped></style>
