{"_args": [["ssf@0.10.3", "C:\\Users\\<USER>\\Desktop\\si-warehouse-page"]], "_from": "ssf@0.10.3", "_id": "ssf@0.10.3", "_inBundle": false, "_integrity": "sha512-pRuUdW0WwyB2doSqqjWyzwCD6PkfxpHAHdZp39K3dp/Hq7f+xfMwNAWIi16DyrRg4gg9c/RvLYkJTSawTPTm1w==", "_location": "/wtf-core-vue/ssf", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ssf@0.10.3", "name": "ssf", "escapedName": "ssf", "rawSpec": "0.10.3", "saveSpec": null, "fetchSpec": "0.10.3"}, "_requiredBy": ["/wtf-core-vue/xlsx"], "_resolved": "http://maven.xtjc.net/repository/npm-all/ssf/-/ssf-0.10.3.tgz", "_spec": "0.10.3", "_where": "C:\\Users\\<USER>\\Desktop\\si-warehouse-page", "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "author": {"name": "sheetjs"}, "bin": {"ssf": "bin/ssf.njs"}, "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "config": {"blanket": {"pattern": "ssf.js"}}, "dependencies": {"frac": "~1.1.2"}, "description": "Format data using ECMA-376 spreadsheet Format Codes", "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "blanket": "~1.2.3", "dtslint": "^0.1.2", "mocha": "~2.5.3", "typescript": "2.2.0"}, "engines": {"node": ">=0.8"}, "homepage": "http://sheetjs.com/", "keywords": ["format", "sprintf", "spreadsheet"], "license": "Apache-2.0", "main": "./ssf", "name": "ssf", "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"build": "make", "dtslint": "dtslint types", "lint": "make fullint", "test": "make test"}, "types": "types", "version": "0.10.3"}