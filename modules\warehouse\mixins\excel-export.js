/* eslint-disable */
import { saveAs } from 'file-saver';
/**
 * 用于Excel文件导出
 */
export default {
    methods: {
        /**
         * 下载Excel文件
         */
        downloadFile(data, fileName) {
            if (!data) {
                return;
            }
            const blob = new Blob([data]);
            // 对于<a>标签，只有 Firefox 和 Chrome（内核） 支持 download 属性
            // IE10以上支持blob但是依然不支持download
            if ('download' in document.createElement('a')) {
                // 支持a标签download的浏览器
                const link = document.createElement('a');// 创建a标签
                link.download = fileName;// a标签添加属性
                link.style.display = 'none';
                link.href = URL.createObjectURL(blob);
                document.body.appendChild(link);
                link.click();// 执行下载
                URL.revokeObjectURL(link.href); // 释放url
                document.body.removeChild(link);// 释放标签
            } else {
                navigator.msSaveBlob(blob, fileName);
            }
        },
        /**
         * 根据响应内容返回消息
         * @param {blob/json} res 接口blob响应内容
         * @param {object} opts {
         *  success:'json数据成功提示消息|function(成功消息回调)',
         *  error:'json数据失败提示消息|function(失败消息回调)',
         *  type:'保存文件类型'
         *
         */
        download(res, fileName, opts) {
            // 默认空对象，避免后续参数使用出现异常
            opts = opts || {};
            // 如果没有传入文件名称，则尝试获取服务器返回文件名
            if (!fileName) {
                fileName = res.headers.filename;
            }
            // 解析响应blob流，如果是json格式，则提示消息。否则是下载的文件
            if (res && res.type && res.type.toLowerCase().includes('json')) {
                // 这里是用于读取响应内容
                const reader = new FileReader();
                // 存储当前this，用于在onload函数中使用
                const _slef = this;
                // 异步读取响应内容结果
                reader.onload = function() {
                    const response = JSON.parse(reader.result);
                    // 根据响应json结果，判断并提示信息
                    if (response.head.code === '000000') {
                        if (typeof (opts.success) === 'function') {
                            opts.success.apply(_slef, [response]);
                        } else {
                            _slef.$message.success(opts.success || response.head.message);
                        }
                    } else if (typeof (opts.error) === 'function') {
                            opts.error.apply(_slef, [response]);
                        } else {
                            const {code} = response.head;
                            let msg = '';
                            if (code === '991905') {
                                _slef.$message.error(opts.error || _slef.$t('systemManagement.logger.message.export1') + response.head.message + _slef.$t('systemManagement.logger.message.export2'));
                            } else {
                                msg = `systemManagement.bgReturnError[${code}]`;
                                _slef.$message.error(opts.error || _slef.$t(msg));
                            }
                        }
                };

                // 调用响应方法，开始读取响应的blob内容
                reader.readAsText(res, 'utf-8');
            } else {
                // 这里是blob文件流，调用方法进行文件下载
                saveAs(new Blob([res], { type: opts.type || res.type }), fileName);
            }
        }
    }
};
