const path = require('path');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const VueLoaderPlugin = require('vue-loader/lib/plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CleanWebpackPlugin = require('clean-webpack-plugin');
const morePlugins = [];

if (process.env.NODE_ENV === 'development') {
    morePlugins.push(
        new HtmlWebpackPlugin({
            title: '加载中……',
            template: 'dev/index.html'
        })
    );
}
module.exports = {
    entry: process.env.NODE_ENV === 'production' ? './src/main.js' : './dev/index.js',
    output: {
        filename: '[name].js',
        path: path.resolve(__dirname, 'dist'),
        publicPath: process.env.NODE_ENV === 'production' ? './' : '',
        library: 'WtfCoreVue',
        libraryTarget: 'umd',
        umdNamedDefine: true
    },
    mode: process.env.NODE_ENV,
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './src'),
            '@root': path.resolve(__dirname)
        },
        mainFiles: ['index'],
        extensions: ['.js', '.vue']
    },
    devServer: {
        contentBase: './dist'
    },
    module: {
        rules: [{
            test: /.vue$/,
            loader: 'vue-loader'
        },
        {
            test: /\.js$/,
            exclude: /node_modules/,
            loader: 'babel-loader'
        },
        {
            test: /\.css$/,
            use: ['vue-style-loader', 'css-loader']
        },
        {
            test: /\.scss$/,
            use: [{
                loader: MiniCssExtractPlugin.loader
            },
            {
                // Interprets CSS
                loader: 'css-loader',
                options: {
                    importLoaders: 2
                }
            },
            {
                loader: 'sass-loader' // 将 Sass 编译成 CSS
            }
            ]
        },
        {
            test: /\.svg$/,
            loader: 'svg-sprite-loader',
            include: [path.resolve(__dirname, 'src/icons')],
            options: {
                symbolId: 'icon-[name]'
            }
        },
        {
            test: /\.(jpg|jpeg|png|gif)$/,
            loader: 'file-loader',
            options: {
                name: 'fonts/[name].[ext]'
            }
        },
        {
            test: /\.(ttf|eot|woff|woff2)$/,
            loader: 'file-loader',
            options: {
                name: 'fonts/[name].[ext]'
            }
        }
        ]
    },
    optimization: {
        // runtimeChunk: 'single',
        splitChunks: {
            chunks: 'all',
            maxInitialRequests: Infinity,
            minSize: 0,
            cacheGroups: {
                libs: {
                    name: 'chunk-libs',
                    test: /[\\/]node_modules[\\/]/,
                    priority: 35,
                    chunks: 'initial' // only package third parties that are initially dependent
                },
                echarts: {
                    name: 'chunk-echarts',
                    test: /[\\/]node_modules[\\/]_?echarts(.*)/,
                    priority: 40
                },
                highlight: {
                    name: 'chunk-highlight',
                    test: /[\\/]node_modules[\\/]_?highlight(.*)/,
                    priority: 40
                },
                xlsx: {
                    name: 'chunk-xlsx',
                    test: /[\\/]node_modules[\\/]_?xlsx(.*)/,
                    priority: 40
                },
                fontawesome: {
                    name: 'chunk-font-awesome',
                    test: /[\\/]node_modules[\\/]_?font\-awesome(.*)/,
                    priority: 40
                }
            // elementUI: {
            //   name: 'chunk-elementUI', // split elementUI into a single package
            //   priority: 45, // the weight needs to be larger than libs and app or it will be packaged into libs or app
            //   test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
            // },
            // components: {
            //   name: 'chunk-components',
            //   // test: resolve('src/components'), // can customize your rules
            //   test: (module) => {
            //     return /[\\/]src[\\/]components[\\/]/.test(module.context)
            //   },
            //   // test: /.*\/(src\/components\/index)\.js/,
            //   priority: 50
            // }
            }
        }
    },
    plugins: [new BundleAnalyzerPlugin(), new CleanWebpackPlugin(), new VueLoaderPlugin(), new MiniCssExtractPlugin(), ...morePlugins]
};
