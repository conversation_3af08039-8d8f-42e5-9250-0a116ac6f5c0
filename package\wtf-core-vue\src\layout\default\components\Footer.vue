<template>
	<div class="footer-content">
		<div>
			&copy; 2018 Copyright.
		</div>
		<div class="footer-back" @click="toTop">
			<span>{{$t('common.returnTop')}}</span>
			<i class="fa fa-long-arrow-up"></i>
		</div>
		</div>
</template>
<script>
	export default {
		name: 'fixedFooter',
		methods:{
			toTop() {
				document.getElementsByClassName('container')[0].scrollTop = 0;				
			},
		}
	}
</script>
<style lang="scss" scoped>
@import "../../../styles/mixin.scss";
@import "../../../styles/variables.scss";
	.footer-content{
		width:100%;
		height: 100%;
		width:100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-sizing: border-box;
		padding:15px;
		background-color: #f5f6fa;
		border-top: 1px solid #dee5e7;
		.footer-back{
			&:hover{
				cursor: pointer;
				color:#09c;
			}
			span{
				margin-right:5px;
			}
			
		}
	}
</style>
