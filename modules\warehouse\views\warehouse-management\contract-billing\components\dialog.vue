<template>
    <snbc-base-dialog ref="snbcBaseDialogRef" :config="dialogConfig" @confirm="handleSubmit">
        <template slot="dialog-body">
            <snbc-form ref="snbcFormRef" :form="form" :config="formConfig">
                <template slot="form-body">
                    <snbc-form-item v-for="(item, index) in formItems" :key="index" :config="item" />
                </template>
            </snbc-form>
        </template>
    </snbc-base-dialog>
</template>
<script>
import SnbcBaseDialog from 'warehouse/components/snbc-dialog/SnbcBaseDialog.vue';
import SnbcForm from 'warehouse/components/snbc-form/SnbcForm.vue';
import SnbcFormItem from 'warehouse/components/snbc-form/SnbcFormItem.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import { inputRequired, selectRequired, numberRequired } from 'warehouse/common/form-rules/index.js';

const { select, input, number, warehouseSelect, customerSelect, date, productSelect } = FormItems;

// 弹窗校验规则
const rules = {
    warehouseCode: [selectRequired('区仓名称')],
    customerCode: [selectRequired('客户名称')],
    feeType: [selectRequired('计费方式')],
    assertDate: [inputRequired('生效时间')],
    freeDay: [inputRequired('免费天数')],
    feeEach: [inputRequired('计费单价'), numberRequired('计费单价')],
    productId: [selectRequired('产品名称')]
};

// 编辑弹窗配置
const editDialogConfigItems = [
    warehouseSelect,
    {
        ...select,
        name: '计费方式',
        modelKey: 'feeType',
        elOptions: [
            { label: '面积', value: '面积' },
            { label: '流量', value: '流量' },
            { label: '不结算', value: '不结算' }
        ]
    },
    customerSelect,
    productSelect,
    {
        ...number,
        name: '计费单价',
        modelKey: 'feeEach',
        elInputNumberAttrs: {
            precision: 2,
            min: 0,
            max: Math.pow(10, 6) - 0.01
        }
    },
    {
        ...input,
        name: '收费单位',
        modelKey: 'feeRule',
        elInputAttrs: {
            disabled: true
        }
    },
    {
        ...number,
        name: '免费天数',
        modelKey: 'freeDay',
        elInputNumberAttrs: {
            precision: 0,
            min: 0,
            max: Math.pow(10, 6) - 1
        }
    },
    {
        ...date,
        name: '生效时间',
        modelKey: 'assertDate',
        elDatePickerAttrs: {
            'type': 'month',
            'value-format': 'yyyy-MM',
            'pickerOptions': {
                disabledDate(time) {
                    return time.getTime() < Date.now();
                }
            }
        }
    }
];

const initialForm = {
    id: '',
    warehouseCode: '',
    customerCode: '',
    productId: '',
    feeEach: 0,
    feeRule: '',
    freeDay: 0,
    feeType: '',
    assertDate: ''
};

export default {
    name: 'ComtractBillingDialog',
    components: {
        SnbcBaseDialog,
        SnbcForm,
        SnbcFormItem
    },
    data() {
        return {
            // 表单对象
            form: {
                ...initialForm
            },
            // add or edit
            mode: '',
            // 弹窗属性
            elDialogAttrs: {},
            config: {
                items: [],
                rules
            },
            attrs: {}
        };
    },
    computed: {
        // 表单项数组,
        formItems() {
            return (this.config.items || [])
                .filter((item) => {
                    // 不展示客户和产品
                    if (this.form.feeType === '面积') {
                        return !['productId', 'customerCode'].includes(item.modelKey);
                    }
                    // 不展示客户、产品、收费单位、免费天数
                    if (this.form.feeType === '不结算') {
                        return !['productId', 'customerCode', 'feeRule'].includes(item.modelKey);
                    }
                    return true;
                })
                .map((item) => {
                    // 扩展表单项的配置
                    return this.expandSnbcFormCommon(item);
                });
        },
        // 表单配置
        formConfig() {
            return {
                elFormAttrs: {
                    'label-width': this.config['label-width'] || '120px',
                    'rules': {
                        ...this.config.rules,
                        feeEach:
                            this.form.feeType === '不结算'
                                ? [inputRequired('计费单价')]
                                : [inputRequired('计费单价'), numberRequired('计费单价')]
                    }
                }
            };
        },
        // 弹窗配置
        dialogConfig() {
            return {
                elDialogAttrs: this.elDialogAttrs
            };
        }
    },
    watch: {
        'form.feeType': {
            handler(newVal, oldVal) {
                const obj = {
                    '面积': '平/月/元',
                    '流量': '台/天/元',
                    '': ''
                };
                if (newVal === '面积') {
                    this.form.customerCode = '';
                    this.form.productId = '';
                }
                this.form.feeRule = obj[newVal];
            },
            immediate: true
        }
    },
    methods: {
        // 基本新增弹窗
        baseAddDialog(data, title) {
            Object.assign(this.form, initialForm);
            this.config.items = [...editDialogConfigItems];
            this.openDialog('add', data, { title });
        },
        // 基本编辑弹窗
        baseEditDialog(data, title) {
            this.config.items = [...editDialogConfigItems];
            this.openDialog('edit', data, { title });
        },
        // 弹窗打开
        openDialog(mode, form, elDialogAttrs) {
            this.attrs =
                form.assertDate && new Date(form.assertDate) < Date.now()
                    ? {
                          elInputAttrs: { disabled: true },
                          elSelectAttrs: { disabled: true },
                          elInputNumberAttrs: { disabled: true, controls: false },
                          elDatePickerAttrs: { disabled: true, type: 'month' }
                      }
                    : {};
            this.mode = mode;
            Object.keys(this.form).forEach((key) => {
                this.form[key] = form[key] ?? '';
            });
            this.elDialogAttrs = elDialogAttrs || {};
            this.$nextTick(() => {
                // 重置校验及提示
                const { snbcFormRef } = this.$refs;
                if (snbcFormRef) {
                    const formRef = snbcFormRef.getFormRef();
                    formRef.clearValidate();
                }
                this.$nextTick(() => {
                    this.$refs.snbcBaseDialogRef.openDialog();
                });
            });
        },
        hideDialog() {
            this.$refs.snbcBaseDialogRef.hideDialog();
        },
        // 提交表单操作
        async handleSubmit() {
            await this.$tools.validateForm(this.$refs.snbcFormRef.getFormRef());
            this.$emit('submit', {
                mode: this.mode,
                form: this.form
            });
        },
        // 扩展组件属性
        expandSnbcFormCommon(item) {
            const attrs = item.modelKey === 'feeEach' ? {} : { ...this.attrs };
            if (this.form.feeType === '不结算' && item.modelKey === 'feeEach') {
                attrs.elInputNumberAttrs = {
                    ...attrs.elInputNumberAttrs,
                    disabled: true,
                    controls: false
                };
            }
            return {
                modelObj: this.form,
                elFormItemAttrs: {
                    label: item.name,
                    ...(item.elFormItemAttrs || {})
                },
                ...item,
                ...attrs
            };
        }
    }
};
</script>
<style lang="scss" scoped></style>
