<template>
    <el-form-item v-bind="elFormItemAttrs">
        <el-select v-model="config.modelObj[config.modelKey]" v-bind="elSelectAttrs" @change="handleChange">
            <el-option v-for="(option, index) in elOptions" :key="index" :label="option.label" :value="option.value" />
        </el-select>
    </el-form-item>
</template>
<script>
let tabSelected = '';
export default {
    name: 'SnbcFormTaskStateSelect',
    props: {
        /**
         * SnbcFormSelect组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elFormItemAttrs: {},
                    elSelectAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-select组件默认属性设置
            defaultElSelectAttrs: {
                filterable: true,
                clearable: true
            },
            // 所有数据
            allElOptions: [],
            stateMap: {
                准备中: [
                    'draft',
                    'rejected_pending_issue',
                    'pending_sourcing',
                    'rejected_pending_sourcing',
                    'pending_approval'
                ],
                调拨中: ['allocating'],
                结算中: ['pending_settlement', 'rejected_pending_settlement', 'pending_confirmation'],
                已完成: ['completed', 'terminated']
            }
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-select组件应用属性
        elSelectAttrs() {
            return {
                ...this.defaultElSelectAttrs,
                placeholder: `请选择${this.config.name}`,
                ...(this.config.elSelectAttrs || {})
            };
        },
        elOptions() {
            return this.allElOptions.filter((item) => {
                return (this.stateMap[this.config.modelObj.tabSelected] || []).includes(item.value);
            });
        }
    },
    watch: {
        'config.modelObj.tabSelected': {
            handler: 'tabSelectedHandler',
            immediate: true,
            deep: true
        }
    },
    async created() {
        await this.queryAllocateTaskStateList();
    },
    methods: {
        // 调拨任务任务状态查询
        async queryAllocateTaskStateList() {
            const { code, message, result } = await this.$service.warehouse.allocation.queryAllocateTaskStateList();
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
                return;
            }
            this.allElOptions = result.map((item) => {
                return {
                    ...item,
                    label: item.desc,
                    value: item.code
                };
            });
        },
        // 下拉数据切换操作
        handleChange() {
            this.config.changeHandler && this.config.changeHandler();
        },
        tabSelectedHandler(newVal) {
            if (tabSelected === newVal) return;
            tabSelected = newVal;
            this.config.modelObj[this.config.modelKey] = [];
        }
    }
};
</script>
