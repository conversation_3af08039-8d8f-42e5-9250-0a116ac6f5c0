/**
 * 资产相关表单组件
 */
import commonItems from './common-items.js';

const assetCode = {
    ...commonItems.input,
    name: '产品序列号',
    modelKey: 'assetCode'
};
const customerAssetCode = {
    ...commonItems.input,
    name: '客户资产编码',
    modelKey: 'customerAssetCode'
};
const productName = {
    ...commonItems.input,
    name: '产品名称',
    modelKey: 'productName'
};
const productList = {
    ...commonItems.input,
    name: '产品名称',
    modelKey: 'productList'
};
const assetType = {
    ...commonItems.select,
    name: '资产类型',
    modelKey: 'assetType',
    elOptions: [
        { label: '新机', value: '新机' },
        { label: '旧机', value: '旧机' },
        { label: '撤机', value: '撤机' }
    ]
};
const inWarehouseDateRange = {
    ...commonItems.dateRange,
    name: '入库起止日期',
    component: 'SnbcFormDateRangePicker',
    modelKey: 'inWarehouseDateRange'
};
const outWarehouseDateRange = {
    ...commonItems.dateRange,
    name: '出库起止日期',
    component: 'SnbcFormDateRangePicker',
    modelKey: 'outWarehouseDateRange'
};
const assetState = {
    ...commonItems.select,
    name: '资产状态',
    modelKey: 'assetState',
    elOptions: [
        { label: '正常', value: '正常' },
        { label: '货损', value: '货损' },
        { label: '丢失', value: '丢失' }
    ]
};
const inState = {
    ...commonItems.select,
    name: '在库状态',
    modelKey: 'inState',
    elOptions: [
        { label: '在库', value: '在库' },
        { label: '干线在途', value: '干线在途' },
        { label: '调拨在途', value: '调拨在途' }
    ]
};
const associatedTaskName = {
    ...commonItems.input,
    name: '关联任务名称',
    modelKey: 'associatedTaskName'
};
const erpCustomerName = {
    ...commonItems.input,
    name: '所属客户全称',
    modelKey: 'erpCustomerName'
};

export default {
    assetCode,
    customerAssetCode,
    productName,
    assetType,
    inWarehouseDateRange,
    outWarehouseDateRange,
    assetState,
    inState,
    associatedTaskName,
    productList,
    erpCustomerName
};
