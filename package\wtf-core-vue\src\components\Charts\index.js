import Keyboard from './src/Keyboard.vue';
import LineMarker from './src/LineMarker.vue';
import Mix<PERSON>hart from './src/MixChart.vue';

Keyboard.install = function(Vue) {
    Vue.component(Keyboard.name, Keyboard);
};
LineMarker.install = function(Vue) {
    Vue.component(LineMarker.name, LineMarker);
};
MixChart.install = function(Vue) {
    Vue.component(MixChart.name, MixChart);
};

export {
    Keyboard,
    LineMarker,
    MixChart
};
