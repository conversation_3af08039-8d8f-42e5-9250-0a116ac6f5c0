<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig" />
            <snbc-base-dialog ref="dialogRef" :config="dialogConfig" @confirm="handleSubmit">
                <template slot="dialog-body">
                    <snbc-form ref="snbcFormRef" :form="form" :config="formConfig">
                        <template #form-body>
                            <snbc-form-item v-if="mode === 'add'" :config="customerSelect"></snbc-form-item>
                            <snbc-form-item v-if="mode === 'add'" ref="productMultiSelectRef"
                                :config="productMultiSelect"></snbc-form-item>
                            <snbc-form-item v-for="(item, index) in formItems" :key="index" :config="item" />
                        </template>
                    </snbc-form>
                </template>
            </snbc-base-dialog>
        </div>
    </div>
</template>

<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcBaseDialog from 'warehouse/components/snbc-dialog/SnbcBaseDialog.vue';
import SnbcForm from 'warehouse/components/snbc-form/SnbcForm.vue';
import SnbcFormItem from 'warehouse/components/snbc-form/SnbcFormItem.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import FormRules from 'warehouse/common/form-rules/index.js';

const {
    number,
    assertDate,
    customerSelect,
    productName,
    customerName,
    productMultiSelect,
    productSelect
} = FormItems;

const {
    selectRequired,
    inputRequired,
    inputRequiredOnBlur
} = FormRules;

// 查询参数
const queryParams = {
    customerCode: '',
    productId: ''
};

// 查询区域配置项
const queryConfigItems = [
    customerSelect,
    productSelect
];

// 新增弹窗配置
const addDialogConfigItems = [
    {
        ...number,
        name: '元/台天',
        modelKey: 'feeRule',
        elInputNumberAttrs: {
            precision: 2,
            min: 0
        }
    },
    {
        ...number,
        name: '免费天数',
        modelKey: 'freeDay',
        elInputNumberAttrs: {
            precision: 0,
            min: 0
        }
    },
    assertDate
];

// 编辑弹窗配置
const editDialogConfigItems = [
    {
        ...customerName,
        elInputAttrs: {
            disabled: true
        }
    },
    {
        ...productName,
        elInputAttrs: {
            disabled: true
        }
    },
    {
        ...number,
        name: '元/台天',
        modelKey: 'feeRule',
        elInputNumberAttrs: {
            precision: 2,
            min: 0
        }
    },
    {
        ...number,
        name: '免费天数',
        modelKey: 'freeDay',
        elInputNumberAttrs: {
            precision: 0,
            min: 0
        }
    },
    assertDate
];

const form = {
    productIdList: [],
    customerCode: '',
    feeRule: undefined,
    freeDay: undefined,
    assertDate: '',
    productName: '',
    customerName: ''
}

// 弹窗校验规则
const rules = {
    customerCode: [selectRequired('客户名称')],
    productIdList: [selectRequired('产品名称')],
    customerName: [inputRequired('客户名称')],
    productName: [inputRequired('产品名称')],
    feeRule: [inputRequiredOnBlur('金额')],
    freeDay: [inputRequiredOnBlur('免费天数')],
    assertDate: [inputRequired('生效日期')]
};

export default {
    name: 'WarehouseFeeRules',
    components: {
        SnbcBaseTable,
        SnbcBaseDialog,
        SnbcForm,
        SnbcFormItem
    },
    mixins: [functions],
    data() {
        const {
            list: listApi,
            add: addApi,
            edit: editApi
        } = this.$service.warehouse.warehouseFeeRules;
        return {
            listApi,
            addApi,
            editApi,
            tableConfig: {
                queryParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: listApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    { label: '客户名称', prop: 'customerName', show: true, minWidth: 120 },
                    { label: '产品名称', prop: 'productName', show: true, minWidth: 120 },
                    { label: '元/台天', prop: 'feeRule', show: true, minWidth: 120 },
                    { label: '免费天数', prop: 'freeDay', show: true, minWidth: 120 },
                    { label: '生效日期', prop: 'assertDate', show: true, minWidth: 120 }
                ],
                operations: [
                    { name: '编辑', type: 'primary', handleClick: this.handleEdit },
                ],
                headerButtons: [
                    { name: '新增', type: 'primary', handleClick: this.handleAdd }
                ]
            },
            form: {
                ...form
            },
            formConfig: {
                elFormAttrs: {
                    'label-width': '120px',
                    rules
                }
            },
            // 弹窗配置
            dialogConfig: {
                rules,
                items: editDialogConfigItems,
                elDialogAttrs: {
                    title: ''
                }
            },
            mode: 'add'
        };
    },
    computed: {
        customerSelect() {
            return {
                modelObj: this.form,
                elFormItemAttrs: {
                    label: customerSelect.name,
                    ...(customerSelect.elFormItemAttrs || {})
                },
                ...customerSelect
            }
        },
        productMultiSelect() {
            return {
                modelObj: this.form,
                elFormItemAttrs: {
                    label: productMultiSelect.name,
                    ...(productMultiSelect.elFormItemAttrs || {})
                },
                ...productMultiSelect
            }
        },
        formItems() {
            const addItems = addDialogConfigItems.map((item) => {
                return {
                    modelObj: this.form,
                    elFormItemAttrs: {
                        label: item.name,
                        ...(item.elFormItemAttrs || {})
                    },
                    ...item
                };
            });
            const editItems = editDialogConfigItems.map((item) => {
                return {
                    modelObj: this.form,
                    elFormItemAttrs: {
                        label: item.name,
                        ...(item.elFormItemAttrs || {})
                    },
                    ...item
                };
            });
            return this.mode === 'add' ? addItems : editItems
        }
    },
    watch: {
        'form.customerCode': function (newVal, oldVal) {
            const data = {
                customerCode: newVal
            }
            this.form.productIdList = [];
            return this.$refs.productMultiSelectRef?.$children[0]?.queryWarehouseProduct(data);
        }
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.handleQuery();
    },
    methods: {
        // 弹窗打开
        openDialog(mode) {
            this.mode = mode;
            this.$refs.dialogRef.openDialog();
        },
        // 编辑操作
        handleEdit(row) {
            this.openDialog('edit');
            Object.assign(this.form, row);
            this.dialogConfig.elDialogAttrs.title = '区仓收费规则编辑'
        },
        // 新增操作
        handleAdd() {
            this.openDialog('add');
            // 移除校验结果并重置数据
            this.$refs.snbcFormRef && this.$refs.snbcFormRef.getFormRef().resetFields();
            Object.assign(this.form, form);
            this.dialogConfig.elDialogAttrs.title = '区仓收费规则新增'
        },
        // 新增或编辑提交
        async handleSubmit() {
            try {
                // 表单校验
                await this.$refs.snbcFormRef.getFormRef().validate();
            } catch (e) {
                return e.message
            }
            await this.$tools.confirm('确认提交？');
            const params = {};
            let submitApi = this.addApi;
            if (this.mode === 'edit') {
                params.id = this.form.id;
                params.feeRule = this.form.feeRule;
                params.freeDay = this.form.freeDay;
                params.assertDate = this.form.assertDate;
                params.productIdList = [this.form.productId];
                params.customerCode = this.form.customerCode;
                submitApi = this.editApi;
            } else {
                params.feeRule = this.form.feeRule;
                params.freeDay = this.form.freeDay;
                params.assertDate = this.form.assertDate;
                params.productIdList = this.form.productIdList;
                params.customerCode = this.form.customerCode;
            }
            try {
                const res = await submitApi(params);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.$refs.dialogRef.hideDialog();
                this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        }
    }
};
</script>

<style lang="scss" scoped></style>
