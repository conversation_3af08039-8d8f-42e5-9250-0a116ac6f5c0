/// CASTING A STRING TO A NUMBER
/// @param {Number} $number
/// @param {Number} $unit
/// <AUTHOR>
/// @link http://hugogiraudel.com/2014/01/15/sass-string-to-number/

@function unit-length($number, $unit) {
    $strings: 'px' 'cm' 'mm' '%' 'ch' 'pica' 'in' 'em' 'rem' 'pt' 'pc' 'ex' 'vw' 'vh' 'vmin' 'vmax';
    $units:   1px  1cm  1mm  1%  1ch  1pica  1in  1em  1rem  1pt  1pc  1ex  1vw  1vh  1vmin  1vmax;
    $index: index($strings, $unit);

    @if not $index {
        @warn "Invalid unit `#{$unit}`.";
        @return false;
    }

    @return $number * nth($units, $index);
}