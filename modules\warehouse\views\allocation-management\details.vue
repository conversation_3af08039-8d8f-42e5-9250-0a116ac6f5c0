<template>
    <div class="view">
        <div class="content">
            <el-page-header @back="goBack" content="调拨任务详情"></el-page-header>
            <snbc-card :title="title">
                <template #card-body>
                    <snbc-descriptions :items="basicInfo" :config="config" />
                </template>
            </snbc-card>
            <snbc-tabs :tabs="tabs" style="margin-bottom: 32px">
                <template #taskDetails>
                    <allocation-task-details
                        :taskDetails="taskDetails"
                        :isDetails="true"
                        :allocateTaskCode="allocateTaskCode"
                        :whetherSpecifyAsset="whetherSpecifyAsset"
                    >
                    </allocation-task-details>
                </template>
                <template #logisticsInfo>
                    <allocation-logistics-info-details :allocateTaskCode="allocateTaskCode">
                    </allocation-logistics-info-details>
                </template>
                <template #operateLog>
                    <allocation-operate-log
                        :allocateTaskCode="allocateTaskCode"
                        :whetherSpecifyAsset="whetherSpecifyAsset"
                    >
                    </allocation-operate-log>
                </template>
            </snbc-tabs>
            <div v-if="type === 'audit'" class="button-block">
                <el-button type="danger" @click="handleRefuse">拒 绝</el-button>
                <el-button type="primary" @click="handlePass">通 过</el-button>
            </div>
        </div>
        <AllocationRefuseDialog ref="refuseDialogRef"></AllocationRefuseDialog>
    </div>
</template>
<script>
import functions from 'frame/mixins/functions.js';
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import SnbcTabs from 'warehouse/components/snbc-tabs/SnbcTabs.vue';
import AllocationOperateLog from './components/OperateLog.vue';
import AllocationTaskDetails from './components/TaskDetails.vue';
import AllocationRefuseDialog from './components/RefuseDialog.vue';
import SnbcDescriptions from 'warehouse/components/snbc-descriptions/SnbcDescriptions.vue';
import AllocationLogisticsInfoDetails from './components/LogisticsInfoDetails.vue';

export default {
    name: 'AllocationDetails',
    components: {
        SnbcCard,
        SnbcTabs,
        AllocationOperateLog,
        AllocationTaskDetails,
        AllocationRefuseDialog,
        SnbcDescriptions,
        AllocationLogisticsInfoDetails
    },
    mixins: [functions],
    data() {
        return {
            tabs: [
                {
                    label: '任务明细',
                    id: 'taskDetails'
                },
                {
                    label: '物流信息',
                    id: 'logisticsInfo'
                },
                {
                    label: '操作记录',
                    id: 'operateLog'
                }
            ],
            basicInfo: [
                { label: '备货申请任务编号', key: 'stockRequestCode', value: '' },
                { label: '任务来源', key: 'taskSource', value: '' },
                {
                    label: '是否是指定资产',
                    key: 'whetherSpecifyAsset',
                    value: ''
                },
                { label: '来源区仓', key: 'sourceWarehouseName', value: '' },
                { label: '期望到货日期', key: 'expectedDate', value: '' },
                { label: '目标区仓', key: 'targetWarehouseName', value: '' },
                { label: '调拨任务名称', key: 'allocateTaskName', value: '' },
                { label: '备注', key: 'remark', value: '' }
            ],
            config: {
                elDescriptionsAttrs: {
                    column: 2,
                    labelStyle: {
                        width: '120px'
                    },
                    contentStyle: {
                        'max-width': '100px'
                    }
                }
            },
            taskDetails: [],
            allocateTaskCode: '',
            whetherSpecifyAsset: false,
            type: '',
            title: '调拨任务基本信息'
        };
    },
    created() {
        this.init();
        this.isHasMenu();
        this.initNativeDrop();
    },
    methods: {
        // 初始化
        init() {
            this.allocateTaskCode = this.$route.query.allocateTaskCode;
            this.title = `调拨任务编号：${this.allocateTaskCode}`;
            this.type = this.$route.query.type;
            this.whetherSpecifyAsset = !!Number(this.$route.query.whetherSpecifyAsset);
            this.getAllocateTaskBaseInfo();
            if (this.whetherSpecifyAsset) {
                this.querySpecifyAllocateDetailEditPage();
                return;
            }
            this.queryAllocateDetailNotSpecified();
        },
        // 返回
        goBack() {
            this.$store
                .dispatch('tagsView/delView', {
                    path: '/app/allocation/allocation-details',
                    name: 'AllocationDetails'
                })
                .then(() => {
                    this.$store.commit('tagsView/TOGGLE_VIEW', true);
                    this.$router.go(-1);
                });
        },
        async handleRefuse() {
            this.$refs.refuseDialogRef.showDialog(this.allocateTaskCode);
        },
        async handlePass() {
            try {
                await this.$tools.confirm('请确认是否审核通过？');
                const { code, message } = await this.$service.warehouse.allocation.auditPassAllocateTask(
                    this.allocateTaskCode
                );
                if (code !== '000000') {
                    this.$tools.message.err(message || '调拨任务审核通过失败，请刷新页面重试');
                    return;
                }
                this.$tools.message.suc('调拨任务审核通过成功');
                this.goBack();
            } catch (e) {
                return e.message;
            }
        },
        // 调拨任务基础信息
        getAllocateTaskBaseInfo() {
            this.$service.warehouse.allocation
                .getAllocateTaskBaseInfo(this.allocateTaskCode)
                .then(({ code, result, message }) => {
                    if (code !== '000000') {
                        this.$tools.message.err(message || '系统异常');
                        return;
                    }

                    this.basicInfo.forEach((item) => {
                        if (item.key === 'whetherSpecifyAsset') {
                            item.value = result[item.key] ? '是' : '否';
                        } else {
                            item.value = result[item.key] || '无';
                        }
                    });
                });
        },
        // 查询非指定调拨任务任务明细（含申请明细）
        queryAllocateDetailNotSpecified() {
            this.$service.warehouse.allocation
                .queryAllocateDetailNotSpecified(this.allocateTaskCode)
                .then(({ code, result, message }) => {
                    if (code !== '000000') {
                        this.$tools.message.err(message || '系统异常');
                        return;
                    }
                    this.taskDetails = result.taskDetailList;
                });
        },
        // 查询指定调拨任务任务明细
        querySpecifyAllocateDetailEditPage() {
            this.$service.warehouse.allocation
                .querySpecifyAllocateDetailEditPage(this.allocateTaskCode)
                .then(({ code, result, message }) => {
                    if (code !== '000000') {
                        this.$tools.message.err(message || '系统异常');
                        return;
                    }
                    this.taskDetails = result;
                });
        }
    }
};
</script>
<style lang="scss" scoped>
@import '../../styles/mixins.scss';
.content {
    @include form-textarea(48px);
    .button-block {
        display: flex;
        justify-content: center;
    }
}
</style>
