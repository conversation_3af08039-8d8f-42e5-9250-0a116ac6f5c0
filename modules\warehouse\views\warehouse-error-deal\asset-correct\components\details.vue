<template>
    <el-dialog
        class="custom-dialog query-label-line2"
        :visible.sync="dialogVisible"
    >
        <div class="view">
            <div class="content">
                <snbc-card title="非法数据修正信息">
                    <template #card-body>
                        <snbc-descriptions
                            :items="basicInfo"
                            :config="config"
                        />
                    </template>
                </snbc-card>
                <snbc-card title="非法数据修正操作记录">
                    <template #card-body>
                        <el-timeline>
                            <el-timeline-item
                                v-for="item in form.assetCorrectLogs"
                                :key="item.key"
                                :timestamp="item.operateTime"
                                :hide-timestamp="true"
                                color="#409eff"
                            >
                                <el-card>
                                    <p>时间：{{ item.operateTime }}</p>
                                    <p>
                                        操作：{{ item.operateType }}-{{
                                            item.operateState
                                        }}
                                    </p>
                                    <p>操作人：{{ item.operateUserName }}</p>
                                    <p>内容：{{ item.operateContent }}</p>
                                </el-card>
                            </el-timeline-item>
                        </el-timeline>
                        <div
                            class="no-data"
                            v-if="!form.assetCorrectLogs.length"
                        >
                            暂无数据
                        </div>
                    </template>
                </snbc-card>
            </div>
        </div>
    </el-dialog>
</template>
<script>
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import SnbcDescriptions from 'warehouse/components/snbc-descriptions/SnbcDescriptions';

const info = [
    {
        label: '非法数据修正编号',
        key: 'applyCode',
        value: ''
    },
    {
        label: '非法数据修正名称',
        key: 'applyName',
        value: ''
    },
    {
        label: '区仓名称',
        key: 'warehouseName',
        value: ''
    },
    {
        label: '操作类型',
        key: 'operationType',
        value: ''
    },
    {
        label: '出入库时间',
        key: 'operationTime',
        value: ''
    },
    {
        label: '申请原因',
        key: 'applyReason',
        value: ''
    },
    {
        label: '区仓编号',
        key: 'warehouseCode',
        value: ''
    },
    {
        label: '产品序列号',
        key: 'assetCode',
        value: ''
    },
    {
        label: '产品名称',
        key: 'productName',
        value: ''
    },
    {
        label: '关联任务类型',
        key: 'associatedTaskType',
        value: ''
    },
    {
        label: '关联任务编号',
        key: 'associatedTaskCode',
        value: ''
    },
    {
        label: '图片',
        key: 'pictureUrl',
        value: '',
        type: 'image'
    }
];
export default {
    name: 'AssetCorrectDetails',
    components: {
        SnbcDescriptions,
        SnbcCard
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            form: {},
            dialogVisible: false,
            basicInfo: info,
            tableConfig: {
                elTableAttrs: {},
                elTableColumns: []
            },
            list: [],
            config: {
                elDescriptionsAttrs: {
                    column: 1
                }
            }
        };
    },
    methods: {
        openDialog(data) {
            this.form = data;
            this.dialogVisible = true;
            this.basicInfo.forEach((item) => {
                item.value = data[item.key];
            });
            this.list = data.assetCorrectLogs.map((item, index) => {
                return {
                    index: index + 1,
                    ...item
                };
            });
        }
    }
};
</script>
<style lang="scss" scoped></style>
