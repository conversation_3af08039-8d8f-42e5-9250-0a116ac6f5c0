<template>
    <el-dialog
        class="custom-dialog query-label-line2"
        :visible.sync="dialogVisible"
    >
        <div class="view">
            <div class="content">
                <snbc-card title="任务补录信息">
                    <template #card-body>
                        <snbc-descriptions
                            :items="basicInfo"
                            :config="config"
                        />
                        <el-table
                            :data="form.additionalRecords"
                            border
                            style="width: 100%"
                        >
                            <el-table-column
                                prop="assetCode"
                                label="产品序列号"
                                min-width="180"
                            >
                            </el-table-column>
                            <el-table-column
                                prop="operateTime"
                                label="时间"
                                min-width="190"
                            >
                            </el-table-column>
                            <el-table-column
                                prop="operateType"
                                label="类型"
                                min-width="100"
                            >
                            </el-table-column>
                            <el-table-column
                                prop="taskAdditionalType"
                                min-width="120"
                                label="补录类型"
                            >
                            </el-table-column>
                            <el-table-column
                                prop="productName"
                                min-width="120"
                                label="产品名称"
                            >
                            </el-table-column>
                        </el-table>
                    </template>
                </snbc-card>
                <snbc-card title="任务补录操作记录">
                    <template #card-body>
                        <el-timeline>
                            <el-timeline-item
                                v-for="item in form.additionalLogs"
                                :key="item.key"
                                :timestamp="item.operateTime"
                                :hide-timestamp="true"
                                color="#409eff"
                            >
                                <el-card>
                                    <p>时间：{{ item.operateTime }}</p>
                                    <p>
                                        操作：{{ item.operateType }}-{{
                                            item.operateState
                                        }}
                                    </p>
                                    <p>操作人：{{ item.operateUserName }}</p>
                                    <p>内容：{{ item.operateContent }}</p>
                                </el-card>
                            </el-timeline-item>
                        </el-timeline>
                        <div class="no-data" v-if="!form.additionalLogs.length">
                            暂无数据
                        </div>
                    </template>
                </snbc-card>
            </div>
        </div>
    </el-dialog>
</template>
<script>
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import SnbcDescriptions from 'warehouse/components/snbc-descriptions/SnbcDescriptions';

const info = [
    {
        label: '补录申请编码',
        key: 'taskAdditionalCode',
        value: ''
    },
    {
        label: '补录申请名称',
        key: 'taskAdditionalName',
        value: ''
    },
    {
        label: '补录类型',
        key: 'taskAdditionalType',
        value: ''
    },
    {
        label: '申请原因',
        key: 'taskAdditionalReason',
        value: ''
    },
    {
        label: '关联任务编号',
        key: 'associatedTaskCode',
        value: ''
    },
    {
        label: '关联任务类型',
        key: 'associatedTaskType',
        value: ''
    },
    {
        label: '目的区仓名称',
        key: 'warehouseName',
        value: ''
    },
    {
        label: '源仓名称',
        key: 'srcWarehouseName',
        value: ''
    },
    {
        label: '关联任务名',
        key: 'associatedTaskName',
        value: ''
    },
    {
        label: '来源',
        key: 'source',
        value: ''
    },

    {
        label: '备注',
        key: 'remark',
        value: ''
    }
];
export default {
    name: 'TaskAdditionalDetails',
    components: {
        SnbcDescriptions,
        SnbcCard
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            form: {},
            dialogVisible: false,
            basicInfo: info,
            tableConfig: {
                elTableAttrs: {},
                elTableColumns: []
            },
            list: [],
            config: {
                elDescriptionsAttrs: {
                    column: 1
                }
            }
        };
    },
    methods: {
        openDialog(data) {
            this.form = data.baseInfo;
            this.form.additionalLogs = data.additionalLogs;
            this.form.additionalRecords = data.additionalRecords;
            this.dialogVisible = true;
            this.basicInfo.forEach((item) => {
                item.value = data.baseInfo[item.key];
            });
            this.additionalRecords = data.additionalRecords;
            this.list = data.additionalLogs.map((item, index) => {
                return {
                    index: index + 1,
                    ...item
                };
            });
        }
    }
};
</script>
<style lang="scss" scoped></style>
