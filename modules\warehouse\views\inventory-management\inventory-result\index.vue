<template>
    <div class="view">
        <div class="content">
            <snbc-descriptions :items="basicInfo" :config="config" />
            <snbc-base-table ref="tableRef" :table-config="tableConfig" />
            <snbc-base-form-dialog ref="dialogRef" :config="dialogConfig" @submit="handleSubmit" />
        </div>
    </div>
</template>

<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcDescriptions from 'warehouse/components/snbc-descriptions/SnbcDescriptions.vue';
import FormItems from 'warehouse/common/form-items/inventory-items';
import FormRules from 'warehouse/common/form-rules';
import SnbcBaseFormDialog from 'warehouse/components/snbc-dialog/SnbcBaseFormDialog.vue';
import CommonFormItems from 'warehouse/common/form-items/index.js';

const { input, select } = CommonFormItems;

const { remark } = FormItems;
const dialogConfigItems = [remark];
const { inputRequired } = FormRules;
const rules = {
    remark: [inputRequired('修正备注')]
};
const queryParams = {
    taskCode: '',
    planCode: '',
    productName: '',
    invResultList: '',
    assetCode: '',
    assetState: ''
};

export default {
    name: 'WarehouseInventoryResult',
    components: {
        SnbcDescriptions,
        SnbcBaseTable,
        SnbcBaseFormDialog
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        const { getInventoryResult: listApi } = this.$service.warehouse.inventoryManagement;
        return {
            listApi,
            basicInfo: [
                { label: '盘点名称', key: 'planName', value: '' },
                { label: '盘点开始日期', key: 'startInventoryTime', value: '' },
                { label: '盘点描述', key: 'planDesc', value: '' },
                { label: '区仓名称', key: 'warehouseName', value: '' },
                { label: '盘点人', key: 'operateUserName', value: '' },
                { label: '盘点状态', key: 'taskStateName', value: '' },
                { label: '盘点任务单号', key: 'taskCode', value: '' },
                { label: '系统库存数量', key: 'assetNumber', value: '' },
                { label: '实际库存数量', key: 'finishNumber', value: '' },
                { label: '正常数量', key: 'normalNumber', value: '' },
                { label: '无码数量', key: 'noCodeNumber', value: '' },
                { label: '货损数量', key: 'damageNumber', value: '' }
            ],
            config: {
                elDescriptionsAttrs: {
                    column: 3
                }
            },
            tableConfig: {
                queryParams,
                queryConfig: {
                    items: [
                        {
                            ...input,
                            name: '产品名称',
                            modelKey: 'productName'
                        },
                        {
                            ...input,
                            name: '产品序列号',
                            modelKey: 'assetCode'
                        },
                        {
                            ...select,
                            name: '盘点结果',
                            modelKey: 'invResultList',
                            elOptions: [
                                { label: '盘盈', value: 'profit' },
                                { label: '盘亏', value: 'loss' },
                                { label: '正常', value: 'normal' }
                            ]
                        },
                        {
                            ...select,
                            name: '资产状态',
                            modelKey: 'assetState',
                            elOptions: [
                                { label: '正常', value: 'normal' },
                                { label: '货损', value: 'damage' }
                            ]
                        }
                    ]
                },
                queryApi: listApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '物料编码',
                        prop: 'materialCode',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '产品序列号',
                        prop: 'assetCode',
                        show: true,
                        minWidth: 180
                    },
                    {
                        label: '盘点结果',
                        prop: 'invResultName',
                        renderMode: 'tag',
                        elTagAttrsFn: (item) => {
                            if (item.invResult === 'normal') {
                                return {
                                    type: 'success'
                                };
                            }
                            return {
                                type: 'danger'
                            };
                        },
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '资产状态',
                        prop: 'assetStateName',
                        renderMode: 'tag',
                        elTagAttrsFn: (item) => {
                            if (item.assetState === 'normal') {
                                return {
                                    type: 'success'
                                };
                            }
                            return {
                                type: 'danger'
                            };
                        },
                        show: true,
                        minWidth: 140
                    },
                    {
                        label: '数据是否修正',
                        prop: 'isConfirmName',
                        show: true,
                        minWidth: 120,
                        renderMode: 'tag',
                        handleClick: this.handleConfirm,
                        elTagAttrsFn: (item) => {
                            if (item.isConfirm === 0) {
                                return {
                                    type: 'danger '
                                };
                            }
                            return {
                                type: 'success'
                            };
                        }
                    }
                ],
                hooks: {
                    queryParamsHook: this.queryParamsHook
                }
            },
            dialogConfig: {
                rules,
                items: dialogConfigItems
            }
        };
    },
    computed: {},
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.tableConfig.queryParams.taskCode = this.$route.query.taskCode;
        this.$refs.tableRef.handleQuery();
        this.getResultDetail();
    },
    methods: {
        // 填写备注信息
        handleConfirm(row) {
            this.$refs.dialogRef.baseAddDialog(row, '修正备注');
        },
        async handleSubmit({ form }) {
            await this.$tools.confirm('确认提交？');
            const params = this.$tools.deepClone(form);
            try {
                const res = await this.$service.warehouse.inventoryManagement.addRemarkForResult(params);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.$refs.dialogRef.hideDialog();
                await this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },

        // 获取结果信息
        async getResultDetail() {
            const data = await this.$service.warehouse.inventoryManagement.getResultDetail(queryParams.taskCode);
            if (data.code !== '000000') {
                this.$tools.message.err(data.message || '系统异常');
                return;
            }
            this.basicInfo.forEach((item) => {
                item.value = data.result[item.key];
            });
        },

        // 格式化参数
        queryParamsHook(params) {
            params.taskCode = this.$route.query.taskCode;
            Object.keys(params).forEach((key) => {
                !params[key] && delete params[key];
            });
            if (params.invResultList) {
                params.invResultList = [params.invResultList];
            }
            if (params.assetState) {
                params.assetState = [params.assetState];
            }
        }
    }
};
</script>

<style lang="scss" scoped></style>
