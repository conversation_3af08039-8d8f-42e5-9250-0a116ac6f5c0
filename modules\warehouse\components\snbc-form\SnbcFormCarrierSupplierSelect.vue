<template>
    <el-form-item v-bind="elFormItemAttrs">
        <el-select
            v-model="config.modelObj[config.modelKey]"
            v-bind="elSelectAttrs"
            @change="handleChange"
        >
            <select-all :config="selectAllConfig" />
            <el-option
                v-for="(option, index) in elOptions"
                :key="index"
                :label="option.label"
                :value="option.value"
            />
        </el-select>
    </el-form-item>
</template>
<script>
import SelectAll from './components/SelectAll.vue';

export default {
    name: 'SnbcFormSupplierSelect',
    components: { SelectAll },
    props: {
        /**
         * SnbcFormSelect组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elFormItemAttrs: {},
                    elSelectAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-select组件默认属性设置
            defaultElSelectAttrs: {
                'filterable': true,
                'clearable': true,
                'collapse-tags': true
            },
            // 下拉数据
            elOptions: []
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-select组件应用属性
        elSelectAttrs() {
            return {
                ...this.defaultElSelectAttrs,
                placeholder: `请选择${this.config.name}`,
                ...(this.config.elSelectAttrs || {})
            };
        },
        // 全选组件配置
        selectAllConfig() {
            return {
                ...this.config,
                elOptions: this.elOptions
            };
        }
    },
    created() {
        this.getSupplierForm();
    },
    methods: {
        // 物流供应商查询
        async getSupplierForm() {
            try {
                const res =
                    await this.$service.warehouse.allocation.getSupplierForm();
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                if (result.length > 0) {
                    this.elOptions = result.map((item) => {
                        return {
                            ...item,
                            label: item.supplierName,
                            value: item.supplierCode
                        };
                    });
                } else {
                    this.elOptions = [
                        {
                            label: '暂无数据',
                            value: '',
                            disabled: true
                        }
                    ];
                }
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 下拉数据切换操作
        handleChange() {
            this.config.changeHandler && this.config.changeHandler();
        }
    }
};
</script>
