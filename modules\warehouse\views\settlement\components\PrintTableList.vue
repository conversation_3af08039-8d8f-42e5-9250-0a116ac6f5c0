<template>
    <el-table
        class="snbc-table"
        :data="tableData"
        v-bind="elTableAttrs"
        :cell-class-name="'snbc-table-cell'"
        :row-class-name="rowClassName"
        :span-method="spanMethod"
    >
        <el-table-column
            v-for="column in elTableColumns"
            :key="column.prop"
            :label="column.label"
            :prop="column.prop"
            v-bind="column.elTableColumnAttrs"
        >
            <template slot-scope="scope">
                <!-- 单元格内容渲染 -->
                <template>
                    <span :class="cellClassName(column, scope.row)">{{
                        column.render ? column.render(scope.row[column.prop], scope.row) : scope.row[column.prop]
                    }}</span>
                </template>
            </template>
        </el-table-column>
    </el-table>
</template>
<script>
export default {
    name: 'PrintTableList',
    props: {
        /**
         * 列表配置项集合
         */
        config: {
            type: Object,
            default() {
                return {
                    // table属性
                    elTableAttrs: {},
                    // table事件
                    elTableListeners: {},
                    // table列
                    elTableColumns: [],
                    // table行操作
                    operations: [],
                    // 操作列宽度
                    operationColumnWidth: 0,
                    // 多选操作
                    selectionAble: false,
                    // 行样式-function
                    rowClassName: null,
                    // 合并行或列的计算方法-function
                    spanMethod: null,
                    // 表格行是否可选计算方法
                    selectable: null
                };
            }
        },
        /**
         * 列表数据
         */
        list: {
            type: Array,
            default() {
                return [];
            }
        }
    },
    data() {
        return {
            // el-table组件默认属性
            defaultElTableAttrs: {
                'border': true,
                'width': '100%',
                'header-cell-style': { background: '#F5F6FA' }
            },
            // el-table-column默认属性
            defaultElTableColumnAttrs: {
                'sortable': false,
                'align': 'center',
                'show-overflow-tooltip': true
            }
        };
    },
    computed: {
        // 列表数据
        tableData() {
            return this.list;
        },
        // el-table应用属性
        elTableAttrs() {
            return {
                ...this.defaultElTableAttrs,
                ...(this.config.elTableAttrs || {})
            };
        },
        // el-table-column应用属性
        elTableColumns() {
            return (this.config.elTableColumns || [])
                .map((column) => {
                    return {
                        ...column,
                        elTableColumnAttrs: {
                            'min-width': column.minWidth,
                            ...this.defaultElTableColumnAttrs,
                            ...(column.elTableColumnAttrs || {})
                        }
                    };
                })
                .filter((item) => item.show);
        },
        // 操作列配置
        operations() {
            return this.config.operations || [];
        },
        // 操作列宽度计算
        operationColumnWidth() {
            return (
                this.config.operationColumnWidth ||
                this.operations.reduce((total, column) => {
                    return total + (column.width || 80);
                }, 0)
            );
        },
        // 操作按钮展示控制
        showOperation() {
            return (operation, row) => {
                if (operation.handleShow) {
                    return operation.handleShow(row);
                }
                return true;
            };
        }
    },
    methods: {
        // 表格行样式类
        rowClassName({ row, rowIndex }) {
            if (this.config.rowClassName) {
                return this.config.rowClassName({ row, rowIndex }) || '';
            }
        },
        // 表格单元格样式类
        cellClassName(column, row) {
            if (column.cellClassName) {
                return column.cellClassName(column, row);
            }
            return '';
        },
        // 合并行或列的计算方法
        spanMethod({ row, column, rowIndex, columnIndex }) {
            if (this.config.spanMethod) {
                return (
                    this.config.spanMethod({
                        row,
                        column,
                        rowIndex,
                        columnIndex
                    }) || {
                        rowspan: 0,
                        colspan: 0
                    }
                );
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.snbc-table {
    font-size: 16px;
    color: #000000;
    &::before {
        background-color: #000000 !important;
        border-bottom: 1px solid #000000;
    }
    &::after {
        background-color: #000000 !important;
        border-right: 1px solid #000000;
    }
    ::v-deep td {
        border-color: #000000 !important;
        border-bottom: 1px solid #000000 !important;
    }

    ::v-deep th {
        border-left: 1px solid #000 !important;
        border-bottom: 1px solid #000 !important;
    }

    ::v-deep tr {
        border-bottom: 1px solid #000 !important;
    }

    ::v-deep .is-leaf {
        border-bottom: 1px solid #000 !important;
        border-top: 1px solid #000 !important;
    }

    ::v-deep .el-table__header {
        border-bottom: 1px solid #000 !important;
    }

    ::v-deep .el-table__row,
    ::v-deep .has-gutter {
        height: 40px !important;
        border-bottom: 1px solid #000 !important;
    }

    ::v-deep .el-button {
        padding: 0 10px;
        height: 28px;
    }
    ::v-deep .el-table__header .el-table__cell {
        padding: 0;
        height: 40px !important;
    }
    ::v-deep .el-table__row .cell {
        font-size: 16px;
    }

    ::v-deep .el-table__header-wrapper {
        border-bottom: 1px solid #000 !important;
    }

    ::v-deep .el-table__body-wrapper {
        overflow: hidden;
    }

    ::v-deep .el-table__body {
        border-left: 1px solid #000 !important;
    }
}

::v-deep .snbc-table-cell {
    padding: 0;
    border-color: #000000 !important;
}

// ::v-deep.el-table--border {
// border-color: #000000 !important;
// }
::v-deep.el-table--group {
    border-color: #000000 !important;
}
</style>
