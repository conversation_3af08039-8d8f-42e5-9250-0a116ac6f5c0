<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig" />
            <snbc-base-form-dialog ref="dialogRef" :config="dialogConfig" @submit="handleSubmit" />
        </div>
        <MatierialDialog ref="matierialDialogRef" @queryList="queryList"></MatierialDialog>
    </div>
</template>

<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcBaseFormDialog from 'warehouse/components/snbc-dialog/SnbcBaseFormDialog.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import MatierialDialog from 'warehouse/views/warehouse-management/product-info/components/MatierialDialog';
import { selectRequired, inputRequired, maxLength } from 'warehouse/common/form-rules/index.js';

const { customerSelect, productSelect, select, number, productName, input } = FormItems;

// 弹窗校验规则
const rules = {
    customerCode: [selectRequired('客户名称')],
    productName: [inputRequired('产品名称'), maxLength(30)],
    productType: [selectRequired('产品类型')],
    freeLength: [inputRequired('净长')],
    freeWidth: [inputRequired('净宽')],
    sharingLength: [inputRequired('公摊长')],
    sharingWidth: [inputRequired('公摊宽')],
    stackingFactor: [inputRequired('堆叠系数')]
};

// 查询参数
const queryParams = {
    customerCode: '',
    productName: '',
    productType: '',
    materialsCode: ''
};

// 查询区域配置项
const queryConfigItems = [
    customerSelect,
    {
        ...productSelect,
        modelKey: 'productName',
        type: 'name'
    },
    {
        ...select,
        name: '产品类型',
        modelKey: 'productType',
        elOptions: [
            { label: '雨棚', value: '雨棚' },
            { label: '柜机', value: '柜机' },
            { label: '阳光房', value: '阳光房' }
        ]
    },
    {
        ...input,
        name: '物料编码',
        modelKey: 'materialsCode'
    }
];

const items = [
    {
        ...select,
        name: '产品类型',
        modelKey: 'productType',
        elOptions: [
            { label: '雨棚', value: '雨棚' },
            { label: '柜机', value: '柜机' },
            { label: '阳光房', value: '阳光房' }
        ]
    },
    {
        ...number,
        name: '净长',
        modelKey: 'freeLength',
        elInputNumberAttrs: {
            min: 0,
            precision: 2,
            max: Math.pow(10, 6) - 0.01
        }
    },
    {
        ...number,
        name: '净宽',
        modelKey: 'freeWidth',
        elInputNumberAttrs: {
            min: 0,
            precision: 2,
            max: Math.pow(10, 6) - 0.01
        }
    },
    {
        ...number,
        name: '公摊长',
        modelKey: 'sharingLength',
        elInputNumberAttrs: {
            min: 0,
            precision: 2,
            max: Math.pow(10, 6) - 0.01
        }
    },
    {
        ...number,
        name: '公摊宽',
        modelKey: 'sharingWidth',
        elInputNumberAttrs: {
            min: 0,
            precision: 2,
            max: Math.pow(10, 6) - 0.01
        }
    },
    {
        ...number,
        name: '堆叠系数',
        modelKey: 'stackingFactor',
        elInputNumberAttrs: {
            min: 1,
            precision: 0,
            max: Math.pow(10, 6) - 1
        }
    }
];

// 编辑弹窗配置
const editDialogConfigItems = [
    {
        ...productName,
        elInputAttrs: {
            disabled: true
        }
    },
    {
        ...customerSelect,
        elSelectAttrs: {
            disabled: true
        }
    },
    ...items
];

// 新增弹窗配置
const addDialogConfigItems = [productName, customerSelect, ...items];

export default {
    name: 'WarehouseProductInfo',
    components: {
        SnbcBaseTable,
        SnbcBaseFormDialog,
        MatierialDialog
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        const { getList: listApi, add: addApi, edit: editApi } = this.$service.warehouse.product;
        return {
            listApi,
            addApi,
            editApi,
            tableConfig: {
                queryParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: listApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '客户名称',
                        prop: 'customerName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '产品类型',
                        prop: 'productType',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '堆叠系数',
                        prop: 'stackingFactor',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '净长',
                        prop: 'freeLength',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '净宽',
                        prop: 'freeWidth',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '公摊长',
                        prop: 'sharingLength',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '公摊宽',
                        prop: 'sharingWidth',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '净面积',
                        prop: 'freeAcreage',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '占地面积',
                        prop: 'occupyAcreage',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '关联物料数量',
                        prop: 'materialTotal',
                        show: true,
                        minWidth: 120,
                        renderMode: 'button',
                        elButtonAttrs: {
                            type: 'text'
                        },
                        handleClick: this.handleClickCell
                    }
                ],
                operations: [
                    {
                        name: '编辑',
                        type: 'primary',
                        handleClick: this.handleEdit
                    }
                ],
                headerButtons: [
                    {
                        name: '新增',
                        type: 'primary',
                        elButtonAttrs: {
                            icon: 'el-icon-plus'
                        },
                        handleClick: this.handleAdd
                    }
                ]
            },
            // 弹窗配置
            dialogConfig: {
                rules,
                items: editDialogConfigItems
            }
        };
    },
    computed: {},
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        if (this.$route.query.customerCode) {
            this.tableConfig.queryParams.customerCode = this.$route.query.customerCode;
        }
        this.$refs.tableRef.handleQuery();
    },
    methods: {
        // 编辑操作
        handleEdit(row) {
            const data = this.$tools.cloneDeep(row);
            this.dialogConfig.items = editDialogConfigItems;
            this.$refs.dialogRef.baseEditDialog(data, '编辑产品');
        },
        // 新增操作
        handleAdd() {
            const data = {
                freeLength: 0,
                freeWidth: 0,
                sharingLength: 0,
                sharingWidth: 0,
                stackingFactor: 1
            };
            this.dialogConfig.items = addDialogConfigItems;
            this.$refs.dialogRef.baseAddDialog(data, '新增产品');
        },
        // adding or editting
        async handleSubmit({ mode, form }) {
            await this.$tools.confirm('确认提交？');
            const params = { ...form };
            let submitApi = this.addApi;
            if (mode === 'edit') {
                params.id = form.id;
                submitApi = this.editApi;
            }
            try {
                const res = await submitApi(params);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.tableConfig.queryConfig.items = [];
                this.$nextTick(() => {
                    this.tableConfig.queryConfig.items = queryConfigItems;
                });
                this.$refs.dialogRef.hideDialog();
                this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // navigate to product-martierial detail
        handleClickCell(row) {
            this.$refs.matierialDialogRef.showDialog(row);
        },
        queryList() {
            this.$refs.tableRef.queryList();
        },
        // export product info
        async handleExport() {
            const stream = await this.$service.warehouse.assets.exportAssetRecordList(this.logTableConfig.queryParams);
            this.$tools.downloadExprotFile(stream, `产品信息`, 'xlsx', 'xlsx').catch((e) => {
                this.$tools.message.err(e || '导出失败');
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
