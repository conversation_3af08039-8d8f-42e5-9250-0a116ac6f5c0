/**
 * 用于组件的list列表展示类组件
 */
export default {
    data() {
        return {
            // 列表数据
            list: [],
            // 编辑操作相关数据
            editInfo: {
                // 是否显示窗口
                isShow: false,
                // 窗口模式，true：编辑，false：新增
                isEdit: false,
                // 编辑数据,一般由组件进行重写
                formData: {}
            }
        };
    },
    methods: {
        // 返回上一页
        goBack() {
            this.$emit('view-back');
        },
        // 显示新增窗口
        showAdd() {
            // 清空数据
            Object.assign(this.editInfo.formData, this.$options.data().editInfo.formData);

            this.editInfo.isShow = true;
            this.editInfo.isEdit = false;
        },
        // 显示编辑窗口
        showEdit(row) {
            Object.assign(this.editInfo.formData, row);

            this.editInfo.isShow = true;
            this.editInfo.isEdit = true;
        },
        // 保存完成回调，isSuccess保存是否成功标识
        saveFinish(isSuccess) {
            if (!isSuccess) {
                return;
            }
            this.getList();
        },
        //  用于刷新右侧页面
        refreshSelectedTag(tag) {
            //  从缓存池中将当前刷新的路由删掉
            this.$store.dispatch('tagsView/delCachedView', tag);
            this.$router.push({
                path: `/redirect${  tag.path}`
            });
            setTimeout(() => {
                tag.status = 0;
                //  将当前刷新的路由放入缓存池中
                this.$store.dispatch('tagsView/addView', tag);
            }, 1000);
        }
    }
};
