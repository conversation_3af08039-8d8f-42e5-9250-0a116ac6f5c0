<template>
    <div>
        <snbc-card title="物流寻源结果" v-if="isSoureceShow" class="first-card">
            <template #card-body
                ><snbc-descriptions
                    :items="logisticsSourceDesc"
                    :config="config"
                />
            </template>
        </snbc-card>
        <snbc-card title="实际物流信息" v-if="isActualShow" class="second-card">
            <template #card-body
                ><snbc-descriptions
                    :items="actualLogisticsDesc"
                    :config="config"
                />
            </template>
        </snbc-card>
        <div class="no-info" v-if="!isSoureceShow && !isActualShow">
            暂无数据
        </div>
    </div>
</template>
<script>
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import SnbcDescriptions from 'warehouse/components/snbc-descriptions/SnbcDescriptions';

// 整车  零担  物流寻源和实际物流信息

// 整车的 descriptions
const descriptionsFTL = [
    { label: '运输方式', key: 'transportMode', value: '' },
    { label: '承运物流商', key: 'logisticsName', value: '' },
    { label: '联系电话', key: 'logisticsTel', value: '' },
    { label: '联系人', key: 'logisticsPerson', value: '' },
    { label: '里程（km）', key: 'mileage', value: '' },
    { label: '车型', key: 'carType', value: '' },
    { label: '调拨干线费（元）', key: 'trunkFee', value: '' },
    { label: '单价（元/km）', key: 'unitPrice', value: '' },
    { label: '提送费（元）', key: 'deliveryFee', value: '' },
    { label: '包装费（元）', key: 'packFee', value: '' },
    { label: '装卸费（元）', key: 'handingFee', value: '' },
    { label: '其他费用（元）', key: 'otherFee', value: '' },
    { label: '总价（元）', key: 'totalFee', value: '' },
    { label: '费用说明', key: 'feeDesc', value: '' }
];

// 零担的 descriptions
const descriptionsLTL = [
    { label: '运输方式', key: 'transportMode', value: '' },
    { label: '承运物流商', key: 'logisticsName', value: '' },
    { label: '联系电话', key: 'logisticsTel', value: '' },
    { label: '联系人', key: 'logisticsPerson', value: '' },
    { label: '里程（km）', key: 'mileage', value: '' },
    { label: '立方数', key: 'cubicNumber', value: '' },
    { label: '调拨干线费（元）', key: 'trunkFee', value: '' },
    { label: '单价（元/m³）', key: 'unitPrice', value: '' },
    { label: '提送费（元）', key: 'deliveryFee', value: '' },
    { label: '包装费（元）', key: 'packFee', value: '' },
    { label: '装卸费（元）', key: 'handingFee', value: '' },
    { label: '其他费用（元）', key: 'otherFee', value: '' },
    { label: '总价（元）', key: 'totalFee', value: '' },
    { label: '费用说明', key: 'feeDesc', value: '' }
];

// 实际物流信息的描述
const extraItems = [
    { label: '结算方', key: 'settlementParty', value: '' },
    { label: '结算价格（元）', key: 'settlementPrice', value: '' }
];

export default {
    name: 'AllocationLogisticsInfoDetails',
    components: {
        SnbcCard,
        SnbcDescriptions
    },
    props: {
        allocateTaskCode: {
            type: String,
            default() {
                return '';
            }
        }
    },
    data() {
        return {
            isActualShow: false,
            isSoureceShow: false,
            logisticsSourceDesc: [],
            actualLogisticsDesc: [],
            config: {
                elDescriptionsAttrs: {
                    column: 2,
                    labelStyle: {
                        width: '120px'
                    }
                }
            }
        };
    },
    watch: {
        allocateTaskCode: {
            handler: 'init',
            immediate: true
        }
    },
    methods: {
        async init() {
            if (!this.allocateTaskCode) return;
            const { result } =
                await this.$service.warehouse.allocation.queryAllLogisticsInfo(
                    this.allocateTaskCode
                );
            this.isActualShow = !!result.logisticsResultVO;
            this.isSoureceShow = !!result.logisticsSourceVO;
            // 物流寻源
            if (!this.isSoureceShow) return;
            if (result.logisticsSourceVO.transportMode === '整车') {
                this.logisticsSourceDesc = descriptionsFTL.map((item) => {
                    const value = result.logisticsSourceVO[item.key];
                    return {
                        ...item,
                        value:
                            typeof value === 'number' &&
                            !['mileage', 'cubicNumber'].includes(item.key)
                                ? value / 100
                                : value
                    };
                });
            } else {
                this.logisticsSourceDesc = descriptionsLTL.map((item) => {
                    const value = result.logisticsSourceVO[item.key];
                    return {
                        ...item,
                        value:
                            typeof value === 'number' &&
                            !['mileage', 'cubicNumber'].includes(item.key)
                                ? value / 100
                                : value
                    };
                });
            }
            // 实际物流信息
            if (!this.isActualShow) return;
            if (result.logisticsResultVO.transportMode === '整车') {
                this.actualLogisticsDesc = [
                    ...descriptionsFTL,
                    ...extraItems
                ].map((item) => {
                    const value = result.logisticsResultVO[item.key];
                    return {
                        ...item,
                        value:
                            typeof value === 'number' &&
                            !['mileage', 'cubicNumber'].includes(item.key)
                                ? value / 100
                                : value
                    };
                });
            } else {
                this.actualLogisticsDesc = [
                    ...descriptionsLTL,
                    ...extraItems
                ].map((item) => {
                    const value = result.logisticsResultVO[item.key];
                    return {
                        ...item,
                        value:
                            typeof value === 'number' &&
                            !['mileage', 'cubicNumber'].includes(item.key)
                                ? value / 100
                                : value
                    };
                });
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.no-info {
    line-height: 32px;
    text-align: center;
}
</style>
