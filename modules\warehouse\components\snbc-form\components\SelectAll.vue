<template>
    <el-checkbox
        v-if="config.selectAllAble"
        class="select-all"
        v-model="checked"
        @change="handleSelectAll"
    >
        全选
    </el-checkbox>
</template>
<script>
export default {
    name: 'SelectAll',
    props: {
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elOptions: [],
                    elFormItemAttrs: {},
                    elSelectAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // 全选标记
            checked: false
        };
    },
    computed: {
        // 下拉数据
        options() {
            return this.config?.elOptions || [];
        },
        // 判断是否是全选
        isChecked() {
            const { modelObj, modelKey, selectAllAble } = this.config;
            return (
                selectAllAble &&
                modelObj[modelKey].length &&
                modelObj[modelKey].length === this.options.length
            );
        }
    },
    watch: {
        isChecked(newVal, oldVal) {
            this.checked = newVal;
        }
    },
    methods: {
        // 全选操作
        handleSelectAll() {
            const { modelObj, modelKey } = this.config;
            if (this.checked) {
                const all = this.options.map((item) => item.value);
                this.$set(modelObj, modelKey, all);
            } else {
                this.$set(modelObj, modelKey, []);
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.select-all {
    width: 100%;
    padding: 0 10px;
    text-align: right;
}
</style>
