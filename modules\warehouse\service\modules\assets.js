/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        assets: {
            // 资产信息管理-历史资产
            getAssetRecordList(data) {
                if (
                    data.inWarehouseDateRange &&
                    data.inWarehouseDateRange.length
                ) {
                    data.intoStartDate = data.inWarehouseDateRange[0];
                    data.intoEndDate = data.inWarehouseDateRange[1];
                }
                if (
                    data.outWarehouseDateRange &&
                    data.outWarehouseDateRange.length
                ) {
                    data.outStartDate = data.outWarehouseDateRange[0];
                    data.outEndDate = data.outWarehouseDateRange[1];
                }
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/info/asset/asset_record_list`,
                    method: 'post',
                    data
                });
            },
            // 资产信息管理-历史资产导出
            exportAssetRecordList(data) {
                if (
                    data.inWarehouseDateRange &&
                    data.inWarehouseDateRange.length
                ) {
                    data.intoStartDate = data.inWarehouseDateRange[0];
                    data.intoEndDate = data.inWarehouseDateRange[1];
                }
                if (
                    data.outWarehouseDateRange &&
                    data.outWarehouseDateRange.length
                ) {
                    data.outStartDate = data.outWarehouseDateRange[0];
                    data.outEndDate = data.outWarehouseDateRange[1];
                }
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/info/asset/download_asset_record_list`,
                    method: 'post',
                    responseType: 'blob',
                    timeout: 30000,
                    data
                });
            },
            // 资产信息管理-在库资产导入
            importAssetExcel(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/warehouse/improt_download_rules/import_asset_excel`,
                    method: 'post',
                    data
                });
            },
            // 资产信息管理-在库资产
            getInLibraryList(data) {
                if (
                    data.inWarehouseDateRange &&
                    data.inWarehouseDateRange.length
                ) {
                    data.intoStartDate = data.inWarehouseDateRange[0];
                    data.intoEndDate = data.inWarehouseDateRange[1];
                }
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/info/asset/in_library_list`,
                    method: 'post',
                    data
                });
            },
            // 资产信息管理-在库资产导出
            exportInLibraryList(data) {
                if (
                    data.inWarehouseDateRange &&
                    data.inWarehouseDateRange.length
                ) {
                    data.intoStartDate = data.inWarehouseDateRange[0];
                    data.intoEndDate = data.inWarehouseDateRange[1];
                }
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/info/asset/download_library_list`,
                    method: 'post',
                    responseType: 'blob',
                    timeout: 30000,
                    data
                });
            },
            // 资产信息管理-查询资产信息操作记录列表
            getOperationRecordList(data) {
                if (data.dateRange && data.dateRange.length) {
                    const [startDate, toEndDate] = data.dateRange;
                    Object.assign(data, {
                        startDate,
                        toEndDate
                    });
                }
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/info/asset/operation_record_list`,
                    method: 'post',
                    data
                });
            },
            // 获取资产出入库统计记录
            getStatisticalTotalList(warehouseCode = null) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/info/asset/statistical_total_list`,
                    method: 'get',
                    params: {
                        warehouseCode
                    }
                });
            },
            // 获取单个资产操作记录详情
            getOperationRecordDetails(assetCode = '', customerAssetCode = '') {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/info/asset/operation_record_Details`,
                    method: 'get',
                    params: {
                        assetCode,
                        customerAssetCode
                    }
                });
            }
        }
    };

    return service;
};
