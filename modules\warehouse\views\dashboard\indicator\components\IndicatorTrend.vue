<template>
    <el-card class="snbc-card padding-top-5">
        <div slot="header" class="snbc-card-header">
            <span>核心指标历史趋势</span>
            <router-link to="/app/dashboard/indicator-query">
                <el-button class="padding-right-0" type="text">
                    更多&gt;&gt;
                </el-button>
            </router-link>
        </div>
        <ChartFeeAndIncome ref="ChartFeeAndIncomeRef" class="margin-top-10" />
        <ChartNumberAndFlow ref="ChartNumberAndFlowRef" class="margin-top-10" />
        <ChartMatchRate ref="ChartMatchRateRef" class="margin-top-10" />
        <ChartComplianceRate
            ref="ChartComplianceRateRef"
            class="margin-top-10"
        />
        <ChartAreaUtilizationRate
            ref="ChartAreaUtilizationRateRef"
            class="margin-top-10"
        />
    </el-card>
</template>
<script>
import ChartFeeAndIncome from './ChartFeeAndIncome.vue';
import ChartNumberAndFlow from './ChartNumberAndFlow.vue';
import ChartMatchRate from './ChartMatchRate.vue';
import ChartComplianceRate from './ChartComplianceRate.vue';
import ChartAreaUtilizationRate from './ChartAreaUtilizationRate.vue';

export default {
    name: 'IndicatorTrend',
    components: {
        ChartFeeAndIncome,
        ChartNumberAndFlow,
        ChartMatchRate,
        ChartComplianceRate,
        ChartAreaUtilizationRate
    },
    data() {
        return {};
    },
    methods: {
        // 数据查询
        async queryData() {
            this.getFinancialChart();
            this.getFlowChart();
            this.getInventoryAccuracyChart();
            this.getComplianceRateChart();
            this.getSpaceUtilizationChart();
        },
        // 区仓支出和收入
        async getFinancialChart() {
            const { getFinancialChart } =
                this.$service.warehouse.board_home_page;
            this.$refs.ChartFeeAndIncomeRef.chart.showLoading();
            try {
                const res = await getFinancialChart();
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                const result = res.result || [];
                this.$refs.ChartFeeAndIncomeRef.drawChart(result);
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            } finally {
                this.$refs.ChartFeeAndIncomeRef.chart.hideLoading();
            }
        },
        // 区仓数量和流量
        async getFlowChart() {
            const { getFlowChart } = this.$service.warehouse.board_home_page;
            this.$refs.ChartNumberAndFlowRef.chart.showLoading();
            try {
                const res = await getFlowChart();
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                const result = res.result || [];
                this.$refs.ChartNumberAndFlowRef.drawChart(result);
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            } finally {
                this.$refs.ChartNumberAndFlowRef.chart.hideLoading();
            }
        },
        // 货账相符率
        async getInventoryAccuracyChart() {
            const { getInventoryAccuracyChart } =
                this.$service.warehouse.board_home_page;
            this.$refs.ChartMatchRateRef.chart.showLoading();
            try {
                const res = await getInventoryAccuracyChart();
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                const result = res.result || [];
                this.$refs.ChartMatchRateRef.drawChart(result);
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            } finally {
                this.$refs.ChartMatchRateRef.chart.hideLoading();
            }
        },
        // 出入库及时率
        async getComplianceRateChart() {
            const { getComplianceRateChart } =
                this.$service.warehouse.board_home_page;
            this.$refs.ChartComplianceRateRef.chart.showLoading();
            try {
                const res = await getComplianceRateChart();
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                const result = res.result || [];
                this.$refs.ChartComplianceRateRef.drawChart(result);
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            } finally {
                this.$refs.ChartComplianceRateRef.chart.hideLoading();
            }
        },
        // 面积利用率
        async getSpaceUtilizationChart() {
            const { getSpaceUtilizationChart } =
                this.$service.warehouse.board_home_page;
            this.$refs.ChartAreaUtilizationRateRef.chart.showLoading();
            try {
                const res = await getSpaceUtilizationChart();
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                const result = res.result || [];
                this.$refs.ChartAreaUtilizationRateRef.drawChart(result);
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            } finally {
                this.$refs.ChartAreaUtilizationRateRef.chart.hideLoading();
            }
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .el-card__body {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}
</style>
