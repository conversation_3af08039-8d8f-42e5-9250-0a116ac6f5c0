/// Mixin to customize scrollbars
/// Beware, this does not work in all browsers
/// <AUTHOR>
/// @param {Length} $size - Horizontal scrollbar's height and vertical scrollbar's width
/// @param {Color} $foreground-color - Scrollbar's color
/// @param {Color} $background-color [mix($foreground-color, white, 50%)] - Scrollbar's color
/// @example scss - Scrollbar styling
///   @include scrollbars(.5em, slategray);
@mixin scrollbars($size, $foreground-color, $background-color: mix($foreground-color, white,  50%)) {
  // For Google Chrome
  ::-webkit-scrollbar {
      width:  $size;
      height: $size;
  }

  ::-webkit-scrollbar-thumb {
      background: $foreground-color;
      border-radius: $size;
  }

  ::-webkit-scrollbar-track {
      background: $background-color;
    //   border-radius: $size;
  }

//   ::-webkit-scrollbar-button{
//       height: 10px;
//       background: $foreground-color;
//   }

  // For Firefox
  * {
    scrollbar-color: $foreground-color $background-color; /* 滑块颜色  滚动条背景颜色 */
    scrollbar-width: thin; /* 滚动条宽度有三种：thin、auto、none */
  }

  // For Internet Explorer
  body {
    scrollbar-face-color: $foreground-color;
    scrollbar-track-color: $background-color;
  }
}
