/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();
    return {
        inventoryManagement: {
            test(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/xxxxxx',
                    method: 'post',
                    data
                });
            },
            /**
             * 获取盘点列表
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            getInventoryManageList(data) {
                if (data.endTime) {
                    data.endTime = `${data.endTime.substring(0, 11)}23:59:59`;
                }
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/inventory/plan_and_task/search_plan_list_by_condition',
                    method: 'post',
                    data
                });
            },

            getInventoryCountList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/inventory/plan_and_task/xxxxx',
                    method: 'post',
                    data
                });
            },
            /**
             * 获取盘点任务细节
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            getResultDetail(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/inventory/result_and_handle/search_result_detail`,
                    method: 'get',
                    params: {
                        taskCode: data
                    }
                });
            },
            /**
             * 获取盘点任务列表
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            getInventoryTaskList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/inventory/plan_and_task/search_task_list_by_condition',
                    method: 'post',
                    data
                });
            },
            /**
             * 编辑盘点
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            editInventoryManagement(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/inventory/plan_and_task/recreate_plan_and_task',
                    method: 'post',
                    data
                });
            },
            /**
             * 获取盘点详情
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            getInventoryDetailInfo(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/inventory/plan_and_task/search_plan_message_by_id`,
                    method: 'get',
                    params: {
                        id: data
                    }
                });
            },
            /**
             * 新增盘点
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            addInventoryManagement(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/inventory/plan_and_task/create_plan_and_task',
                    method: 'post',
                    data
                });
            },
            /**
             * 开始取消盘点
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            startAndCancleInventoryManagement(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/inventory/plan_and_task/start_or_cancel_plan',
                    method: 'post',
                    data
                });
            },
            getInventoryCount(data) {
                return new Promise((resolve) => {
                    setTimeout(() => {
                        resolve({
                            code: '000000',
                            message: '成功',
                            result: {
                                total: 2,
                                list: [
                                    {
                                        id: '1',
                                        planOrder: 'GSFH202304120001',
                                        warehouseName: '威海仓',
                                        taskOrder: 'GSFH20230412000516951',
                                        materialCode: '841561518956',
                                        productName: '好产品',
                                        inventoryStartTime: '2023-04-29',
                                        inventoryEndTime: '2023-05-10',
                                        inventoryState: '进行中',
                                        totalAssets: 4,
                                        haveCompleteNumber: 2,
                                        exceptionTaskNumber: 1
                                    },
                                    {
                                        id: '2',
                                        planOrder: 'GSFH202304120001',
                                        warehouseName: '威海仓',
                                        taskOrder: 'GSFH20230412000156154',
                                        materialCode: '841561518956',
                                        productName: '好产品',
                                        inventoryStartTime: '2023-04-29',
                                        inventoryEndTime: '2023-05-10',
                                        inventoryState: '进行中',
                                        totalAssets: 4,
                                        haveCompleteNumber: 2,
                                        exceptionTaskNumber: 1
                                    }
                                ],
                                pageNum: 1,
                                pageSize: 10,
                                size: 1,
                                startRow: 1,
                                endRow: 1,
                                pages: 1,
                                prePage: 0,
                                nextPage: 0,
                                isFirstPage: true,
                                isLastPage: true,
                                hasPreviousPage: false,
                                hasNextPage: false,
                                navigatePages: 8,
                                navigatepageNums: [1],
                                navigateFirstPage: 1,
                                navigateLastPage: 1
                            }
                        });
                    });
                });
            },
            /**
             * 给结果增加备注
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            getInventoryResult(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/inventory/result_and_handle/search_result_list',
                    method: 'post',
                    data
                });
            },
            /**
             *
             *
             * @returns {Promise} 返回值
             */
            getProductList() {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/basedata/product_info/search_all_product',
                    method: 'post',
                    data: {}
                });
            },
            /**
             * 给结果增加备注
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            addRemarkForResult(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/inventory/result_and_handle/update_confirm_state',
                    method: 'post',
                    data
                });
            },
            /**
             * 获取异常信息
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            getExceptionData(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/inventory/result_and_handle/search_except_asset',
                    method: 'post',
                    data
                });
            },
            /**
             * 获取异常剩余页签信息
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            getHandledData(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/inventory/result_and_handle/handle_result',
                    method: 'post',
                    data
                });
            },
            /**
             * 获取异常剩余页签信息
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            dealExceptionData(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/inventory/result_and_handle/create_entry_exit',
                    method: 'post',
                    data
                });
            },
            /**
             * 获取自动匹配结果的数据
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            getMatchAsset(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/inventory/result_and_handle/search_match_asset',
                    method: 'post',
                    data
                });
            },
            /**
             * 处理匹配的盘亏盘盈数据
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            handleMatchAsset(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/inventory/result_and_handle/handle_match_asset',
                    method: 'post',
                    data
                });
            },
            /**
             * 匹配无源合并
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            serchLostData(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/inventory/result_and_handle/search_loss_by_task_code',
                    method: 'post',
                    data
                });
            },
            /**
             * 新旧资产编码绑定
             * @param {Object} data 参数名称
             * @returns {Promise} 返回值
             */
            bindNotFoundAsset(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/inventory/result_and_handle/bind_not_found_asset',
                    method: 'post',
                    data
                });
            }
        }
    };
};
