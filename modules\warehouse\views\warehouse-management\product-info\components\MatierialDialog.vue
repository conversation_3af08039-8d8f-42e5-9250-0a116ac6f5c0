<template>
    <SnbcBaseDialog ref="snbcBaseDialogDialog" :config="dialogConfig">
        <template slot="dialog-body">
            <SnbcBaseTable ref="tableRef" :table-config="tableConfig" />
            <AddMaterialDialogVue @handleQuery="handleQuery" ref="addMaterialDialogRef"></AddMaterialDialogVue>
        </template>
    </SnbcBaseDialog>
</template>
<script>
import SnbcBaseDialog from 'warehouse/components/snbc-dialog/SnbcBaseDialog.vue';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import AddMaterialDialogVue from './AddMaterialDialog.vue';

export default {
    name: 'MatierialDialog',
    components: {
        SnbcBaseDialog,
        SnbcBaseTable,
        AddMaterialDialogVue
    },
    data() {
        const { getMaterialsList: listApi } = this.$service.warehouse.product;
        return {
            tableConfig: {
                queryApi: listApi,
                elTableColumns: [
                    {
                        label: '序号',
                        prop: 'index',
                        show: true,
                        elTableColumnAttrs: {
                            width: 80
                        }
                    },
                    {
                        label: '物料编码',
                        prop: 'materialsCode',
                        show: true,
                        elTableColumnAttrs: {
                            width: 160
                        }
                    },
                    {
                        label: '物料描述',
                        prop: 'materialRemark',
                        show: true,
                        minWidth: 120
                    }
                ],
                operations: [
                    {
                        name: '解绑',
                        type: 'primary',
                        handleClick: this.handleUnband
                    }
                ],
                headerButtons: [
                    {
                        name: '新增关联物料',
                        type: 'primary',
                        elButtonAttrs: {
                            icon: 'el-icon-plus'
                        },
                        handleClick: this.handleAdd
                    }
                ],
                hooks: {
                    queryParamsHook: (params) => {
                        params.productId = this.productId;
                    }
                }
            },
            dialogConfig: Object.freeze({
                elDialogAttrs: {
                    title: '关联物料列表',
                    width: '800px'
                },
                hasFooter: false
            }),
            productId: ''
        };
    },
    methods: {
        showDialog(row) {
            this.productId = row.id;
            this.$refs.snbcBaseDialogDialog.openDialog();
            this.$nextTick(() => {
                this.$refs.tableRef.list = [];
                this.handleQuery();
            });
        },
        handleQuery() {
            this.$refs.tableRef.handleQuery();
        },
        // add a new relationship between product and material
        handleAdd() {
            this.$refs.addMaterialDialogRef.showDialog({ id: this.productId });
        },
        // dissolve a old relationship between product and material
        async handleUnband(row) {
            await this.$tools.confirm(`解绑之后，历史资产数据不会更改，如果想更改，请联系项目组。`);
            const { code, message } = await this.$service.warehouse.product.unbindMaterialToProduct({
                productId: this.productId,
                materialCode: row.materialsCode
            });
            if (code !== '000000') {
                this.$message.error(message);
                return;
            }
            this.$message.success('解绑成功');
            this.handleQuery();
            this.$emit('queryList');
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .header__title {
    visibility: hidden;
}

::v-deep .popover {
    display: none;
}
</style>
