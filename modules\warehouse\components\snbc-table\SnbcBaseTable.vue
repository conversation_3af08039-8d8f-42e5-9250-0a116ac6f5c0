<template>
    <div class="content query-label-line2">
        <snbc-table-query
            v-if="showTableQuery"
            :query-params="tableConfig.queryParams"
            :query-config="tableConfig.queryConfig"
            @query="handleQuery"
            @reset="handleReset"
            @seniorQuery="handleSeniorQuery"
        />
        <slot name="table-notice" />
        <snbc-table-header
            :config="{
                headerButtons: tableConfig.headerButtons,
                headerTitle: tableConfig.headerTitle,
                importButtons: tableConfig.importButtons
            }"
            :selections="selections"
            :columns="tableConfig.elTableColumns"
        />
        <slot name="tabs" />
        <slot name="table-info-top" />
        <snbc-table-list
            :config="tableConfig"
            :list="list"
            :selections="selections"
            @selection-change="handleSelectionChange"
            @sort-change="handleSortChange"
        />
        <template v-if="hasPage">
            <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="tableConfig.pageParams.page"
                :limit.sync="tableConfig.pageParams.size"
                @pagination="queryList"
            />
        </template>
    </div>
</template>
<script>
import objectFactory from 'warehouse/common/object-factory/index.js';
import SnbcTableQuery from 'warehouse/components/snbc-table/SnbcTableQuery.vue';
import SnbcTableHeader from 'warehouse/components/snbc-table/SnbcTableHeader.vue';
import SnbcTableList from 'warehouse/components/snbc-table/SnbcTableList.vue';

export default {
    name: 'SnbcBaseTable',
    components: {
        SnbcTableQuery,
        SnbcTableHeader,
        SnbcTableList
    },
    props: {
        /**
         * 表格相关配置项
         */
        tableConfig: {
            type: Object,
            default() {
                return {
                    // 查询参数对象
                    queryParams: {},
                    // 分页查询参数
                    pageParams: objectFactory('pageParams'),
                    // 排序参数
                    sortParams: objectFactory('sortParams'),
                    // 查询区域表单配置
                    queryConfig: {
                        items: []
                    },
                    // 查询api
                    queryApi: null,
                    // 列配置
                    elTableColumns: [],
                    // 行操作
                    operations: [],
                    // 表格头部按钮
                    headerButtons: [],
                    // 启用列选择
                    selectionAble: true
                };
            }
        }
    },
    data() {
        return {
            // 列表是否已查询
            queried: false,
            total: 0,
            list: [],
            // 多选项
            selections: []
        };
    },
    computed: {
        // 查询区域展示控制
        showTableQuery() {
            return this.tableConfig?.queryConfig?.items?.length > 0;
        },
        // 是否展示分页
        hasPage() {
            return this.tableConfig?.hasPage !== false;
        }
    },
    created() {
        // 设置分页参数
        if (!this.tableConfig.pageParams) {
            this.tableConfig.pageParams = objectFactory('pageParams');
        }
        // 设置排序参数
        if (!this.tableConfig.sortParams) {
            this.tableConfig.sortParams = objectFactory('sortParams');
        }
        // 默认排序
        const { sortKey, sortOrder } = this.tableConfig.sortParams;
        if (sortKey && sortOrder) {
            if (!this.tableConfig.elTableAttrs) {
                this.tableConfig.elTableAttrs = {};
            }
            this.tableConfig.elTableAttrs.defaultSort = {
                prop: sortKey,
                order: sortOrder
            };
        }
    },
    methods: {
        // 查询操作，第一页查询
        handleQuery() {
            this.$set(this.tableConfig.pageParams, 'page', 1);
            this.queryList();
            this.$emit('query');
        },
        // 重置参数操作
        handleReset() {
            for (const key in this.tableConfig.queryParams) {
                if (Object.hasOwnProperty.call(this.tableConfig.queryParams, key)) {
                    // 默认值设置赋值
                    const configItem = this.tableConfig.queryConfig.items.find((item) => item.modelKey === key);
                    if (configItem && configItem.defaultValue !== undefined) {
                        this.tableConfig.queryParams[key] = configItem.defaultValue;
                    } else {
                        this.resetValueByKey(key);
                    }
                }
            }
            this.handleQuery();
        },
        // 列表数据查询
        async queryList() {
            const params = {
                ...this.tableConfig.queryParams,
                ...this.tableConfig.pageParams,
                ...this.tableConfig.sortParams
            };
            // 列表查询参数hook处理
            if (this.tableConfig?.hooks?.queryParamsHook) {
                this.tableConfig.hooks.queryParamsHook(params);
            }
            try {
                const res = await this.tableConfig.queryApi(params);
                const { code, result, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                const { list, total, startRow } = result;
                // 列表数据hook处理
                if (this.tableConfig?.hooks?.tableListHook) {
                    this.tableConfig.hooks.tableListHook(list, total);
                }
                // 添加序号
                list.map((item, index) => {
                    item.index = startRow + index;
                    return item;
                });
                this.list = list;
                this.total = total;
                this.queried = true;
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 当选择项发生变化时触发
        handleSelectionChange(selections) {
            this.tableConfig.handleSelectionChange && this.tableConfig.handleSelectionChange(selections);
            this.selections = selections;
        },
        // 排序条件发生变化
        handleSortChange(params) {
            const { column, prop: sortKey, order: sortOrder } = params;
            if (column.sortable === 'custom' && sortOrder) {
                this.$set(this.tableConfig.sortParams, 'sortKey', sortKey);
                this.$set(this.tableConfig.sortParams, 'sortOrder', sortOrder);
            } else {
                this.$set(this.tableConfig.sortParams, 'sortKey', '');
                this.$set(this.tableConfig.sortParams, 'sortOrder', '');
            }
            if (column.sortable === 'custom') {
                this.handleQuery();
            }
        },
        // 高级查询处理 参数
        handleSeniorQuery(type) {
            if (type) return;
            const keys = this.tableConfig.queryConfig.items.map((item) => item.modelKey).slice(3);

            for (const key of keys) {
                this.resetValueByKey(key);
            }
        },
        // 重置 查询数据
        resetValueByKey(key) {
            const resetValMap = {
                '[object Undefined]': undefined,
                '[object Number]': undefined,
                '[object String]': '',
                '[object Array]': []
            };
            const paramType = Object.prototype.toString.call(this.tableConfig.queryParams[key]);
            let resetVal = resetValMap[paramType];
            if (!Object.keys(resetValMap).includes(paramType)) {
                resetVal = null;
            }
            this.$set(this.tableConfig.queryParams, key, resetVal);
        }
    }
};
</script>
<style lang="scss" scoped>
.content {
    min-height: 100%;
    overflow-x: hidden;
    ::v-deep .pagination-container {
        padding: 8px 16px;
        margin-top: 0;
        .el-pagination {
            margin-top: 8px;
        }
    }
}
</style>
