<template>
    <el-dialog
        class="custom-dialog query-label-line2"
        :visible.sync="dialogVisible"
        v-bind="elDialogAttrs"
    >
        <snbc-tabs :tabs="tabsConfig">
            <template #1>
                <snbc-form ref="snbcFormRef" :form="form" :config="formConfig">
                    <template #form-body>
                        <template v-for="(item, index) in formItems">
                            <snbc-form-item
                                v-if="
                                    item.modelKey !== 'responsibleCode' &&
                                    (item.modelKey !== 'damageTaskCode' ||
                                        mode != 'add') &&
                                    (item.modelKey !== 'associatedTaskCode' ||
                                        form.associatedTaskType !=
                                            '日常工作') &&
                                    (mode !== 'edit' ||
                                        item.modelKey !== 'content')
                                "
                                :prop="item.modelKey"
                                :key="index"
                                :config="item"
                            />
                            <snbc-form-item
                                v-if="item.modelKey === 'responsibleCode'"
                                ref="responsibleCodeRef"
                                :prop="item.modelKey"
                                :key="index"
                                :config="responsibleCodeSelect"
                            >
                            </snbc-form-item>
                        </template>
                    </template>
                </snbc-form>
            </template>
            <template #2>
                <el-timeline>
                    <el-timeline-item
                        v-for="item in form.damageTaskLogs"
                        :key="item.key"
                        :timestamp="item.operateTime"
                        :hide-timestamp="true"
                        color="#409eff"
                    >
                        <el-card>
                            <p>时间：{{ item.operateTime }}</p>
                            <p>
                                操作：{{ item.operateType }}-{{
                                    item.operateState
                                }}
                            </p>
                            <p>操作人：{{ item.operateUserName }}</p>
                            <p>内容：{{ item.content }}</p>
                        </el-card>
                    </el-timeline-item>
                </el-timeline>
                <div class="no-data" v-if="!form.damageTaskLogs.length">
                    暂无数据
                </div>
            </template>
        </snbc-tabs>
        <span v-if="mode === 'edit'" slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleSave" v-if="!submitFlag"
                >保 存</el-button
            >
            <el-button type="primary" @click="handleSubmit" v-if="submitFlag"
                >提 交</el-button
            >
        </span>
        <span v-if="mode === 'audit'" slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handlePass">通 过</el-button>
            <el-button type="warning" @click="handleReject">驳 回</el-button>
        </span>
    </el-dialog>
</template>

<script>
import SnbcTabs from 'warehouse/components/snbc-tabs/SnbcTabs.vue';
import SnbcFormItem from 'warehouse/components/snbc-form/SnbcFormItem.vue';
import SnbcForm from 'warehouse/components/snbc-form/SnbcForm.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import Supplier from 'warehouse/common/form-items/supplier.js';
import FormRules from 'warehouse/common/form-rules/index.js';
import ElAttrs from 'warehouse/common/el-attrs/index.js';

const { inputRequired, maxLength, selectRequired, fileRequired, dateRequired } =
    FormRules;

const { input, select, date, textarea, file, number } = FormItems;

const { responsibleCodeSelect } = Supplier;

const { elDialogAttrs } = ElAttrs;

const damageTaskCode = {
    ...input,
    name: '货损任务编号',
    modelKey: 'damageTaskCode',
    elInputAttrs: {
        readonly: true
    }
};

const damageType = {
    ...select,
    name: '货损类型',
    modelKey: 'damageType',
    elOptions: [
        { label: '损毁 ', value: '损毁' },
        { label: '丢失', value: '丢失' },
        { label: '货损维修 ', value: '货损维修' }
    ],
    elSelectAttrs: {}
};

const occurrenceTime = {
    ...date,
    name: '发生时间',
    modelKey: 'occurrenceTime',
    elDatePickerAttrs: {
        type: 'datetime'
    }
};
const finishTime = {
    ...date,
    name: '完成时间',
    modelKey: 'finishTime',
    elDatePickerAttrs: {
        type: 'datetime'
    }
};
const accountability = {
    ...select,
    name: '责任归属',
    modelKey: 'accountability',
    elOptions: [
        { label: '物流供应商', value: '物流供应商' },
        { label: '安装供应商', value: '安装供应商' },
        { label: '区仓供应商', value: '区仓供应商' }
    ],
    elSelectAttrs: {}
};

const disposal = {
    ...select,
    name: '处置方式',
    modelKey: 'disposal',
    elOptions: [
        { label: '等价赔偿', value: '等价赔偿' },
        { label: '自行承担', value: '自行承担' },
        { label: '自维修', value: '自维修' },
        { label: '委外维修', value: '委外维修' }
    ],
    elSelectAttrs: {}
};
const compensationAmount = {
    ...number,
    name: '金额',
    modelKey: 'compensationAmount',
    elInputNumberAttrs: {
        precision: 2,
        step: 0.01,
        max: *********,
        min: 0
    }
};
const remark = {
    ...textarea,
    name: '备注',
    modelKey: 'remark',
    elInputAttrs: {
        disabled: true
    }
};
const applyReason = {
    ...textarea,
    name: '申请原因',
    modelKey: 'applyReason',
    elInputAttrs: {}
};
const warehouseName = {
    ...input,
    name: '区仓名称',
    modelKey: 'warehouseName',
    elInputAttrs: {
        disabled: true
    }
};
const assetCode = {
    ...input,
    name: '产品序列号',
    modelKey: 'assetCode',
    elInputAttrs: {}
};
const productName = {
    ...input,
    name: '产品名称',
    modelKey: 'productName',
    elInputAttrs: {}
};
const associatedTaskType = {
    ...select,
    name: '关联任务类型',
    modelKey: 'associatedTaskType',
    elOptions: [
        { label: '公司发货 ', value: '公司发货' },
        { label: '盘点提案处理', value: '盘点提案处理' },
        { label: '调拨', value: '调拨' },
        { label: '日常工作', value: '日常工作' }
    ],
    elSelectAttrs: {
        disabled: true
    }
};
const pictureUrl = {
    ...file,
    name: '证据和提案照片',
    modelKey: 'pictureUrl',
    dataType: 'string',
    elUploadAttrs: {
        limit: 5
    }
};
const associatedTaskCode = {
    ...input,
    name: '关联任务编号',
    modelKey: 'associatedTaskCode',
    elInputAttrs: {
        disabled: true
    }
};
const content = {
    ...textarea,
    name: '审核内容',
    modelKey: 'content'
};
const rules = {
    applyReason: [inputRequired('申请原因'), maxLength(500)],
    content: [inputRequired('审核内容'), maxLength(500)],
    damageTaskCode: [inputRequired('任务编码'), maxLength(32)],
    warehouseCode: [inputRequired('区仓名称'), maxLength(16)],
    damageType: [selectRequired('货损类型')],
    associatedTaskType: [selectRequired('关联任务类型')],
    assetCode: [inputRequired('产品序列号'), maxLength(64)],
    productName: [inputRequired('产品名称'), maxLength(64)],
    associatedTaskCode: [inputRequired('关联任务编码'), maxLength(32)],
    pictureUrl: [fileRequired('证据和提案照片'), maxLength(2000)],
    occurrenceTime: [dateRequired('发生时间')],
    finishTime: [dateRequired('完成时间')],
    accountability: [selectRequired('责任归属'), maxLength(32)],
    responsibleCode: [inputRequired('责任方'), maxLength(32)],
    disposal: [selectRequired('处置方式')],
    compensationAmount: [inputRequired('金额')]
};

export default {
    name: 'DamageTaskInfo',
    components: {
        SnbcForm,
        SnbcFormItem,
        SnbcTabs
    },
    data() {
        return {
            submitFlag: false,
            options: [],
            taskDetails: 1,
            // 标题 view | edit
            title: '',
            // 弹窗模式 view | edit
            mode: '',
            // 弹窗显示
            dialogVisible: false,
            elDialogAttrs: {},
            form: {},
            formConfig: {
                elFormAttrs: {
                    rules
                }
            },
            // 标签页配置
            tabsConfig: [
                {
                    id: 1,
                    label: '货损丢失任务信息'
                },
                {
                    id: 2,
                    label: '执行日志'
                }
            ]
        };
    },
    computed: {
        formItems() {
            return [
                damageTaskCode,
                warehouseName,
                associatedTaskType,
                associatedTaskCode,
                damageType,
                assetCode,
                productName,
                pictureUrl,
                occurrenceTime,
                finishTime,
                accountability,
                responsibleCodeSelect,
                disposal,
                compensationAmount,
                remark,
                applyReason,
                content
            ].map((item) => {
                if (
                    [
                        'damageTaskCode',
                        'associatedTaskCode',
                        'assetCode',
                        'productName',
                        'applyReason'
                    ].includes(item.modelKey)
                ) {
                    item.elInputAttrs.disabled = this.mode === 'audit';
                }
                if (
                    ['damageType', 'accountability', 'disposal'].includes(
                        item.modelKey
                    )
                ) {
                    item.elSelectAttrs.disabled = this.mode === 'audit';
                }
                if (['responsibleCode'].includes(item.modelKey)) {
                    responsibleCodeSelect.elSelectAttrs.disabled =
                        this.mode === 'audit';
                }
                if (['occurrenceTime', 'finishTime'].includes(item.modelKey)) {
                    item.elDatePickerAttrs.disabled = this.mode === 'audit';
                }

                if (['pictureUrl'].includes(item.modelKey)) {
                    item.elUploadAttrs.disabled = this.mode === 'audit';
                }
                if (['compensationAmount'].includes(item.modelKey)) {
                    item.elInputNumberAttrs.disabled = this.mode === 'audit';
                }

                return {
                    modelObj: this.form,
                    elFormItemAttrs: {
                        label: item.name,
                        ...(item.elFormItemAttrs || {})
                    },
                    ...item
                };
            });
        },
        responsibleCodeSelect() {
            return {
                modelObj: this.form,
                elFormItemAttrs: {
                    label: responsibleCodeSelect.name,
                    ...(responsibleCodeSelect.elFormItemAttrs || {})
                },
                ...responsibleCodeSelect
            };
        }
    },
    watch: {
        'form.accountability': function (newVal, oldVal) {
            if (newVal !== oldVal) {
                // 清空责任方
                this.$refs.responsibleCodeRef[0].$children[0].clearValue();
                if (newVal === '区仓供应商') {
                    return this.$refs.responsibleCodeRef[0].$children[0].queryList(
                        {
                            isWarehouse: 1
                        }
                    );
                }
                if (newVal === '物流供应商') {
                    return this.$refs.responsibleCodeRef[0].$children[0].queryList(
                        {
                            isLogistics: 1
                        }
                    );
                }
                if (newVal === '安装供应商') {
                    return this.$refs.responsibleCodeRef[0].$children[0].queryList(
                        {
                            isInstaller: 1
                        }
                    );
                }
            }
        }
    },
    methods: {
        openDialog(row, mode, submitFlag) {
            this.submitFlag = false;
            this.submitFlag = submitFlag;
            this.mode = mode;
            if (this.mode === 'edit') {
                this.title = '编辑资产货损任务';
            }
            if (this.mode === 'view') {
                this.title = '查看资产货损任务';
            }
            if (this.mode === 'audit') {
                this.title = '审核资产货损任务';
            }
            this.form = row;
            this.elDialogAttrs = {
                ...elDialogAttrs,
                title: this.title
            };
            this.dialogVisible = true;
        },
        hideDialog() {
            this.dialogVisible = false;
        },
        // 提交操作
        async handleSubmit() {
            await this.$tools.validateForm(this.$refs.snbcFormRef.getFormRef());
            await this.$tools.confirm('确认提交审核吗？');
            try {
                // 默认名称和编号相同
                this.form.damageTaskName = this.form.damageTaskCode;
                this.form.taskState = '审核中';
                const res = await this.$service.warehouse.damageTask.edit(
                    this.form
                );
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('提交成功');
                this.$parent.$refs.tableRef1.queryList();
                this.hideDialog();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 编辑保存
        async handleSave() {
            // 默认名称和编号相同
            this.form.damageTaskName = this.form.damageTaskCode;
            this.form.taskState = '待提交';
            await this.$tools.validateForm(this.$refs.snbcFormRef.getFormRef());
            await this.$tools.confirm('确认保存吗？');
            try {
                const res = await this.$service.warehouse.damageTask.edit(
                    this.form
                );
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('保存成功');
                this.$parent.$refs.tableRef1.queryList();
                this.hideDialog();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 驳回
        async handleReject() {
            await this.$tools.validateForm(this.$refs.snbcFormRef.getFormRef());
            await this.$tools.confirm('确认驳回吗？');
            try {
                this.form.operateState = '驳回';
                const res = await this.$service.warehouse.damageTask.examine(
                    this.form
                );
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('驳回成功');
                this.$parent.$refs.tableRef1.queryList();
                this.hideDialog();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 通过
        async handlePass() {
            await this.$tools.validateForm(this.$refs.snbcFormRef.getFormRef());
            await this.$tools.confirm('确认通过吗？');
            try {
                this.form.operateState = '通过';
                const res = await this.$service.warehouse.damageTask.examine(
                    this.form
                );
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('通过成功');
                this.$parent.$refs.tableRef1.queryList();
                this.hideDialog();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 取消操作-隐藏弹窗
        handleCancel() {
            this.hideDialog();
        }
    }
};
</script>

<style lang="scss" scoped>
.no-data {
    line-height: 60px;
    text-align: center;
    color: #909399;
}
</style>
