<template>
    <el-dialog
        class="custom-dialog query-label-line2"
        :visible.sync="dialogVisible"
        v-bind="elDialogAttrs"
    >
        <snbc-tabs :tabs="tabsConfig">
            <template #1>
                <snbc-form ref="snbcFormRef" :form="form" :config="formConfig">
                    <template #form-body>
                        <template v-for="(item, index) in formItems">
                            <snbc-form-item
                                v-if="
                                    (item.modelKey !== 'applyCode' ||
                                        mode != 'add') &&
                                    (item.modelKey !== 'associatedTaskCode' ||
                                        form.associatedTaskType !=
                                            '日常工作') &&
                                    (mode !== 'edit' ||
                                        item.modelKey !== 'content')
                                "
                                :prop="item.modelKey"
                                :key="index"
                                :config="item"
                            />
                        </template>
                    </template>
                </snbc-form>
            </template>
            <template #2>
                <el-timeline>
                    <el-timeline-item
                        v-for="item in form.assetCorrectLogs"
                        :key="item.key"
                        :timestamp="item.operateTime"
                        :hide-timestamp="true"
                        color="#409eff"
                    >
                        <el-card>
                            <p>时间：{{ item.operateTime }}</p>
                            <p>
                                操作：{{ item.operateType }}-{{
                                    item.operateState
                                }}
                            </p>
                            <p>操作人：{{ item.operateUserName }}</p>
                            <p>内容：{{ item.operateContent }}</p>
                        </el-card>
                    </el-timeline-item>
                </el-timeline>
                <div class="no-data" v-if="!form.assetCorrectLogs.length">
                    暂无数据
                </div>
            </template>
        </snbc-tabs>
        <span v-if="mode === 'edit'" slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleSave" v-if="!submitFlag"
                >保 存</el-button
            >
            <el-button type="primary" @click="handleSubmit" v-if="submitFlag"
                >提 交</el-button
            >
        </span>
        <span v-if="mode === 'audit'" slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handlePass">通 过</el-button>
            <el-button type="warning" @click="handleReject">驳 回</el-button>
        </span>
    </el-dialog>
</template>

<script>
import SnbcTabs from 'warehouse/components/snbc-tabs/SnbcTabs.vue';
import SnbcFormItem from 'warehouse/components/snbc-form/SnbcFormItem.vue';
import SnbcForm from 'warehouse/components/snbc-form/SnbcForm.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import FormRules from 'warehouse/common/form-rules/index.js';
import ElAttrs from 'warehouse/common/el-attrs/index.js';

const { inputRequired, maxLength, selectRequired, dateRequired } = FormRules;

const { input, select, date, textarea, file } = FormItems;

const { elDialogAttrs } = ElAttrs;

const applyCode = {
    ...input,
    name: '非法数据修正编号',
    modelKey: 'applyCode',
    elInputAttrs: {
        readonly: true
    }
};
const applyName = {
    ...input,
    name: '非法数据修正名称',
    modelKey: 'applyName',
    elInputAttrs: {}
};
const warehouseName = {
    ...input,
    name: '区仓名称',
    modelKey: 'warehouseName',
    elInputAttrs: {
        readonly: true
    }
};
const operationType = {
    ...select,
    name: '操作类型',
    modelKey: 'operationType',
    elOptions: [
        { label: '入库 ', value: '入库' },
        { label: '出库', value: '出库' }
    ],
    elSelectAttrs: {
        disabled: true
    }
};
const operationTime = {
    ...date,
    name: '出入库时间',
    modelKey: 'operationTime',
    elDatePickerAttrs: {
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm:ss'
    }
};

const applyReason = {
    ...textarea,
    name: '申请原因',
    modelKey: 'applyReason',
    elInputAttrs: {}
};

const assetCode = {
    ...input,
    name: '产品序列号',
    modelKey: 'assetCode',
    elInputAttrs: { readonly: true }
};
const productName = {
    ...input,
    name: '产品名称',
    modelKey: 'productName',
    elInputAttrs: {
        readonly: true
    }
};
const associatedTaskType = {
    ...select,
    name: '关联任务类型',
    modelKey: 'associatedTaskType',
    elOptions: [
        { label: '公司发货 ', value: '公司发货' },
        { label: '盘点提案处理', value: '盘点提案处理' },
        { label: '调拨', value: '调拨' },
        { label: '日常工作', value: '日常工作' }
    ],
    elSelectAttrs: {}
};
const associatedTaskCode = {
    ...input,
    name: '关联任务编号',
    modelKey: 'associatedTaskCode',
    elInputAttrs: {
        disabled: true
    }
};
const pictureUrl = {
    ...file,
    name: '图片',
    modelKey: 'pictureUrl',
    dataType: 'string',
    elUploadAttrs: {
        limit: 5,
        disabled: true
    }
};
const content = {
    ...textarea,
    name: '审核内容',
    modelKey: 'content'
};
const rules = {
    applyName: [inputRequired('任务名称'), maxLength(64)],
    applyReason: [inputRequired('申请原因'), maxLength(500)],
    content: [inputRequired('审核内容'), maxLength(500)],
    applyCode: [inputRequired('任务编码'), maxLength(32)],
    warehouseCode: [inputRequired('区仓编码'), maxLength(16)],
    associatedTaskType: [selectRequired('关联任务类型')],
    operationType: [selectRequired('操作类型')],
    assetCode: [inputRequired('产品序列号'), maxLength(64)],
    productName: [inputRequired('产品名称'), maxLength(64)],
    associatedTaskCode: [inputRequired('关联任务编码'), maxLength(32)],
    operationTime: [dateRequired('出入库时间')]
};

export default {
    name: 'AssetCorrectForm',
    components: {
        SnbcForm,
        SnbcFormItem,
        SnbcTabs
    },
    data() {
        return {
            submitFlag: false,
            options: [],
            taskDetails: 1,
            // 标题 view | edit
            title: '',
            // 弹窗模式 view | edit
            mode: '',
            // 弹窗显示
            dialogVisible: false,
            elDialogAttrs: {},
            form: {},
            formConfig: {
                elFormAttrs: {
                    rules
                }
            },
            // 标签页配置
            tabsConfig: [
                {
                    id: 1,
                    label: '非法数据修正信息'
                },
                {
                    id: 2,
                    label: '执行日志'
                }
            ]
        };
    },
    computed: {
        formItems() {
            return [
                applyCode,
                applyName,
                warehouseName,
                associatedTaskType,
                associatedTaskCode,
                operationType,
                assetCode,
                productName,
                operationTime,
                applyReason,
                content,
                pictureUrl
            ].map((item) => {
                if (
                    [
                        'applyCode',
                        'applyName',
                        'associatedTaskCode',
                        'assetCode',
                        'productName',
                        'warehouseName',
                        'applyReason'
                    ].includes(item.modelKey)
                ) {
                    item.elInputAttrs.disabled = this.mode === 'audit';
                }
                if (['associatedTaskType'].includes(item.modelKey)) {
                    item.elSelectAttrs.disabled = this.mode === 'audit';
                }
                if (['operationTime'].includes(item.modelKey)) {
                    item.elDatePickerAttrs.disabled = this.mode === 'audit';
                }
                return {
                    modelObj: this.form,
                    elFormItemAttrs: {
                        label: item.name,
                        ...(item.elFormItemAttrs || {})
                    },
                    ...item
                };
            });
        }
    },
    methods: {
        openDialog(row, mode, submitFlag) {
            this.submitFlag = false;
            this.submitFlag = submitFlag;
            this.mode = mode;
            if (this.mode === 'edit') {
                this.title = '编辑非法数据修正';
            }
            if (this.mode === 'view') {
                this.title = '查看非法数据修正';
            }
            if (this.mode === 'audit') {
                this.title = '审核非法数据修正';
            }
            this.form = row;
            this.elDialogAttrs = {
                ...elDialogAttrs,
                title: this.title
            };
            this.dialogVisible = true;
        },
        hideDialog() {
            this.dialogVisible = false;
        },
        // 提交操作
        async handleSubmit() {
            await this.$tools.validateForm(this.$refs.snbcFormRef.getFormRef());
            await this.$tools.confirm('确认提交审核吗？');
            try {
                this.form.assetCorrectState = '审核中';
                const res = await this.$service.warehouse.assetCorrect.create(
                    this.form
                );
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('提交成功');
                this.$parent.$refs.tableRef1.queryList();
                this.hideDialog();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 编辑保存
        async handleSave() {
            this.form.assetCorrectState = '待提交';
            await this.$tools.validateForm(this.$refs.snbcFormRef.getFormRef());
            await this.$tools.confirm('确认保存吗？');
            try {
                const res = await this.$service.warehouse.assetCorrect.edit(
                    this.form
                );
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('保存成功');
                this.$parent.$refs.tableRef1.queryList();
                this.hideDialog();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 驳回
        async handleReject() {
            await this.$tools.validateForm(this.$refs.snbcFormRef.getFormRef());
            await this.$tools.confirm('确认驳回吗？');
            try {
                this.form.logDomain = {
                    operateState: '驳回',
                    operateType: '审核',
                    operateContent: this.form.content,
                    applyCode: this.form.applyCode
                };
                const res = await this.$service.warehouse.assetCorrect.examine(
                    this.form
                );
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('驳回成功');
                this.$parent.$refs.tableRef1.queryList();
                this.hideDialog();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 通过
        async handlePass() {
            await this.$tools.validateForm(this.$refs.snbcFormRef.getFormRef());
            await this.$tools.confirm('确认通过吗？');
            try {
                this.form.logDomain = {
                    operateState: '通过',
                    operateContent: this.form.content,
                    operateType: '审核',
                    applyCode: this.form.applyCode
                };
                this.form.logDomain.applyCode = this.form.applyCode;
                const res = await this.$service.warehouse.assetCorrect.examine(
                    this.form
                );
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('通过成功');
                this.$parent.$refs.tableRef1.queryList();
                this.hideDialog();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 取消操作-隐藏弹窗
        handleCancel() {
            this.hideDialog();
        }
    }
};
</script>

<style lang="scss" scoped>
.no-data {
    line-height: 60px;
    text-align: center;
    color: #909399;
}
</style>
