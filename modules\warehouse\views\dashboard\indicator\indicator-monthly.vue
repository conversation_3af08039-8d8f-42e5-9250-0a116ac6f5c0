<!-- 区仓指标月明细查询 -->
<template>
    <div class="view">
        <div class="content query-label-line2">
            <snbc-base-table ref="tableRef" :table-config="tableConfig" @query="handleQuery">
                <template #table-info-top>
                    <el-descriptions class="margin-bottom-10" border :column="8">
                        <el-descriptions-item
                            v-for="(item, index) in countList"
                            :key="index"
                            v-bind="$tools.elDescItemLabelAttrs(item.name)"
                        >
                            {{ item.value }}
                        </el-descriptions-item>
                    </el-descriptions>
                </template>
            </snbc-base-table>
        </div>
    </div>
</template>

<script>
import Vue from 'vue';
import moment from 'moment';
import functions from 'frame/mixins/functions.js';
import elAttrs from 'warehouse/common/el-attrs/index.js';
import textRender from 'warehouse/common/text-render/index.js';
import objectFactory from 'warehouse/common/object-factory/index.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import commonItems from 'warehouse/common/form-items/common-items.js';
import warehouseItems from 'warehouse/common/form-items/warehouse-items.js';

const { cloneDeep } = Vue.prototype.$tools;
// 查询参数
const queryParams = {
    warehouseCode: '',
    requestDate: [],
    orderRule: 'desc',
    orderMetric: 'traffic'
};
const pageParams = objectFactory('pageParams');

// 支持内存缓存的页面参数
const allParams = {
    queryParams: cloneDeep(queryParams),
    // 分页参数
    pageParams: cloneDeep(pageParams)
};

// 统计周期
const requestDate = {
    ...commonItems.dateRange,
    name: '统计周期',
    modelKey: 'requestDate',
    elDatePickerAttrs: {
        'type': 'monthrange',
        'unlink-panels': true,
        'value-format': 'yyyy-MM'
    }
};
// 查询区域配置项
const queryConfigItems = [
    warehouseItems.warehouseSelect,
    requestDate,
    warehouseItems.orderMetric,
    warehouseItems.orderRule
];
export default {
    name: 'IndicatorMonthly',
    components: {
        SnbcBaseTable
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            tableConfig: {
                queryParams: allParams.queryParams,
                pageParams: allParams.pageParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: this.$service.warehouse.statistics.getMonthlyList,
                // 导出操作
                headerButtons: [
                    {
                        ...elAttrs.exportBtnAttrs,
                        permissionCode: 'WAREHOUSE_INDICATOR_MONTHLY_EXPORT',
                        handleClick: this.handleExport
                    }
                ],
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 60 },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '建仓时间',
                        prop: 'createTime',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '供应商',
                        prop: 'supplier',
                        show: true,
                        minWidth: 200
                    },

                    {
                        label: '服务站区仓负责人',
                        prop: 'warehouseMaster',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '月份',
                        prop: 'statisticsDateFormat',
                        show: true,
                        minWidth: 140,
                        renderMode: 'button',
                        elButtonAttrs: {
                            type: 'text'
                        },
                        handleClick: this.indicatorDaily
                    },
                    {
                        label: '租赁面积（㎡）',
                        prop: 'warehouseRentArea',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '计费方式',
                        prop: 'billingMethod',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '单价（元）',
                        prop: 'unitPrice',
                        show: true,
                        minWidth: 100,
                        render(value) {
                            return textRender.unitRender(value, '');
                        }
                    },
                    {
                        label: '流量（台）',
                        prop: 'traffic',
                        show: true,
                        minWidth: 100,
                        render(value) {
                            return textRender.unitRender(value, '');
                        }
                    },
                    {
                        label: '支出（元）',
                        prop: 'monthlyCost',
                        show: true,
                        minWidth: 100,
                        render(value) {
                            return textRender.unitRender(value, '');
                        }
                    },
                    {
                        label: '已支付（元）',
                        prop: 'whetherPayment',
                        show: true,
                        minWidth: 100,
                        render(value) {
                            return textRender.unitRender(value, '');
                        }
                    },
                    {
                        label: '收入（元）',
                        prop: 'monthlyIncome',
                        show: true,
                        minWidth: 100,
                        render(value) {
                            return textRender.unitRender(value, '');
                        }
                    },
                    {
                        label: '吞吐量（台次）',
                        prop: 'throughput',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '平均库龄（天）',
                        prop: 'averageAge',
                        show: true,
                        minWidth: 100,
                        cellClassName: (column, row) => {
                            return this.$tools.getClassByCompareValue(this.countData.averageAge, row.averageAge);
                        }
                    },
                    {
                        label: '新机平均库龄（天）',
                        prop: 'averageAgeNew',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '撤机平均库龄（天）',
                        prop: 'averageAgeWithDraw',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '面积利用率',
                        prop: 'actualAreaUtilizationRate',
                        show: true,
                        minWidth: 100,
                        cellClassName: (column, row) => {
                            return this.$tools.getClassByCompareValue(
                                row.actualAreaUtilizationRate,
                                this.countData.actualAreaUtilizationRate
                            );
                        },
                        render(value) {
                            return textRender.unitRender(value, '%');
                        }
                    },
                    {
                        label: '出入库及时率',
                        prop: 'timelinessRate',
                        show: true,
                        minWidth: 120,
                        cellClassName: (column, row) => {
                            return this.$tools.getClassByCompareValue(
                                row.timelinessRate,
                                this.countData.timelinessRate
                            );
                        },
                        render(value) {
                            return textRender.unitRender(value, '%');
                        }
                    },
                    {
                        label: '货账相符率',
                        prop: 'accuracyRate',
                        show: true,
                        minWidth: 100,
                        cellClassName: (column, row) => {
                            return this.$tools.getClassByCompareValue(row.accuracyRate, this.countData.accuracyRate);
                        },
                        render(value) {
                            return textRender.unitRender(value, '%');
                        }
                    }
                ],
                hooks: {
                    tableListHook: this.tableListHook
                }
            },
            // 统计数据展示
            countList: [
                { name: '支出', value: '-' },
                { name: '收入', value: '-' },
                { name: '货账相符率', value: '-' },
                { name: '面积利用率', value: '-' },
                { name: '出入库及时率', value: '-' },
                { name: '平均库龄', value: '-' },
                { name: '累计流量', value: '-' }
            ],
            // 统计数据对象
            countData: {}
        };
    },
    beforeCreate() {
        if (!this.$store.getters.toggleView) {
            allParams.queryParams = cloneDeep(queryParams);
            allParams.pageParams = cloneDeep(pageParams);
        }
    },
    created() {
        // 非tag切换进入，重置页面参数至默认状态
        if (!this.$store.getters.toggleView) {
            this.$tools.setQueryParams.call(this);
        }
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.queryList();
        this.getMonthlyStatistics();
    },
    methods: {
        // 列表查询操作
        handleQuery() {
            this.getMonthlyStatistics();
        },
        // 列表数据处理
        tableListHook(list) {
            list.map((item) => {
                item.statisticsDateFormat = textRender.dateRender(item.statisticsDate, 'YYYY年MM月');
                return item;
            });
        },
        // 导出操作
        async handleExport() {
            const { exportMonthlyStatistics } = this.$service.warehouse.statistics;
            const params = this.$tools.cloneDeep(this.tableConfig.queryParams);
            const stream = await exportMonthlyStatistics(params);
            this.$tools.downloadExprotFile(stream, '区仓指标月明细', 'xlsx').catch((e) => {
                this.$tools.message.err(e || '导出失败');
            });
        },
        // 汇总数据
        async getMonthlyStatistics() {
            const { unitRender } = textRender;
            this.countData = {};
            // 设置具体值
            this.$tools.setIndicator(this.countList, {
                支出: unitRender('', '元'),
                收入: unitRender('', '元'),
                货账相符率: unitRender('', '%'),
                面积利用率: unitRender('', '%'),
                出入库及时率: unitRender('', '%'),
                平均库龄: unitRender('', '天'),
                累计流量: unitRender('', '台')
            });
            const { getMonthlyStatistics } = this.$service.warehouse.statistics;
            try {
                const res = await getMonthlyStatistics(this.tableConfig.queryParams);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.countData = res.result || {};
                const {
                    monthlyCost,
                    monthlyIncome,
                    accuracyRate,
                    actualAreaUtilizationRate,
                    timelinessRate,
                    averageAge,
                    monthlyTraffic
                } = this.countData;
                // 设置具体值
                this.$tools.setIndicator(this.countList, {
                    支出: unitRender(monthlyCost, '元'),
                    收入: unitRender(monthlyIncome, '元'),
                    货账相符率: unitRender(accuracyRate, '%'),
                    面积利用率: unitRender(actualAreaUtilizationRate, '%'),
                    出入库及时率: unitRender(timelinessRate, '%'),
                    平均库龄: unitRender(averageAge, '天'),
                    累计流量: unitRender(monthlyTraffic, '台')
                });
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 跳转日指标明细页面
        indicatorDaily(item) {
            const { warehouseCode, statisticsDate } = item;
            const date = moment(statisticsDate);
            const startDate = date.startOf('month').format('YYYY-MM-DD');
            const endDate = date.endOf('month').format('YYYY-MM-DD');
            this.$router.push({
                path: '/app/dashboard/indicator-daily',
                query: {
                    warehouseCode,
                    requestDate: [startDate, endDate]
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
