// 目前框架路由，只支持到二级路由
export default [
    {
        path: '/app/correct',
        name: 'correct',
        noPermission: true,
        useLayout: true,
        redirect: 'noRedirect',
        children: [
            {
                path: 'correct-management',
                component: () => import('warehouse/views/correct/management/index'),
                name: 'CorrectManagement',
                noPermission: true,
                meta: { title: '资产出入库非法数据修正', icon: 'fa fa-home' }
            }
        ]
    },
    {
        path: '/app/error-deal',
        name: 'damage-task',
        noPermission: true,
        useLayout: true,
        redirect: 'noRedirect',
        children: [
            {
                path: 'damage-task',
                component: () => import('warehouse/views/warehouse-error-deal/damagetask/index'),
                name: 'DamageTaskManagement',
                noPermission: true,
                meta: { title: '资产货损丢失申请管理', icon: 'fa fa-home' }
            },
            {
                path: 'sundry-entry-exit',
                component: () => import('warehouse/views/warehouse-error-deal/sundry-entry-exit/index'),
                name: 'SundryEntryExit',
                noPermission: true,
                meta: { title: '资产杂项出入库', icon: 'fa fa-home' }
            },
            {
                path: 'task-additional',
                component: () => import('warehouse/views/warehouse-error-deal/task-additional/index'),
                name: 'TaskAdditionalManagement',
                noPermission: true,
                meta: { title: '任务补录管理', icon: 'fa fa-home' }
            },
            {
                path: 'sundry-entry-exit-detail',
                component: () => import('warehouse/views/warehouse-error-deal/sundry-entry-exit/detail'),
                name: 'SundryEntryExitDetail',
                noPermission: true,
                meta: { title: '资产杂项出入库申请详情', icon: 'fa fa-home' }
            },
            {
                path: 'asset-correct',
                component: () => import('warehouse/views/warehouse-error-deal/asset-correct/index'),
                name: 'AssetCorrectManagement',
                noPermission: true,
                meta: { title: '非法数据修正管理', icon: 'fa fa-home' }
            }
        ]
    },
    {
        path: '/app/warehouse',
        name: 'warehouse-management',
        noPermission: true,
        useLayout: true,
        redirect: 'noRedirect',
        children: [
            {
                path: 'base-information',
                component: () => import('warehouse/views/warehouse-management/base-information/index'),
                name: 'WarehouseBaseInformation',
                noPermission: true,
                meta: { title: '区仓基础信息', icon: 'fa fa-home' }
            },
            {
                path: 'entry-and-exit-rules',
                component: () => import('warehouse/views/warehouse-management/entry-and-exit-rules/index'),
                name: 'WarehouseEntryAndExitRules',
                noPermission: true,
                meta: {
                    title: '区仓资产出入规则',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'inventory-restrict',
                component: () => import('warehouse/views/warehouse-management/inventory-restrict/index'),
                name: 'WarehouseInventoryRestrict',
                noPermission: true,
                meta: {
                    title: '区仓库存上下限',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'fee-rules',
                component: () => import('warehouse/views/warehouse-management/fee-rules/index'),
                name: 'WarehouseFeeRules',
                noPermission: true,
                meta: {
                    title: '区仓收费规则管理',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'product-info',
                component: () => import('warehouse/views/warehouse-management/product-info/index'),
                name: 'WarehouseProductInfo',
                noPermission: true,
                meta: {
                    title: '产品信息管理',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'rental-area',
                component: () => import('warehouse/views/warehouse-management/rental-area/index'),
                name: 'WarehouseRentalArea',
                noPermission: true,
                meta: {
                    title: '租赁面积管理',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'contract-billing',
                component: () => import('warehouse/views/warehouse-management/contract-billing/index'),
                name: 'WarehouseContractBilling',
                noPermission: true,
                meta: {
                    title: '合同计费规则管理',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'settled-customer-management',
                component: () => import('warehouse/views/warehouse-management/settled-customer-management/index'),
                name: 'settledCustomerManagement',
                noPermission: true,
                meta: {
                    title: '区仓入驻客户管理',
                    icon: 'fa fa-home'
                }
            }
        ]
    },
    {
        path: '/app/stock-up',
        name: 'stock-up',
        noPermission: true,
        useLayout: true,
        redirect: 'noRedirect',
        children: [
            {
                path: 'application-create',
                component: () => import('warehouse/views/stock-up/stock-up-application-create/index'),
                name: 'StockUpApplicationCreate',
                noPermission: true,
                meta: {
                    title: '备货申请任务创建',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'application-review',
                component: () => import('warehouse/views/stock-up/stock-up-application-review/index'),
                name: 'StockUpApplicationReview',
                noPermission: true,
                meta: {
                    title: '备货申请任务审核',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'application-details',
                component: () => import('warehouse/views/stock-up/stock-up-application-details/index.vue'),
                name: 'StockUpApplicationDetails',
                noPermission: true,
                meta: {
                    title: '备货申请详情',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'delivery-task-create',
                component: () => import('warehouse/views/stock-up/company-delivery-task-create/index.vue'),
                name: 'CompanyDeliveryTaskCreate',
                noPermission: true,
                meta: {
                    title: '发货任务生成',
                    icon: 'fa fa-home'
                }
            }
        ]
    },
    {
        // 公司发货业务页面
        path: '/app/company-delivery',
        name: 'company-delivery',
        noPermission: true,
        useLayout: true,
        redirect: 'noRedirect',
        children: [
            {
                path: 'management-list',
                component: () => import('warehouse/views/company-delivery/management-list/index'),
                name: 'CompanyDeliveryManagementList',
                noPermission: true,
                meta: {
                    title: '公司发货任务管理',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'delivery-list',
                component: () => import('warehouse/views/company-delivery/delivery-list/index'),
                name: 'CompanyDeliveryList',
                noPermission: true,
                meta: {
                    title: '公司发货清单查询',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'assets-list',
                component: () => import('warehouse/views/company-delivery/assets-list/index'),
                name: 'CompanyDeliveryAssetsList',
                noPermission: true,
                meta: {
                    title: '公司发货资产查询',
                    icon: 'fa fa-home'
                }
            }
        ]
    },
    {
        path: '/app/assets',
        name: 'warehouse-assets',
        noPermission: true,
        useLayout: true,
        redirect: 'noRedirect',
        children: [
            {
                path: 'summary-statistics',
                component: () => import('warehouse/views/warehouse-assets/summary-statistics/index'),
                name: 'WarehouseSummaryStatistics',
                noPermission: true,
                meta: {
                    title: '区仓资产统计汇总',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'history-statistics',
                component: () => import('warehouse/views/warehouse-assets/history-statistics/index'),
                name: 'WarehouseHistoryStatistics',
                noPermission: true,
                meta: {
                    title: '区仓历史在库资产统计',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'chart-statistics',
                component: () => import('warehouse/views/warehouse-assets/chart-statistics/index'),
                name: 'WarehouseChartStatistics',
                noPermission: true,
                meta: { title: '区仓资产统计图表', icon: 'fa fa-home' }
            },
            {
                path: 'assets-management',
                component: () => import('warehouse/views/warehouse-assets/assets-management/index'),
                name: 'WarehouseAssetsManagement',
                noPermission: true,
                meta: { title: '区仓资产信息查询', icon: 'fa fa-home' }
            },
            {
                path: 'operate-log',
                component: () => import('warehouse/views/warehouse-assets/operate-log/index'),
                name: 'OperateLog',
                noPermission: true,
                meta: { title: '区仓资产操作查询', icon: 'fa fa-home' }
            },
            {
                path: 'operate-details',
                component: () => import('warehouse/views/warehouse-assets/operate-log/details'),
                name: 'OperateDetails',
                noPermission: true,
                meta: { title: '区仓资产操作记录', icon: 'fa fa-home' }
            }
        ]
    },
    {
        path: '/app/allocation',
        name: 'allocation-management',
        noPermission: true,
        useLayout: true,
        redirect: 'noRedirect',
        children: [
            {
                path: 'allocation-management',
                component: () => import('warehouse/views/allocation-management/index'),
                name: 'AllocationManagement',
                noPermission: true,
                meta: { title: '调拨任务管理', icon: 'fa fa-home' }
            },
            {
                path: 'allocation-edit',
                component: () => import('warehouse/views/allocation-management/edit'),
                name: 'AllocationEdit',
                noPermission: true,
                meta: { title: '调拨任务编辑', icon: 'fa fa-home' }
            },
            {
                path: 'allocation-details',
                component: () => import('warehouse/views/allocation-management/details'),
                name: 'AllocationDetails',
                noPermission: true,
                meta: { title: '调拨任务详情', icon: 'fa fa-home' }
            }
        ]
    },
    {
        path: '/app/inventory',
        name: 'inventory-management',
        noPermission: true,
        useLayout: true,
        redirect: 'noRedirect',
        children: [
            {
                path: 'inventory-manage',
                component: () => import('warehouse/views/inventory-management/inventory-manage/index'),
                name: 'WarehouseInventoryManage',
                noPermission: true,
                meta: {
                    title: '盘点计划管理',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'inventory-count',
                component: () => import('warehouse/views/inventory-management/inventory-count/index'),
                name: 'WarehouseInventoryCount',
                noPermission: true,
                meta: {
                    title: '盘点统计',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'inventory-result',
                component: () => import('warehouse/views/inventory-management/inventory-result/index'),
                name: 'WarehouseInventoryResult',
                noPermission: true,
                meta: {
                    title: '盘点结果',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'inventory-taskQuery',
                component: () => import('warehouse/views/inventory-management/inventory-taskQuery/index'),
                name: 'WarehouseInventoryTaskQuery',
                noPermission: true,
                meta: {
                    title: '盘点任务',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'inventory-deal',
                component: () => import('warehouse/views/inventory-management/inventory-deal/index'),
                name: 'WarehouseInventoryDeal',
                noPermission: true,
                meta: {
                    title: '盘点结果处理',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'person-manage',
                component: () => import('warehouse/views/warehouse-basicInfo/person-manage/index'),
                name: 'PersonManage',
                noPermission: true,
                meta: {
                    title: '人员管理',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'supplier-manage',
                component: () => import('warehouse/views/inventory-management/warehouse-supplierInfo/index'),
                name: 'SupplierManage',
                noPermission: true,
                meta: {
                    title: '调拨承运商管理',
                    icon: 'fa fa-home'
                }
            }
        ]
    },
    {
        path: '/app/product-in-lib',
        name: 'product-in-lib',
        noPermission: true,
        useLayout: true,
        redirect: 'noRedirect',
        children: [
            {
                path: 'statistics',
                component: () => import('warehouse/views/product-in-library-info/statistics/index'),
                name: 'ProductInLibStatistics',
                noPermission: true,
                meta: {
                    title: '产品在库统计',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'distribution',
                component: () => import('warehouse/views/product-in-library-info/distribution/index'),
                name: 'ProductDistribution',
                noPermission: true,
                meta: {
                    title: '产品分布查询',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'statistics-material',
                component: () => import('warehouse/views/product-in-library-info/statistics-material/index'),
                name: 'ProductInLibMaterial',
                noPermission: true,
                meta: {
                    title: '产品在库统计-物料',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'distribution-material',
                component: () => import('warehouse/views/product-in-library-info/distribution-material/index'),
                name: 'ProductDistributionMaterial',
                noPermission: true,
                meta: {
                    title: '产品分布查询-物料',
                    icon: 'fa fa-home'
                }
            }
        ]
    },
    // 区仓综合看板
    {
        path: '/app/dashboard',
        name: 'indicator-dashboard',
        noPermission: true,
        useLayout: true,
        redirect: 'noRedirect',
        children: [
            {
                path: 'indicator-dashboard',
                component: () => import('warehouse/views/dashboard/indicator/dashboard'),
                name: 'IndicatorDashboard',
                noPermission: true,
                meta: {
                    title: '区仓综合看板',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'indicator-history-trend',
                component: () => import('warehouse/views/dashboard/indicator/indicator-history-trend'),
                name: 'IndicatorHistoryTrend',
                noPermission: true,
                meta: {
                    title: '区仓指标历史趋势查询',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'indicator-query',
                component: () => import('warehouse/views/dashboard/indicator/indicator-query'),
                name: 'IndicatorQuery',
                noPermission: true,
                meta: {
                    title: '区仓指标查询',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'indicator-daily',
                component: () => import('warehouse/views/dashboard/indicator/indicator-daily'),
                name: 'IndicatorDaily',
                noPermission: true,
                meta: {
                    title: '仓库指标日明细查询',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'indicator-monthly',
                component: () => import('warehouse/views/dashboard/indicator/indicator-monthly'),
                name: 'IndicatorMonthly',
                noPermission: true,
                meta: {
                    title: '区仓指标月明细查询',
                    icon: 'fa fa-home'
                }
            }
        ]
    },
    // 区仓统计报表
    {
        path: '/app/statistics-report',
        name: 'statistics-report',
        noPermission: true,
        useLayout: true,
        redirect: 'noRedirect',
        children: [
            {
                path: 'warehouse-realtime-report',
                component: () => import('warehouse/views/warehouse-statistics-report/realtime-report'),
                name: 'WarehouseRealtimeReport',
                noPermission: true,
                meta: {
                    title: '区仓实时信息',
                    icon: 'fa fa-home'
                }
            },
            {
                path: 'warehouse-history-report',
                component: () => import('warehouse/views/warehouse-statistics-report/history-report'),
                name: 'WarehouseHistoryReport',
                noPermission: true,
                meta: {
                    title: '区仓历史信息查询',
                    icon: 'fa fa-home'
                }
            }
        ]
    },
    // 结算单管理
    {
        path: '/app/settlement',
        name: 'settlementManagement',
        noPermission: true,
        useLayout: true,
        redirect: 'noRedirect',
        children: [
            {
                path: 'list',
                component: () => import('warehouse/views/settlement/index'),
                name: 'settlement',
                noPermission: true,
                meta: {
                    title: '结算单管理',
                    icon: 'fa fa-home'
                }
            }
        ]
    },
    // 撤机入库管理
    {
        path: '/app/withdrawal',
        name: 'withdrawalManagement',
        noPermission: true,
        useLayout: true,
        redirect: 'noRedirect',
        children: [
            {
                path: 'index',
                component: () => import('warehouse/views/withdrawal/index'),
                name: 'withdrawal',
                noPermission: true,
                meta: {
                    title: '撤机入库任务管理',
                    icon: 'fa fa-home'
                }
            }
        ]
    }
];
