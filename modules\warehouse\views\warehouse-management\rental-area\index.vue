<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig" />
            <snbc-base-form-dialog
                ref="dialogRef"
                :config="dialogConfig"
                @submit="handleSubmit"
            />
        </div>
    </div>
</template>

<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcBaseFormDialog from 'warehouse/components/snbc-dialog/SnbcBaseFormDialog.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import FormItems2 from 'warehouse/common/form-items/inventory-items.js';
import { inputRequired } from 'warehouse/common/form-rules/index.js';

const { input, number, date } = FormItems;
const { warehouseCode } = FormItems2;

// 弹窗校验规则
const rules = {
    maintenanceArea: [inputRequired('租赁面积')]
};

// 查询参数
const queryParams = { warehouseCode: '', currentMonth: '' };

// 查询区域配置项
const queryConfigItems = [
    warehouseCode,
    {
        ...date,
        name: '当前月份',
        modelKey: 'currentMonth',
        elDatePickerAttrs: {
            'type': 'month',
            'value-format': 'yyyy-MM'
        }
    }
];

// 编辑弹窗配置
const editDialogConfigItems = [
    {
        ...input,
        name: '区仓名称',
        modelKey: 'warehouseName',
        elInputAttrs: {
            disabled: true
        }
    },
    {
        ...input,
        name: '当前月份',
        modelKey: 'currentMonth',
        elInputAttrs: {
            disabled: true
        }
    },
    {
        ...input,
        name: '总面积',
        modelKey: 'totalArea',
        elInputAttrs: {
            disabled: true
        }
    }
];

const maintenanceArea = {
    ...number,
    name: '租赁面积',
    modelKey: 'maintenanceArea',
    elInputNumberAttrs: {
        min: 0,
        controls: false,
        precision: 2
    }
};

export default {
    name: 'WarehouseProductInfo',
    components: {
        SnbcBaseTable,
        SnbcBaseFormDialog
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        const {
            searchMaintenanceList: listApi,
            updateMaintenanceById: editApi
        } = this.$service.warehouse.warehouseRentalArea;
        return {
            listApi,
            editApi,
            tableConfig: {
                queryParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: listApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '总面积',
                        prop: 'totalArea',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '租赁面积',
                        prop: 'maintenanceArea',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '当前月份',
                        prop: 'currentMonth',
                        show: true,
                        minWidth: 120
                    }
                ],
                operations: [
                    {
                        name: '编辑',
                        type: 'primary',
                        handleClick: this.handleEdit
                    }
                ]
            },
            // 弹窗配置
            dialogConfig: {
                rules,
                items: []
            }
        };
    },
    computed: {},
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.handleQuery();
    },
    methods: {
        // 编辑操作
        handleEdit(row) {
            const data = this.$tools.cloneDeep(row);
            maintenanceArea.elInputNumberAttrs.max = Number(row.totalArea);
            this.dialogConfig.items = [
                ...editDialogConfigItems,
                maintenanceArea
            ];
            this.$refs.dialogRef.baseEditDialog(data, '租赁面积编辑');
        },
        // 新增或编辑提交
        async handleSubmit({ mode, form }) {
            await this.$tools.confirm('确认提交？');
            const params = { ...form };
            try {
                const { code, message } = await this.editApi(params);
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.$refs.dialogRef.hideDialog();
                this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        }
    }
};
</script>

<style lang="scss" scoped></style>
