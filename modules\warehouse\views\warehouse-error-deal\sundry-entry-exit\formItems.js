import FormItems from 'warehouse/common/form-items/index.js';
import {
    selectRequired,
    inputRequired,
    maxLength
} from 'warehouse/common/form-rules/index.js';

const sundryApplyName = {
    name: '申请名称',
    component: 'SnbcFormInput',
    modelKey: 'sundryApplyName'
};

const sundryApplyCode = {
    name: '申请单号',
    component: 'SnbcFormInput',
    modelKey: 'sundryApplyCode'
};

const requestDate = {
    ...FormItems.dateRange,
    name: '申请时间',
    modelKey: 'requestDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

const warehouseName = {
    name: '区仓名称',
    component: 'SnbcFormRegionWarehouseSelect',
    modelKey: 'warehouseCode'
};

const applyState = {
    name: '状态',
    component: 'SnbcFormSelect',
    modelKey: 'applyState',
    elOptions: [
        { label: '审核', value: '审核' },
        { label: '待提交', value: '待提交' },
        { label: '通过', value: '通过' },
        { label: '驳回', value: '驳回' }
    ]
};

const applyType = {
    name: '申请类型',
    component: 'SnbcFormSelect',
    modelKey: 'applyType',
    elOptions: [
        { label: '丢失', value: '丢失' },
        { label: '损毁', value: '损毁' },
        { label: '操作失误', value: '操作失误' }
    ]
};

const accessType = {
    name: '出入库类型',
    component: 'SnbcFormSelect',
    modelKey: 'accessType',
    elOptions: [
        { label: '出库', value: '出库' },
        { label: '入库', value: '入库' }
    ]
};

const associatedTaskCode = {
    name: '来源任务',
    component: 'SnbcFormInput',
    modelKey: 'associatedTaskCode'
};

const applySource = {
    name: '申请来源',
    component: 'SnbcFormSelect',
    modelKey: 'applySource',
    elOptions: [
        { label: '盘点自动修正', value: '盘点自动修正' },
        { label: '盘点提案修正', value: '盘点提案修正' },
        { label: '公司发货', value: '公司发货' },
        { label: '调拨', value: '调拨' },
        { label: '日常工作', value: '日常工作' }
    ]
};

const applyReason = {
    name: '申请原因',
    component: 'SnbcFormTextarea',
    modelKey: 'applyReason'
};

const remark = {
    name: '备注',
    component: 'SnbcFormInput',
    modelKey: 'remark'
};

// 弹窗校验规则
export const rules = {
    sundryApplyName: [inputRequired('申请名称'), maxLength(64)],
    warehouseCode: [selectRequired('区仓名称')],
    applyState: [selectRequired('出入库类型')],
    applySource: [selectRequired('申请来源')],
    accessType: [selectRequired('申请来源')],
    applyType: [selectRequired('申请类型')],
    associatedTaskCode: [inputRequired('来源任务')],
    applyReason: [inputRequired('申请原因'), maxLength(255)]
};

// 查询区域配置项
export const queryConfigItems = [
    sundryApplyName,
    sundryApplyCode,
    warehouseName,
    applyState,
    applyType,
    accessType,
    // productName,
    // assetCode,
    requestDate
];

// 新增弹窗配置
export const addDialogConfigItems = [
    sundryApplyName,
    warehouseName,
    accessType,
    applyType,
    applySource,
    associatedTaskCode,
    applyReason
];

// 编辑弹窗配置
export const editDialogConfigItems = [
    {
        ...sundryApplyCode,
        elInputAttrs: {
            disabled: true
        }
    },
    sundryApplyName,
    warehouseName,
    accessType,
    {
        ...applyState,
        elSelectAttrs: {
            disabled: true
        }
    },
    applyType,
    applySource,
    associatedTaskCode,
    applyReason,
    {
        ...remark,
        elInputAttrs: {
            disabled: true
        }
    }
];

// 审核弹窗配置
export const examineDialogConfigItems = [
    {
        ...sundryApplyCode,
        elInputAttrs: {
            disabled: true
        }
    },
    {
        ...sundryApplyName,
        elInputAttrs: {
            disabled: true
        }
    },
    {
        ...warehouseName,
        elSelectAttrs: {
            disabled: true
        }
    },
    {
        ...accessType,
        elSelectAttrs: {
            disabled: true
        }
    },
    {
        ...applyState,
        elSelectAttrs: {
            disabled: true
        }
    },
    {
        ...applyType,
        elSelectAttrs: {
            disabled: true
        }
    },
    {
        ...applySource,
        elSelectAttrs: {
            disabled: true
        }
    },
    {
        ...associatedTaskCode,
        elInputAttrs: {
            disabled: true
        }
    },
    {
        ...applyReason,
        elInputAttrs: {
            disabled: true
        }
    }
    // examineContent
];
