<template>
    <snbc-card title="调拨任务">
        <template #card-body>
            <snbc-form ref="snbcFormRef" :form="list" :config="formConfig">
                <template #form-body>
                    <template v-for="(item, index) in formItems">
                        <snbc-form-item v-if="item.modelKey !== 'taskExpandName'" :key="index" :config="item" />
                        <snbc-form-item v-if="item.modelKey === 'taskExpandName'" :key="index" :config="item">
                            <template #prepend>
                                {{ item.modelObj[item.modelValueKey] }}
                            </template>
                        </snbc-form-item>
                    </template>
                </template>
            </snbc-form>
            <snbc-table-list :list="taskDetailList" :config="tableConfig" />
        </template>
    </snbc-card>
</template>
<script>
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import SnbcForm from 'warehouse/components/snbc-form/SnbcForm.vue';
import SnbcFormItem from 'warehouse/components/snbc-form/SnbcFormItem.vue';
import SnbcTableList from 'warehouse/components/snbc-table/SnbcTableList.vue';
import CommonItems from 'warehouse/common/form-items/common-items.js';
import StockupItems from 'warehouse/common/form-items/stockup-item.js';
import FormRules from 'warehouse/common/form-rules/index.js';

const { input } = CommonItems;
const { expectedArrivalDate, remark } = StockupItems;
const { dateRequired, inputRequired } = FormRules;
const taskExpandName = {
    ...input,
    name: '调拨任务名称',
    modelKey: 'taskExpandName',
    modelValueKey: 'taskName',
    elInputAttrs: {
        maxlength: 20
    }
};

const special = {
    ...remark,
    name: '特殊说明',
    elInputAttrs: {
        placeholder:
            '如对调拨任务有特殊要求请再此填写，备注将在手机端任务下展示给区仓人员。示例：请调拨灰白色美团双面柜。'
    }
};

const rules = {
    taskExpandName: [inputRequired('调拨任务名称')],
    expectedArrivalDate: [dateRequired('期望到货日期')]
};
export default {
    name: 'AllocationTask',
    components: {
        SnbcCard,
        SnbcForm,
        SnbcFormItem,
        SnbcTableList
    },
    props: {
        list: {
            type: Object,
            default() {
                return {};
            }
        }
    },
    data() {
        return {
            // 表单配置
            formConfig: {
                elFormAttrs: {
                    rules,
                    'label-width': '160px'
                }
            },
            // 任务明细配置
            tableConfig: {
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '来源仓',
                        prop: 'fromWarehouseName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '数量',
                        prop: 'sendNumber',
                        show: true,
                        minWidth: 120
                    }
                ]
            }
        };
    },
    computed: {
        formItems() {
            return [taskExpandName, expectedArrivalDate, special].map((item) => {
                return {
                    modelObj: this.list,
                    elFormItemAttrs: {
                        label: item.name,
                        ...(item.elFormItemAttrs || {})
                    },
                    ...item
                };
            });
        },
        taskDetailList() {
            return this.list.taskDetailList.map((item, index) => {
                item.index = index + 1;
                return item;
            });
        }
    },
    // mounted() {},
    methods: {
        getFormRef() {
            return this.$refs.snbcFormRef.getFormRef();
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__content {
    width: 60%;
}
</style>
