import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();
    const service = {
        taskAdditional: {
            createDraft(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/task_additional/create_draft',
                    method: 'post',
                    data
                });
            },
            createCommit(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/task_additional/create_commit',
                    method: 'post',
                    data
                });
            },
            examine(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/task_additional/confirm',
                    method: 'post',
                    data
                });
            },
            remove(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/task_additional/remove`,
                    method: 'post',
                    data,
                    params: {
                        id: data.id
                    }
                });
            },
            edit(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/task_additional/update',
                    method: 'post',
                    data
                });
            },
            detail(taskAdditionalCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/task_additional/detail',
                    method: 'get',
                    params: { taskAdditionalCode }
                });
            },
            getInOutRecord(associatedTaskCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/task_additional/in_out_record',
                    method: 'get',
                    params: { associatedTaskCode }
                });
            },
            list(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/task_additional/search_todo_tasks',
                    method: 'post',
                    data
                });
            },
            listSearch(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/task_additional/search_show_tasks',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
