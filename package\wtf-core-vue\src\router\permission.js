// import { Message } from 'element-ui';
import Tools from '../utils';
import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css'; // progress bar style
import Cookies from 'js-cookie';

import VueCookies from 'vue-cookies';
// 引入BASE64解码
const Base64 = require('js-base64').Base64;
// import { getToken } from '@/utils/auth' // get token from cookie
// import getPageTitle from '@/utils/get-page-title'

NProgress.configure({ showSpinner: false }); // NProgress Configuration

// 工具类初始化
const tools = new Tools();

// const whiteList = ['/login', '/auth-redirect']; // no redirect whitelist

export default (router, store) => {
	router.beforeEach(async (to, from, next) => {
		NProgress.start();
		// 页签标题国际化
		document.title = tools.getPageTitle(to.meta.title);
		// 如果当前token存在，如果输入不存在的路由，则跳转到首页；如果token不存在，则跳转回登录页
		let hasToken = "";

		if (VueCookies.get('token')) {
			hasToken = Base64.decode(VueCookies.get('token'));
		}
		if (hasToken) {
			// 匹配前往的路由不存在
			if (to.matched.length === 0) {
				// 跳转到首页
				next({ path: '/app/dashboard-index' });
			} else {
				next();
			}
			NProgress.done();

		} else {
            const evnFlag = tools.getEnvFlag();
            const loginPageUrl = `https://${evnFlag}adm.xinbeiyang.info/#/login/index`;
            window.open(loginPageUrl, "_self");
			NProgress.done();
		}
	});

	router.afterEach(() => {
		// finish progress bar
		NProgress.done();
	});
};
