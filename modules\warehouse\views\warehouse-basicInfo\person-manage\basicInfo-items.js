import commonItems from 'warehouse/common/form-items/common-items.js';

export const updatePersonInfoItems = (function () {
    const roleCode = {
        ...commonItems.select,
        name: '角色',
        modelKey: 'roleCode',
        elSelectAttrs: {
            disabled: true
        }
    };
    const phone = {
        ...commonItems.input,
        name: '电话号',
        modelKey: 'phone',
        elInputAttrs: {
            disabled: true
        }
    };

    const loginAccount = {
        ...commonItems.input,
        name: '登陆账号',
        modelKey: 'loginAccount',
        elInputAttrs: {
            disabled: true
        }
    };
    const userName = {
        ...commonItems.input,
        name: '人员名称',
        modelKey: 'userName',
        elInputAttrs: {
            disabled: 'true'
        }
    };
    const warehouseCodeList = {
        ...commonItems.select,
        name: '区仓名称',
        modelKey: 'warehouseCodeList',
        selectAllAble: true,
        elSelectAttrs: {
            multiple: true
        }
    };

    return [warehouseCodeList, roleCode, userName, phone, loginAccount];
})();
