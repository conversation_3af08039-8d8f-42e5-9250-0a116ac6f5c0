<template>
    <div class="view">
        <div class="content">
            <IndicatorCard ref="IndicatorCardRef" @getIndicators="getIndicators" @checkRules="checkRules" />
            <IndicatorTop ref="IndicatorTopRef" class="margin-top-10" :indicators="indicators" @query="handleQuery" />
            <IndicatorTrend ref="IndicatorTrendRef" class="margin-top-10" />
            <IndicatorCalculationRules ref="IndicatorCalculationRulesRef" />
        </div>
    </div>
</template>

<script>
import IndicatorCard from './components/IndicatorCard.vue';
import IndicatorTop from './components/IndicatorTop.vue';
import IndicatorTrend from './components/IndicatorTrend.vue';
import IndicatorCalculationRules from './components/IndicatorCalculationRules.vue';
import functions from 'frame/mixins/functions.js';

export default {
    name: 'IndicatorDashboard',
    components: {
        IndicatorCard,
        IndicatorTop,
        IndicatorTrend,
        IndicatorCalculationRules
    },
    mixins: [functions],
    data() {
        return {
            // 区仓指标数据，来源IndicatorCard
            indicators: {}
        };
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.queryPageData();
    },
    methods: {
        // 区仓指标数据，子组件间通信
        getIndicators(data) {
            this.indicators = data;
        },
        checkRules() {
            this.$refs.IndicatorCalculationRulesRef.openDialog();
        },
        // 查询页面数据
        queryPageData() {
            this.$refs.IndicatorTopRef.queryData();
            this.$refs.IndicatorTrendRef.queryData();
        },
        // 按统计周期查询区仓指标
        handleQuery(params) {
            this.$refs.IndicatorCardRef.queryData(params);
        }
    }
};
</script>
