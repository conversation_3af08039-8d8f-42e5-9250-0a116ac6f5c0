<template>
    <el-dialog class="custom-dialog" :visible.sync="dialogVisible" v-bind="elDialogAttrs">
        <snbc-card title="备货基础信息">
            <template #card-body>
                <snbc-form ref="snbcFormRef" :form="requestData.stockRequest" :config="formConfig">
                    <template #form-body>
                        <template v-for="(item, index) in formItems">
                            <snbc-form-item
                                v-if="item.modelKey !== 'stockRequestName' || mode !== 'add'"
                                :key="index"
                                :config="item"
                            />
                            <snbc-form-item
                                v-if="item.modelKey === 'stockRequestName' && mode === 'add'"
                                :key="index"
                                :config="item"
                            >
                                <template #prepend>
                                    {{ stockRequestName }}
                                </template>
                            </snbc-form-item>
                        </template>
                    </template>
                </snbc-form>
            </template>
        </snbc-card>
        <snbc-card class="margin-top-10" title="备货详细信息" button-name="新增" @action="handleAdd">
            <template #card-body>
                <el-table :data="requestData.stockRequestDetailList" v-bind="elTableAttrs">
                    <el-table-column v-bind="elTableColumnAttrs" label="客户名称" width="140">
                        <template slot-scope="scope">
                            <el-select
                                v-model="scope.row.customerCode"
                                placeholder="请选择"
                                @change="changeCustomer(scope.row)"
                            >
                                <el-option
                                    v-for="(option, index) in customerList"
                                    :key="index"
                                    :label="option.customerName"
                                    :value="option.customerCode"
                                />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column v-bind="elTableColumnAttrs" label="产品名称" width="140">
                        <template slot-scope="scope">
                            <el-select v-model="scope.row.productName" placeholder="请选择">
                                <el-option
                                    v-for="(option, index) in getCustomerProduct(scope.row.customerCode)"
                                    :key="'product' + index"
                                    :label="option.productName"
                                    :value="option.productName"
                                    :disabled="isDisabledProduct(option.productName)"
                                />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column v-bind="elTableColumnAttrs" label="申请数量" width="140">
                        <template slot-scope="scope">
                            <el-input-number
                                v-model="scope.row.requestNumber"
                                :min="1"
                                controls-position="right"
                            ></el-input-number>
                        </template>
                    </el-table-column>
                    <el-table-column v-bind="elTableColumnAttrs" label="到货计划" width="160">
                        <template slot-scope="scope">
                            <el-date-picker
                                v-model="scope.row.expectedArrivalDate"
                                type="date"
                                placeholder="选择日期"
                                value-format="yyyy-MM-dd"
                                :picker-options="{ disabledDate }"
                            ></el-date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column v-bind="elTableColumnAttrs" label="申请原因" width="180">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.requestCause" maxlength="255"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column v-bind="operateColumnAttrs">
                        <template slot-scope="scope">
                            <el-button size="mini" type="danger" @click="handleRemove(scope.$index)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
        </snbc-card>
        <span slot="footer" class="dialog-footer">
            <el-button @click="handleSubmit('draft')" :disabled="btnDisabled" v-if="mode === 'add'">草 稿</el-button>
            <el-button @click="handleSubmit('save')" :disabled="btnDisabled" v-if="mode === 'edit'">保 存</el-button>
            <el-button type="primary" @click="handleSubmit('submit')" :disabled="btnDisabled">提 交</el-button>
        </span>
    </el-dialog>
</template>

<script>
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import SnbcForm from 'warehouse/components/snbc-form/SnbcForm.vue';
import SnbcFormItem from 'warehouse/components/snbc-form/SnbcFormItem.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import FormRules from 'warehouse/common/form-rules/index.js';
import ElAttrs from 'warehouse/common/el-attrs/index.js';

const { selectRequired } = FormRules;

const { directionWarehouseCode, requestFrom, input, remark } = FormItems;

const { elDialogAttrs, elTableAttrs, elTableColumnAttrs, operateColumnAttrs } = ElAttrs;

const stockRequestName = {
    ...input,
    name: '申请名称',
    modelKey: 'stockRequestName',
    modelValueKey: 'directionWarehouseName'
};

const rules = {
    directionWarehouseCode: [selectRequired('目标区仓')],
    stockRequestName: [{ required: true, message: `请补充申请名称`, trigger: 'change' }],
    requestFrom: [selectRequired('备货来源')]
};

export default {
    name: 'StockUpApplicationCreateOfNonDesignatedAssets',
    components: {
        SnbcForm,
        SnbcFormItem,
        SnbcCard
    },
    data() {
        return {
            elTableAttrs,
            operateColumnAttrs,
            elTableColumnAttrs: {
                ...elTableColumnAttrs,
                'show-overflow-tooltip': false
            },
            dialogVisible: false,
            mode: 'add',
            elDialogAttrs: {
                ...elDialogAttrs,
                width: '950px'
            },
            requestData: {
                stockRequest: {},
                stockRequestDetailList: []
            },
            formConfig: {
                elFormAttrs: {
                    rules
                }
            },
            customerList: [],
            // 客户名称map
            customerMap: {},
            // 区仓map
            warehouseMap: {},
            // 客户产品map
            customerProductMap: {},
            // 申请名称
            stockRequestName: ''
        };
    },
    computed: {
        formItems() {
            return [
                {
                    ...directionWarehouseCode,
                    elSelectAttrs: {
                        disabled: this.mode !== 'add'
                    },
                    component: 'SnbcFormRegionWarehouseSelect'
                },
                {
                    ...stockRequestName,
                    elInputAttrs: {
                        disabled: this.mode !== 'add',
                        maxlength: this.mode === 'add' ? 20 : '',
                        placeholder: '请补充申请名称'
                    }
                },
                requestFrom,
                remark
            ].map((item) => {
                return {
                    modelObj: this.requestData.stockRequest,
                    elFormItemAttrs: {
                        label: item.name,
                        ...(item.elFormItemAttrs || {})
                    },
                    ...item
                };
            });
        },
        // 获取选择客户下的所有产品
        getCustomerProduct() {
            return (customerCode) => {
                return this.customerProductMap[customerCode] || [];
            };
        },
        // 当无详细信息时按钮禁用
        btnDisabled() {
            return !this.requestData.stockRequestDetailList.length;
        },
        // 产品名称禁用项，已经选择的产品名称不可重复选择
        isDisabledProduct() {
            return (productName) => {
                return this.requestData.stockRequestDetailList.map((item) => item.productName).includes(productName);
            };
        }
    },
    watch: {
        'requestData.stockRequest.directionWarehouseCode': function (newVal) {
            // (新增时)申请名称前缀
            if (this.mode === 'add') {
                this.stockRequestName = this.createStockRequestName(this.warehouseMap[newVal]);
                // 查询客户名称
                if (newVal) {
                    this.queryAllAccessCustomer();
                    return;
                }
                this.customerList = [];
            }
        },
        'customerList': {
            handler: 'resetOptions',
            immediate: true
        }
    },
    mounted() {
        // 查询所有目标区仓
        this.queryAllWarehouse();
    },
    methods: {
        // 生成申请名称前缀
        createStockRequestName(warehouseName) {
            if (warehouseName) {
                return `${warehouseName + this.getCurrentDate()}备货申请`;
            }
            return '';
        },
        // 获取当前时间
        getCurrentDate() {
            const currentDate = new Date();
            const date = {
                year: currentDate.getFullYear(),
                month: currentDate.getMonth() + 1,
                day: currentDate.getDate()
            };
            const month = date.month > 10 ? date.month : `0${date.month}`;
            const day = date.day > 10 ? date.day : `0${date.day}`;
            return date.year + month + day;
        },
        // 到货时间只能选择当天之后的时间
        disabledDate(time) {
            return time.getTime() <= Date.now();
        },
        // 选择目标区仓变换时重置信息
        resetOptions() {
            if (this.mode !== 'add') {
                return;
            }
            const resetInfo = {
                customerCode: '',
                productName: ''
            };
            this.requestData.stockRequestDetailList.map((item) => {
                Object.assign(item, resetInfo);
                return item;
            });
            this.customerProductMap = {};
        },
        // 显示信息
        async openDialog(config, data) {
            // 备货申请详情数据
            this.requestData = data;
            this.mode = config.mode;
            this.elDialogAttrs = {
                ...this.elDialogAttrs,
                title: config.title
            };
            this.customerProductMap = {};
            // 重置校验
            this.$refs.snbcFormRef && this.$refs.snbcFormRef.getFormRef().resetFields();
            // 编辑操作查询已填写的信息
            if (this.mode === 'edit') {
                // 查询目标区仓下的客户
                await this.queryAllAccessCustomer();
                // 获得已选择的客户的产品信息
                this.requestData.stockRequestDetailList.forEach((item) => {
                    // 获取对应的产品列表
                    this.getProductList(item.customerCode);
                });
            }
            this.dialogVisible = true;
        },
        // 关闭
        hideDialog() {
            this.dialogVisible = false;
        },
        // 客户选择变动联动数据方法
        changeCustomer(row) {
            // 重置产品名称
            row.productName = '';
            // 获取对应的产品列表
            this.getProductList(row.customerCode);
        },
        // 查询客户对应的产品list
        getProductList(customerCode) {
            if (!this.customerProductMap[customerCode] && customerCode) {
                this.queryAllAccessProductName(this.requestData.stockRequest.directionWarehouseCode, customerCode);
            }
        },
        // 新增申请草稿、提交，编辑时
        async handleSubmit(type) {
            // 表单校验
            await this.$tools.validateForm(this.$refs.snbcFormRef.getFormRef());
            // 校验明细列表内容是否都填写完整
            const flag = this.requestData.stockRequestDetailList.every(
                (item) =>
                    item.customerCode &&
                    item.productName &&
                    item.requestNumber &&
                    item.expectedArrivalDate &&
                    item.requestCause
            );
            if (!flag) {
                this.$tools.message.err('详细信息填写不完整，请检查');
                return;
            }
            const params = this.$tools.cloneDeep(this.requestData);
            // 当新增时申请名称赋值
            if (this.mode === 'add') {
                params.stockRequest.stockRequestName =
                    this.stockRequestName + (params.stockRequest.stockRequestName || '');
            }
            // 明细列表中客户名称获取
            params.stockRequestDetailList = params.stockRequestDetailList.map((item) => {
                return {
                    customerName: this.customerMap[item.customerCode],
                    ...item
                };
            });
            let submitApi = this.$service.warehouse.stockUp.add;
            if (type === 'save') {
                submitApi = this.$service.warehouse.stockUp.update;
            } else if (type === 'draft') {
                params.identifier = '草稿';
            } else {
                params.identifier = this.mode === 'add' ? '提交' : '修改提交';
            }
            try {
                const res = await submitApi(params);
                const { code, message } = res;
                this.hideDialog();
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc(message);
                this.$parent.$refs.tableRef1.handleQuery();
            } catch (error) {
                console.error(error);
                this.$tools.message.err(error);
            }
        },
        // 新增一条明细
        handleAdd() {
            this.requestData.stockRequestDetailList.push({
                customerCode: '',
                productName: '',
                requestNumber: '',
                expectedArrivalDate: '',
                requestCause: ''
            });
        },
        // 删除明细
        handleRemove(index) {
            this.requestData.stockRequestDetailList.splice(index, 1);
        },
        // 查询在某区仓有准入产品的客户列表
        async queryAllAccessCustomer() {
            try {
                const res = await this.$service.warehouse.warehouseEntryAndExitRules.getAllAccessCustomer(
                    this.requestData.stockRequest.directionWarehouseCode
                );
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.customerList = result;
                result.forEach((item) => {
                    this.customerMap[item.customerCode] = item.customerName;
                });
            } catch (error) {
                console.error(error);
                this.$tools.message.err(error);
            }
        },
        // 按照客户、区仓查询该客户在该区仓准入的产品名称列表
        async queryAllAccessProductName(warehouseCode, customerCode) {
            try {
                const res = await this.$service.warehouse.warehouseEntryAndExitRules.getAllAccessProductName(
                    warehouseCode,
                    this.customerMap[customerCode]
                );
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                const productList =
                    result.map((item) => {
                        return {
                            productName: item
                        };
                    }) || [];
                this.$set(this.customerProductMap, customerCode, productList);
            } catch (error) {
                console.error(error);
                this.$tools.message.err(error);
            }
        },
        // 查询所有目标区仓
        async queryAllWarehouse() {
            try {
                const res = await this.$service.warehouse.warehouseBaseInformation.getAll();
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                result.forEach((item) => {
                    this.warehouseMap[item.warehouseCode] = item.warehouseName;
                });
            } catch (error) {
                console.error(error);
                this.$tools.message.err(error);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .el-input-number--medium,
::v-deep .el-date-editor.el-input {
    width: auto;
}
</style>
