import BackToTop from './BackToTop';
import Breadcrumb from './Breadcrumb';
// import { Keyboard, LineMarker, MixChart } from './Charts';
import DndList from './DndList';
import DragSelect from './DragSelect';
import DropdownMenu from './DropdownMenu';
import Dropzone from './Dropzone';
import ErrorLog from './ErrorLog';
import GithubCorner from './GithubCorner';
import Hamburger from './Hamburger';
import HeaderSearch from './HeaderSearch';
import ImageCropper from './ImageCropper';
// import JsonEditor from './JsonEditor';
import KanBan from './KanBan';
import LangSelect from './LangSelect';
import Mallki from './Mallki';
// import MarkdownEditor from './MarkdownEditor';
import MdInput from './MdInput';
import Pagination from './Pagination';
import PanThumb from './PanThumb';
import RightPanel from './RightPanel';
import Screenfull from './Screenfull';
import SizeSelect from './SizeSelect';
import Sticky from './Sticky';
import SvgIcon from './SvgIcon';
import ThemePicker from './ThemePicker';
import Tinymce from './Tinymce';
import { SingleImageUpload, SingleImageUpload2, SingleImageUpload3 } from './Upload';
// import UploadExcel from './UploadExcel';
import VueCountTo from 'vue-count-to';

VueCountTo.name = 'VueCountTo';

const components = [
    BackToTop,
    Breadcrumb,
    // Keyboard,
    // LineMarker,
    // MixChart,
    DndList,
    DragSelect,
    DropdownMenu,
    Dropzone,
    ErrorLog,
    GithubCorner,
    Hamburger,
    HeaderSearch,
    ImageCropper,
    // JsonEditor,
    KanBan,
    LangSelect,
    Mallki,
    // MarkdownEditor,
    MdInput,
    Pagination,
    PanThumb,
    RightPanel,
    Screenfull,
    SizeSelect,
    Sticky,
    SvgIcon,
    ThemePicker,
    Tinymce,
    SingleImageUpload,
    SingleImageUpload2,
    SingleImageUpload3,
    // UploadExcel,
    VueCountTo
];

const install = function(Vue, opts = {}) {
    components.forEach(component => {
        Vue.component(component.name, component);
    });
};

/* istanbul ignore if */
if (typeof window !== 'undefined' && window.Vue) {
    install(window.Vue);
}

export default {
    version: '1.0.0',
    install
};
