<template>
    <div>
        <div v-show="tableShow" ref="template">
            <slot>
                <span>编写你自己的打印区域组件，然后slot插入到vue-easy-print</span>
            </slot>
        </div>
        <!-- 这里按钮偷了个懒，直接拿了element-ui的css name来用。 -->
        <button v-if="buttonShow" @click="print" type="button" :class="buttonClass">
            <span>开始打印</span>
        </button>
    </div>
</template>

<script>
export default {
    name: 'VueEasyPrint',
    components: {},
    props: {
        // 针对分页表格模式：末尾空白行插入
        spaceRow: {
            type: Boolean,
            default: false
        },

        // 针对分页表格模式：传入的打印数据。
        tableData: {
            type: Object,
            default() {
                return undefined;
            }
        },
        // 是否显示表格
        tableShow: {
            type: Boolean,
            default: false
        },
        // 是否显示默认的打印按钮
        buttonShow: {
            type: Boolean,
            default: false
        },
        buttonClass: {
            type: String,
            default: 'el-button el-button--default'
        },
        // 每页多少行
        onePageRow: {
            type: Number,
            default: 5
        },

        title: {
            type: String,
            default: '服务信息化平台'
        },

        beforeCopy: Function,
        beforePrint: Function
    },
    data() {
        return {};
    },
    mounted() {
        this.init();
    },
    methods: {
        init() {
            let printI = document.getElementById('easyPrintIframe');
            if (!printI) {
                printI = document.createElement('iframe');
                printI.id = 'easyPrintIframe';
                printI.style.position = 'fixed';
                printI.style.width = '0';
                printI.style.height = '0';
                printI.style.top = '-100px';

                printI.onload = () => {
                    this.getStyle();
                };

                document.body.appendChild(printI);
            } else {
                this.getStyle();
            }
        },
        print() {
            if (typeof this.beforeCopy === 'function') {
                // 检测到有复制前需要执行的功能
                this.beforeCopy();
            }

            const $iframe = document.getElementById('easyPrintIframe');
            // 复制body，打印内容
            $iframe.contentDocument.body.innerHTML = this.$refs.template.innerHTML;

            if (typeof this.beforePrint === 'function') {
                // 检测到有打印前需要执行的功能
                // 比如有些二维码组件无法直接复制dom完成。
                this.beforePrint();
            }

            // 执行打印
            this.$nextTick(() => {
                setTimeout(() => {
                    document.title = this.title;
                    $iframe.contentWindow.print();
                    document.title = '服务信息化平台';
                }, 100);
            });
        },
        getStyle() {
            const printI = document.getElementById('easyPrintIframe');
            let str = '';
            const styles1 = document.querySelectorAll('style');
            for (let i = 0; i < styles1.length; i++) {
                str += styles1[i].outerHTML;
            }

            printI.contentDocument.head.innerHTML = str;
            // 添加link引入
            const styles = document.querySelectorAll('link');
            for (let i = 0; i < styles.length; i++) {
                const link = document.createElement('link');
                link.setAttribute('rel', 'stylesheet');
                if (styles[i].type) link.setAttribute('type', styles[i].type);
                else link.setAttribute('type', 'text/css');
                link.setAttribute('href', styles[i].href);
                link.setAttribute('media', 'all');
                printI.contentDocument.head.appendChild(link);
            }
        }
    }
};
</script>
<style media="print">
@page {
    size: auto;
    margin: 0mm;
}
</style>
