<template>
    <div class="view">
        <div class="content">
            <el-page-header
                @back="goBack"
                content="杂项资产出入库申请详情"
            ></el-page-header>
            <snbc-tabs :tabs="tabs" style="margin-bottom: 32px">
                <!-- 基础信息 -->
                <template #basicInfo>
                    <snbc-card title="杂项资产出入库申请基础信息">
                        <template #card-body>
                            <snbc-descriptions :items="basicInfo" />
                        </template>
                    </snbc-card>
                </template>
                <!-- 执行日志 -->
                <template #logisticsInfo>
                    <snbc-card title="执行日志">
                        <template #card-body>
                            <execute-log
                                :sundryEntryExitCode="sundryEntryExitCode"
                            />
                        </template>
                    </snbc-card>
                </template>
            </snbc-tabs>
        </div>
    </div>
</template>
<script>
import functions from 'frame/mixins/functions.js';
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import SnbcTabs from 'warehouse/components/snbc-tabs/SnbcTabs.vue';
import SnbcDescriptions from 'warehouse/components/snbc-descriptions/SnbcDescriptions.vue';
import ExecuteLog from './executeLog.vue';

export default {
    name: 'SundryEntryExit',
    components: {
        SnbcCard,
        SnbcTabs,
        SnbcDescriptions,
        ExecuteLog
    },
    mixins: [functions],
    data() {
        return {
            tabs: [
                {
                    label: '基础信息',
                    id: 'basicInfo'
                },
                {
                    label: '执行日志',
                    id: 'logisticsInfo'
                }
            ],
            // 基础信息
            basicInfo: [
                { label: '申请单号', prop: 'sundryApplyCode', value: '' },
                { label: '申请名称', prop: 'sundryApplyName', value: '' },
                { label: '区仓名称', prop: 'warehouseName', value: '' },
                { label: '出入库类型', prop: 'accessType', value: '' },
                { label: '申请类型', prop: 'applyType', value: '' },
                { label: '申请来源', prop: 'applySource', value: '' },
                { label: '来源任务', prop: 'associatedTaskCode', value: '' },
                { label: '产品序列号', prop: 'assetCode', value: '' },
                { label: '产品名称', prop: 'productName', value: '' },
                { label: '申请人', prop: 'createUserName', value: '' },
                { label: '申请原因', prop: 'applyReason', value: '' },
                { label: '状态', prop: 'applyState', value: '' },
                { label: '申请时间', prop: 'createTime', value: '' },
                { label: '备注', prop: 'remark', value: '' }
            ],
            sundryEntryExitCode: ''
        };
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.sundryEntryExitCode = this.$route.query.sundryEntryExitCode;
        // 查询详情
        this.queryDetails();
    },
    methods: {
        // 查询详情
        async queryDetails() {
            try {
                const res =
                    await this.$service.warehouse.sundryEntryExit.getDetail(
                        this.sundryEntryExitCode
                    );
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                // 获取杂项出入库申请基本信息
                const basicInfo = result;
                this.basicInfo.map((item) => {
                    item.value = basicInfo[item.prop];
                    return item;
                });
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 返回
        goBack() {
            this.$router.go(-1);
        }
    }
};
</script>
