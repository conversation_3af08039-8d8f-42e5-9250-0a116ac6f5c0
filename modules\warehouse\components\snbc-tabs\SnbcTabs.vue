<template>
    <el-tabs v-model="activeName" type="border-card">
        <el-tab-pane v-for="tab in tabs" :key="tab.id">
            <span slot="label">{{ tab.label }}</span>
            <slot :name="tab.id"></slot>
        </el-tab-pane>
    </el-tabs>
</template>
<script>
export default {
    name: 'SnbcTabs',
    props: {
        tabs: {
            type: Array,
            default: () => {
                return [];
            }
        }
    },
    data() {
        return {
            activeName: ''
        };
    }
};
</script>
<style lang="scss" scoped></style>
