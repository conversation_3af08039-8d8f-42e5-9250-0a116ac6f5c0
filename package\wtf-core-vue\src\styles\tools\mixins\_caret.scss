/// 绘制三角形
/// @param {String} $position 
/// @param {Number} $caret-width 三角形外宽度
/// @param {Number} $border-width 三角形内宽度
/// @param {String} $direction 三角形方向
/// @param {String} $border-color 边框颜色
/// @param {String} $background-color 背景颜色
/// @example
/// //SCSS
/// .caret{
///   @include caret(absolute,50px,1px,bottom,red,#ccc);
/// }
/// //CSS
/// .caret {
///     position: absolute;
/// }
/// .caret:before, .caret:after {
///    content: "";
///   `position: absolute;
/// }
/// .caret:before {
///    top: 0;
///    left: 0;
///    border-top: 50px solid red;
///    border-left: 50px solid transparent;
///    border-right: 50px solid transparent;
/// }
/// .caret:after {
///    left: 1px;
///    top: 0;
///    border-top: 49px solid #ccc;
///    border-left: 49px solid transparent;
///    border-right: 49px solid transparent;
/// }
/// @link http://lugolabs.com/caret

@mixin caret($position,$caret-width,$border-width,$direction,$border-color,$background-color){
  position: $position;
  
  &:before,
  &:after {
    content:"";
    position: absolute;
  }
  @if $direction == top {
    &:before {
      top:0;
      left: 0;
      border-bottom: $caret-width solid $border-color;
      border-left: $caret-width solid transparent;
      border-right: $caret-width solid transparent;
    }
    &:after {
      left: $border-width;
      top: $border-width;
      border-bottom: ($caret-width - $border-width) solid $background-color;
      border-left: ($caret-width - $border-width) solid transparent;
      border-right: ($caret-width - $border-width) solid transparent;
    }
  }
  @else if $direction == right {
    &:before {
      top:0;
      left: 0;
      border-left: $caret-width solid $border-color;
      border-top: $caret-width solid transparent;
      border-bottom: $caret-width solid transparent;
    }
    &:after {
      left: 0;
      top: $border-width;
      border-left: ($caret-width - $border-width) solid $background-color;
      border-top: ($caret-width - $border-width) solid transparent;
      border-bottom: ($caret-width - $border-width) solid transparent;
    }
  }
  @else if $direction == bottom {
    &:before {
      top:0;
      left: 0;
      border-top: $caret-width solid $border-color;
      border-left: $caret-width solid transparent;
      border-right: $caret-width solid transparent;
    }
    &:after {
      left: $border-width;
      top: 0;
      border-top: ($caret-width - $border-width) solid $background-color;
      border-left: ($caret-width - $border-width) solid transparent;
      border-right: ($caret-width - $border-width) solid transparent;
    }
  } 
  @else if $direction == left {
    &:before {
      top:0;
      left: 0;
      border-right: $caret-width solid $border-color;
      border-top: $caret-width solid transparent;
      border-bottom: $caret-width solid transparent;
    }
    &:after {
      left: $border-width;
      top: $border-width;
      border-right: ($caret-width - $border-width) solid $background-color;
      border-top: ($caret-width - $border-width) solid transparent;
      border-bottom: ($caret-width - $border-width) solid transparent;
    }
  }
}