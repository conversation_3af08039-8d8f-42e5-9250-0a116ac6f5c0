import Vue from 'vue';
import Router from 'vue-router';
// import Tools from 'wtf-core-vue/src/utils';
import defaultLayout from '../layout/default/index.vue';

// 工具类初始化
// const tools = new Tools();
// layout列表，可以配合
const layoutObject = {
    'default': defaultLayout
};
// 当前配置的layout
let currentLayout = defaultLayout;

// 存放所有路由信息
const allRouters = [];
// 存储无需权限过滤的路由
// const defaultRouter = {};
// 存储需要权限过滤的路由
const needPermissionRouter = {};
// 菜单项默认数据
const defaultRouteItemMeta = {
    icon: 'el-icon-menu'
};

Vue.use(Router);

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
   // 当设置 true 的时候该路由不会在侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * hidden: true // (默认 false)
 *
   //当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * redirect: 'noRedirect'
 *
   // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
   // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
   // 若你想不管路由下面的 children 声明的个数都显示你的根路由
   // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * alwaysShow: true
 *
 * name: 'router-name' // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta: {
 *   roles: ['admin', 'editor'] // 设置该路由进入的权限，支持多个权限叠加
 *   title: 'title' // 设置该路由在侧边栏和面包屑中展示的名字
 *   icon: 'svg-name' // 设置该路由的图标，支持 svg-class，也支持 el-icon-x element-ui 的 icon
 *   noCache: true // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
 *   breadcrumb: false //  如果设置为false，则不会在breadcrumb面包屑中显示(默认 true)
 *   affix: true // 如果设置为true，它则会固定在tags-view中(默认 false)
 *
     // 当路由设置了该属性，则会高亮相对应的侧边栏。
     // 这在某些场景非常有用，比如：一个文章的列表页路由为：/article/list
     // 点击文章进入文章详情页，这时候路由为/article/1，但你想在侧边栏高亮文章列表的路由，就可以进行如下设置
 *   activeMenu: '/article/list'
 * }
 */
export const asyncRoutes = [];
/**
 * 默认权限，即不需要权限路由
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export let constantRoutes = [];

/**
 * 将source路由数据合并到target路由数据，包含递归children进行合并
 * @param {Array} sourceRouters 源路由数据，需要合并的数据
 * @param {Array} targetRouters 目标路由数据，源数据将合并到这里
 */
export function mergeRouters(sourceRouters, targetRouters) {
    targetRouters = targetRouters || [];
    sourceRouters.forEach((item) => {
        layoutHandler(item);

        let oldItem = targetRouters.filter(v => v.name === item.name)[0];
        // 判断路由是否已经存在，不存在则添加
        if (!oldItem) {
            if (item.meta) {
                item.meta.icon = item.meta.icon || defaultRouteItemMeta.icon;
            }
            targetRouters.push(item);
            return;
        } else {
            
            // 合并子路由
            if (oldItem.children) {
                // 这里将合并后的子路由数据给item，是为了后面更新路由数据不被覆盖
                item.children = mergeRouters(oldItem.children, item.children);
            }

            // 更新当前对象值
            oldItem = Object.assign(oldItem, item);
        }
    });

    // 菜单进行排序
    targetRouters = targetRouters.sort((item1, item2) => {
        // 返回负值，交互顺序。此次为正序排列
        return item2.orderBy < item1.orderBy;
    });

    return targetRouters;
}

/**
 * 将权限过滤权限、非权限路由。根据noPermission过滤
 * @param {Array} routerList 路由的数据列表
 */
function filterConstantRouters(routerList) {
    // 路由源数据为空，则不进行计算
    if (!routerList || routerList.length === 0) {
        return [];
    }

    const tempRoutes = [];
    routerList.forEach((item) => {
        // 选判断子节点是否存在未授权权限
        const children = filterConstantRouters(item.children);
        // 如果子节点存在无权限路由，则父级路由默认显示
        if (children && children.length > 0) {
            const tempItem = Object.assign({}, item, { children });
            tempRoutes.push(tempItem);
        } else if (item.noPermission === true) {
            tempRoutes.push(item);
        }

        // 将有权限的路由存储，用于与登录后权限匹配
        needPermissionRouter[item.name] = item;
    });

    return tempRoutes;
}

/**
 * 从后台返回的权限数据（菜单数据）在这里进行处理，组装成过滤后的路由
 * @param routerList 从接口获取的路由数据
 */
export function filterAsyncRoutes(routerList, settings) {
	const res = [];
	// 如果是前端做国际化，需要做权限过滤
	if(settings.isWebI18n){
		if (!settings.permissionKey) {
			console.error('开启权限过滤后，permissionKey字段不能为空！');
			return;
		}
		// 处理权限过滤的数据
		routerList.forEach((routerItem) => {
			const tmp = { ...routerItem };
			let code = tmp[settings.permissionKey];
			const routeItem = needPermissionRouter[tmp[settings.permissionKey]];
			
			if (routeItem) {
				// 处理排序
				if (settings.permissionOrderKey && tmp[settings.permissionOrderKey]) {
					routeItem['orderBy'] = +tmp[settings.permissionOrderKey];
				}
				// 处理图标
				if (settings.permissionIconKey && tmp[settings.permissionIconKey]) {
					if (routeItem.meta) {
						routeItem.meta['icon'] = tmp[settings.permissionIconKey];
					} else {
						routeItem['meta']['icon'] = tmp[settings.permissionIconKey];
					}
				}
	
				if (tmp.children && Array.isArray(tmp.children) && tmp.children.length > 0) {
					routeItem.children = filterAsyncRoutes(tmp.children, settings);
				}
				res.push(routeItem);
			}
		});
	}else{
		// 如果是后台做国际化，直接处理数据即可
		routerList.forEach((routeItem) => {
			if (routeItem) {
				// 处理图标
				if (routeItem.meta) {
					routeItem.meta['icon'] = routeItem[settings.permissionIconKey];
				} else {
					routeItem.meta = {};
					routeItem['meta']['icon'] = routeItem[settings.permissionIconKey];
				}
				if (routeItem.children && Array.isArray(routeItem.children) && routeItem.children.length > 0) {
					filterAsyncRoutes(routeItem.children, settings);
				}
				res.push(routeItem);
			}
		});
	}
    return res;
}

/**
 * 根据路由名称获取路由信息
 * @param {string} routerName 路由名称
 */
export function getRouter(routerName) {
    return needPermissionRouter[routerName];
}

// 处理layout，如果useLayout字段为true则绑定当前设置的layout
function layoutHandler(routerItem) {
    if (routerItem.useLayout) {
        routerItem.component = currentLayout;
    }
}

// 初始化路由数据
export function defaultRouterHandler(permissionSettings) {
    if (permissionSettings) {
        // constantRoutes = filterDefaultRouter(asyncRoutes);
        constantRoutes = filterConstantRouters(allRouters);
        // 需要权限过滤，则把无需权限过滤的数据放到constantRoutes中
        // Object.keys(defaultRouter).forEach(keys => {
        //     constantRoutes.push(defaultRouter[keys]);
        // });
    } else {
        // 如果不需要权限控制，则直接把asyncRoutes赋值给constantRoutes
        // constantRoutes = asyncRoutes;
        constantRoutes = allRouters;
    }
    resetRouter();
}

const createRouter = () => new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({
        y: 0
    }),
    routes: constantRoutes
});

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
    const newRouter = createRouter();
    router.matcher = newRouter.matcher; // reset router
}

const router = createRouter();

// 设置当前的layout，原则上只允许设置一次
export function setLayout(layoutName) {
    if (layoutName && Object.prototype.hasOwnProperty.call(layoutObject, layoutName)) {
        currentLayout = layoutObject[layoutName];
    }
}

// 合并模块的路由,合并前会检测数据中的useLayout字段，如果为true则绑定当前设置的layout
export function setModulesRouter(moduleName, moduleRouter) {
    if (moduleRouter && Array.isArray(moduleRouter) && moduleRouter.length > 0) {
        // 处理layout匹配，如果遇到字段useLayout=true,则配置layout
        // routerDataHandler(moduleRouter);
        // asyncRoutes = asyncRoutes.concat(moduleRouter);
        mergeRouters(moduleRouter, allRouters);
        // resetRouter();
    } else {
        console.error(
            `%c加载router的值不能为空`,
            'background: #4192d9; padding: 5px; color: #fff; border-radius: 5px'
        );
    }
}
export default router;
