<template>
    <div>
        <el-timeline>
            <el-timeline-item
                v-for="item in logList"
                :key="item.key"
                :hide-timestamp="true"
                color="#409eff"
            >
                <el-card>
                    <p>
                        【{{ item.operTime }}】 {{ item.operUserName }}
                        {{ item.operType }}
                    </p>
                </el-card>
            </el-timeline-item>
        </el-timeline>
        <div class="no-data" v-if="!logList.length">暂无操作记录</div>
    </div>
</template>
<script>
export default {
    name: 'OperationLog',
    props: {
        logList: {
            type: Array,
            default() {
                return [];
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.no-data {
    padding: 16px;
    text-align: center;
}
</style>
