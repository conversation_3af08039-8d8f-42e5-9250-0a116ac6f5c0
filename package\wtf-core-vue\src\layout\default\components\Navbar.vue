<template>
  <div class="navbar">
    <div class="left-menu">
		<hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
		<el-dropdown trigger="click" placement="bottom-start">
			<span class="el-dropdown-link">
				<span class="menu-name">功能导航</span>
				<i class="el-icon-caret-bottom" />
			</span>
			<el-dropdown-menu slot="dropdown">
				<el-dropdown-item class="function-menu" :style="{width:sidebar.opened ? 'calc(100vw - 227px)' : 'calc(100vw - 54px)' }">
				  <component :is="functionComponent.component"></component>
				</el-dropdown-item>
			</el-dropdown-menu>
		</el-dropdown>
	</div>
	<!-- hamburger -->
    <!-- <breadcrumb id="breadcrumb-container" class="breadcrumb-container" /> -->
    <div class="right-menu">
      <template v-if="device!=='mobile'">
        <!-- <header-search v-if="showOptions.search" id="header-search" class="right-menu-item" />

        <error-log class="errLog-container right-menu-item hover-effect" />

        <screenfull v-if="showOptions.full" id="screenfull" class="right-menu-item hover-effect" />

        <el-tooltip v-if="showOptions.rich" :content="$t('navbar.size')" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>

        <lang-select v-if="lang.enabled" class="right-menu-item hover-effect" /> -->

        <template v-for="item in navbarComponents">

          <header-search v-if="item.component==='search'" id="header-search" :key="item.key" class="right-menu-item" />
          <error-log v-else-if="item.component==='log'" :key="item.key" class="errLog-container right-menu-item hover-effect" />
          <screenfull v-else-if="item.component==='screenfull'" id="screenfull" :key="item.key" class="right-menu-item hover-effect" />
          <el-tooltip v-else-if="item.component==='size'" :key="item.key" :content="$t('navbar.size')" effect="dark" placement="bottom">
            <size-select id="size-select" class="right-menu-item hover-effect" />
          </el-tooltip>

          <lang-select v-else-if="item.component==='lang'" :key="item.key" class="right-menu-item hover-effect" />

          <component :is="item.component" v-else :key="item.key" class="right-menu-item hover-effect" />
        </template>
      </template>

      <el-dropdown v-if="isUserAvatar" class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <img v-if="avatar" :src="avatar" class="user-avatar">
          <svg-icon v-if="!avatar" icon-class="avatar" class="user-avatar user-avatar-svg" />
          <span class="user-name">{{ name || "" }}</span>
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item @click.native="changePWD">
            <span style="display:block;" for="changePWD">{{ $t('navbar.changePWD') }}</span>
          </el-dropdown-item>
          <el-dropdown-item @click.native="logout">
            <span style="display:block;" for="logout">{{ $t('navbar.logOut') }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import Vue from 'vue';
import { mapState } from 'vuex';

// 组件配置的默认项
const defaultNavbarComponents = [
    {
        component: 'log',
        show: false
    },
    {
        component: 'search'
    },
    {
        component: 'screenfull'
    },
    {
        component: 'size'
    },
    {
        component: 'lang'
    }
];

export default {
    data() {
        return {
            userAvatar: {
                isComponent: false,
                show: true
            }
        };
    },
    computed: {
        ...mapState({
            sidebar: state => state.app.sidebar,
            avatar: state => state.user.avatar,
            name: state => state.user.name,
            device: state => state.app.device
        }),
        isUserAvatar() {
            // 判断是否配置了用户头像，并且未配置不显示
            return this.$store.state.settings.navbar.filter((item) => {
                return item.component === 'avatar' && item.show !== false;
            });
        },
        navbarComponents() {
            var settings = this.$store.state.settings.navbar;
            const opts = [];

            // 第一遍，先处理未做配置的选项
            defaultNavbarComponents.forEach(defaultItem => {
                var isSetting = settings.some(v => v.component === defaultItem.component);
                if (!isSetting) {
                    opts.push({
                        key: `nvb${parseInt(Math.random() * 1000000)}`,
                        component: defaultItem.component
                    });
                }
            });

            // 处理项目中配置的选项。注意：core中的setting.js和项目中的setting.js的navbar配置是覆盖关系，不是合并关系
            settings.forEach(item => {
                // 如果配置用户头像，则单独处理
                if (item.component !== 'avatar' && item.show !== false) {
                    const obj = {
                        key: `nvb${parseInt(Math.random() * 1000000)}`,
                        component: item.component
                    };
                    if (typeof (item.component) === 'function') {
                        // 注册全局组件的名称。这里未做重名处理，建议使用key标识名称
                        const componentName = item.name || obj.key;
                        Vue.component(componentName, item.component);
                        obj.component = componentName;
                    }
                    opts.push(obj);
                }
            });

            return opts;
        },
		// 功能导航组件
		functionComponent(){
			var item = this.$store.state.settings.functionMenu;
			if(typeof (item.component) === 'function'){
				Vue.component(item.name, item.component);
			}
			return item;
		}
    },
    methods: {
        toggleSideBar() {
            this.$store.dispatch('app/toggleSideBar');
        },
        changePWD() {
            this.$eventBus.emit('changePWDEvent', this);
        },
        profile() {
            this.$eventBus.emit('profileEvent', this);
        },
        logout() {
            this.$eventBus.emit('logOutEvent', this);
        }
    }
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  position: relative;
  background: #262F3E;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
	.left-menu{
		display: flex;
		.el-dropdown{
			display: flex;
			align-items: center;
			color:#fff;
			cursor:pointer;
			.menu-name{
				display: inline-block;
				margin-right:10px;
			}
		}
	}
  .hamburger-container {
    height: 100%;
	display: flex;
	align-items: center;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;
	padding-left:22px;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    height: 100%;
	display: flex;
	align-items: center;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 5px;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        position: relative;
		margin-left:5px;
		display: flex;
		align-items: center;
        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .user-avatar-svg {
          color: #40c9c6;
        }

        .user-name {
            margin-left: 5px;
			font-size: 14px;
			font-weight: bold;
			color: #EAF0FF;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 15px;
          font-size: 12px;
          color:#ffffff;
        }
      }
    }
  }
}
.el-dropdown-menu.el-dropdown-menu--medium{
	.function-menu{
		max-height:500px;
		overflow-y:auto;
	}
	.el-dropdown-menu__item:focus, .el-dropdown-menu__item:not(.is-disabled):hover{
		background-color: #fff; 
    	color: #606266;
	}
}
</style>
