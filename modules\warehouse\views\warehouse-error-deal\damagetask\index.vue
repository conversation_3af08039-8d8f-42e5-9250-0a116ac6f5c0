<template>
    <div class="view">
        <div class="content">
            <snbc-base-table v-show="tabsConfig.activeName === '待处理'" ref="tableRef1" :table-config="tableConfig1">
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
            </snbc-base-table>
            <snbc-base-table v-show="tabsConfig.activeName === '查询'" ref="tableRef2" :table-config="tableConfig2">
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
            </snbc-base-table>
            <damage-task-info ref="damageTaskRef" />
            <damage-task-details ref="damageTaskDetailsRef" />
        </div>
    </div>
</template>

<script>
import DamageTaskDetails from './components/details.vue';
import DamageTaskInfo from './components/DamageTakInfo.vue';
import functions from 'frame/mixins/functions.js';
import SnbcTableTabs from 'warehouse/components/snbc-table/SnbcTableTabs.vue';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import commonItems from 'warehouse/common/form-items/common-items.js';
import { mapState } from 'vuex';

const damageTaskCode = {
    ...commonItems.input,
    name: '货损任务编号',
    modelKey: 'damageTaskCode'
};
const damageType = {
    ...commonItems.select,
    name: '货损类型',
    modelKey: 'damageType',
    elOptions: [
        { label: '损毁 ', value: '损毁' },
        { label: '丢失', value: '丢失' },
        { label: '货损维修 ', value: '货损维修' }
    ]
};
const assetCode = {
    ...commonItems.input,
    name: '产品序列号',
    modelKey: 'assetCode'
};
const associatedTaskType = {
    ...commonItems.select,
    name: '关联任务类型',
    modelKey: 'associatedTaskType',
    elOptions: [
        { label: '公司发货 ', value: '公司发货' },
        { label: '盘点提案处理', value: '盘点提案处理' },
        { label: '调拨', value: '调拨' },
        { label: '日常工作', value: '日常工作' },
        { label: '撤机任务', value: '撤机任务' }
    ]
};
const associatedTaskCode = {
    ...commonItems.input,
    name: '关联任务编号',
    modelKey: 'associatedTaskCode'
};
const warehouseCode = {
    component: 'SnbcFormWarehouseSelect',
    name: '区仓',
    modelKey: 'warehouseCode'
};
export default {
    name: 'DamageTask',
    components: {
        SnbcBaseTable,
        SnbcTableTabs,
        DamageTaskInfo,
        DamageTaskDetails
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 查询参数
            queryParams: {
                damageTaskCode: '',
                damageType: '',
                warehouseCode: '',
                assetCode: '',
                associatedTaskType: '',
                content: '',
                associatedTaskCode: ''
            },
            // 查询区域配置项
            queryConfig: {
                items: [damageTaskCode, damageType, assetCode, warehouseCode, associatedTaskType, associatedTaskCode]
            },
            elTableColumns: [
                { label: '序号', prop: 'index', show: true, minWidth: 50 },
                {
                    label: '货损任务编号',
                    prop: 'damageTaskCode',
                    show: true,
                    minWidth: 180,
                    renderMode: 'button',
                    elButtonAttrs: {
                        type: 'text'
                    },
                    handleClick: this.handleView
                },
                {
                    label: '区仓',
                    prop: 'warehouseName',
                    show: true,
                    minWidth: 180
                },
                {
                    label: '产品序列号',
                    prop: 'assetCode',
                    show: true,
                    minWidth: 120
                },
                {
                    label: '产品名称',
                    prop: 'productName',
                    show: true,
                    minWidth: 120
                },
                {
                    label: '货损类型',
                    prop: 'damageType',
                    show: true,
                    minWidth: 100
                },
                {
                    label: '关联任务类型',
                    prop: 'associatedTaskType',
                    show: true,
                    minWidth: 120
                },
                {
                    label: '关联任务编号',
                    prop: 'associatedTaskCode',
                    show: true,
                    minWidth: 160
                },
                {
                    label: '任务状态',
                    prop: 'taskState',
                    show: true,
                    minWidth: 80
                },
                {
                    label: '创建时间',
                    prop: 'createTime',
                    show: true,
                    minWidth: 120
                }
            ],
            // 标签页配置
            tabsConfig: {
                activeName: '待处理',
                tabItems: ['待处理', '查询'],
                handleTabClick: this.handleTabClick
            },
            // 待提交Table配置
            tableConfig1: {
                queryParams: {},
                // 查询区域配置项
                queryConfig: {},
                elTableColumns: [],
                queryApi: this.$service.warehouse.damageTask.list,
                operations: [
                    {
                        name: '编辑',
                        type: 'warning',
                        handleClick: this.handleEdit,
                        handleShow(row) {
                            return row.taskState === '待提交' || row.taskState === '驳回';
                        }
                    },

                    {
                        name: '提交',
                        handleClick: this.handleSubmit,
                        handleShow(row) {
                            return row.taskState === '待提交' || row.taskState === '驳回';
                        }
                    },
                    {
                        name: '审核',
                        type: 'primary',
                        handleClick: this.handleAudit,
                        handleShow(row) {
                            return row.taskState === '审核中';
                        }
                    }
                ]
            },
            // 已提交Table配置
            tableConfig2: {
                queryParams: {},
                // 查询区域配置项
                queryConfig: {},
                elTableColumns: [],
                queryApi: this.$service.warehouse.damageTask.listSearch,
                headerButtons: [
                    {
                        name: '导出',
                        type: 'primary',
                        handleClick: this.export,
                        permissionCode: 'HSDC'
                    }
                ],
                hooks: {
                    tableListHook: this.tableListHook
                }
            }
        };
    },
    computed: {
        ...mapState(['permission'])
    },
    mounted() {
        this.tableConfig1.elTableColumns = this.elTableColumns;
        this.tableConfig2.elTableColumns = this.elTableColumns;
        this.tableConfig1.queryParams = this.queryParams;
        this.tableConfig2.queryParams = this.queryParams;
        this.tableConfig1.queryConfig = this.queryConfig;
        this.tableConfig2.queryConfig = this.queryConfig;
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef1.handleQuery();
    },
    methods: {
        // 标签切换
        handleTabClick(tab) {
            if (tab.name === '查询') {
                this.$refs.tableRef2.handleQuery();
            } else {
                this.$refs.tableRef1.handleQuery();
            }
        },

        // 查看操作
        async handleView(row) {
            const res = await this.$service.warehouse.damageTask.info(row.damageTaskCode);
            const { code, message } = res;
            if (code !== '000000') {
                this.$tools.message.err(message || '获取货损丢失任务详情失败！');
                return;
            }
            this.$refs.damageTaskDetailsRef.openDialog(res.result);
        },
        // 编辑操作
        async handleEdit(row) {
            try {
                const res = await this.$service.warehouse.damageTask.info(row.damageTaskCode);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '获取货损丢失任务详情失败！');
                    return;
                }
                this.$refs.damageTaskRef.openDialog(res.result, 'edit');
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 编辑操作
        async handleSubmit(row) {
            try {
                const res = await this.$service.warehouse.damageTask.info(row.damageTaskCode);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '获取货损丢失任务详情失败！');
                    return;
                }
                this.$refs.damageTaskRef.openDialog(res.result, 'edit', true);
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 审核
        async handleAudit(row) {
            try {
                const res = await this.$service.warehouse.damageTask.info(row.damageTaskCode);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '获取货损丢失任务详情失败！');
                    return;
                }
                this.$refs.damageTaskRef.openDialog(res.result, 'audit');
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 导出
        async export() {
            const stream = await this.$service.warehouse.damageTask.exportList(this.tableConfig2.queryParams);
            if (stream.type === 'application/json') {
                const reader = new FileReader();
                reader.onload = (event) => {
                    const message = JSON.parse(reader.result).message || '系统异常';
                    this.$tools.message.err(message);
                };
                reader.readAsText(stream);
                return;
            }
            this.$tools
                .downloadExprotFile(stream, `货损丢失`, 'xlsx', 'xlsx')
                .then(() => {
                    this.$tools.message.suc('导出成功');
                })
                .catch((e) => {
                    this.$tools.message.err(e || '导出失败');
                });
        }
    }
};
</script>

<style lang="scss" scoped></style>
