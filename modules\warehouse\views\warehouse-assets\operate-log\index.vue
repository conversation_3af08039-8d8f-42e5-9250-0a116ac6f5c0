<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig"> </snbc-base-table>
        </div>
    </div>
</template>
<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import FormItems from 'warehouse/common/form-items/index.js';

const { assetCode, dateRange, input, select } = FormItems;

export default {
    name: 'OperateLog',
    components: {
        SnbcBaseTable
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        const { getOperationRecordList: listApi } = this.$service.warehouse.assets;
        return {
            listApi,
            tableConfig: {
                queryParams: {
                    assetCode: '',
                    dateRange: [],
                    associatedTaskCode: '',
                    operationType: ''
                },
                queryConfig: {
                    items: [
                        assetCode,
                        {
                            ...dateRange,
                            name: '发生时间',
                            modelKey: 'dateRange',
                            elDatePickerAttrs: {
                                'value-format': 'yyyy-MM-dd'
                            }
                        },
                        {
                            ...select,
                            name: '类型',
                            modelKey: 'operationType',
                            elOptions: [
                                { label: '入库', value: '入库' },
                                { label: '出库', value: '出库' }
                            ]
                        },
                        {
                            ...input,
                            name: '关联任务编号',
                            modelKey: 'associatedTaskCode'
                        }
                    ],
                    elFormAttrs: {
                        'label-width': '120px'
                    }
                },
                queryApi: listApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '产品序列号',
                        prop: 'assetCode',
                        show: true,
                        minWidth: 120,
                        renderMode: 'button',
                        elButtonAttrs: {
                            type: 'text'
                        },
                        handleClick: this.handleClickCell
                    },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '类型',
                        prop: 'operationType',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '发生时间',
                        prop: 'happenDate',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '关联任务名称',
                        prop: 'associatedTaskName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '关联任务编号',
                        prop: 'associatedTaskCode',
                        show: true,
                        minWidth: 180
                    },
                    {
                        label: '任务类型',
                        prop: 'taskType',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '修改申请编号',
                        prop: 'taskAdditionalCode',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '操作人',
                        prop: 'createUserName',
                        show: true,
                        minWidth: 120
                    }
                ]
            }
        };
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        Object.assign(this.tableConfig.queryParams, this.$route.query);
        this.$refs.tableRef.handleQuery();
    },
    methods: {
        handleClickCell(item) {
            this.$router.push({
                path: '/app/assets/operate-details',
                query: {
                    assetCode: item.assetCode
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped></style>
