<template>
    <div ref="container" class="dialog-container">
        <snbc-form
            class="form"
            ref="formRef"
            :form="infoForm"
            :config="formConfig"
        >
            <template #form-body>
                <div class="form-body">
                    <snbc-form-item
                        v-for="item in formItems"
                        :key="item.modelKey"
                        :config="item"
                    >
                    </snbc-form-item>
                </div>
            </template>
        </snbc-form>
        <div v-if="!['audit', 'detail'].includes(mode)" class="task-button">
            <el-button type="primary" @click="handleClickAdd">+ 新增</el-button>
        </div>
        <el-table
            v-if="!['audit', 'detail'].includes(mode)"
            :data="list"
            border
        >
            <el-table-column
                type="index"
                label="序号"
                width="50px"
            ></el-table-column>
            <el-table-column
                v-for="column in elTableColumns"
                v-bind="column.elTableColumnAttrs"
                :key="column.prop"
                :label="column.label"
                :prop="column.prop"
                :width="column.width"
                :min-width="column.minWidth"
            >
                <template slot-scope="scope">
                    <!-- 单元格内容渲染 -->
                    <template v-if="!column.renderMode">{{
                        scope.row[column.prop]
                    }}</template>
                    <template v-if="column.renderMode === 'input'">
                        <el-input
                            v-model="scope.row[column.prop]"
                            @blur="handleBlur(scope.row)"
                        ></el-input>
                    </template>
                    <template v-if="column.renderMode === 'datetime'">
                        <el-date-picker
                            v-model="scope.row[column.prop]"
                            type="datetime"
                            placeholder="请选择"
                            :value-format="'yyyy-MM-dd HH:mm:ss'"
                        ></el-date-picker>
                    </template>
                </template>
            </el-table-column>
            <el-table-column
                v-if="operations.length > 0"
                fixed="right"
                align="center"
                label="操作"
                width="120px"
            >
                <template slot-scope="scope">
                    <template v-for="(operation, index) in operations">
                        <el-button
                            :key="index"
                            :type="operation.type"
                            size="small"
                            @click.native.prevent="
                                operation.handleClick(scope.$index)
                            "
                        >
                            {{ operation.name }}
                        </el-button>
                    </template>
                </template>
            </el-table-column>
        </el-table>
        <el-table v-if="['audit', 'detail'].includes(mode)" :data="list" border>
            <el-table-column
                type="index"
                label="序号"
                width="80px"
            ></el-table-column>
            <el-table-column
                v-for="column in elTableColumns"
                :key="column.prop"
                :label="column.label"
                v-bind="column.elTableColumnAttrs"
                :prop="column.prop"
                :width="column.width"
            >
                <template slot-scope="scope">
                    <!-- 单元格内容渲染 -->
                    <template>{{ scope.row[column.prop] }}</template>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
import SnbcForm from 'warehouse/components/snbc-form/SnbcForm';
import SnbcFormItem from 'warehouse/components/snbc-form/SnbcFormItem.vue';
import {
    rules,
    addDialogConfigItems,
    editDialogConfigItems,
    examineDialogConfigItems
} from '../formItems';

const form = {
    sundryApplyCode: '',
    sundryApplyName: '',
    warehouseCode: '',
    accessType: '',
    applyState: '',
    applyType: '',
    applySource: '',
    associatedTaskCode: '',
    applyReason: ''
};

export default {
    name: 'BasicInfo',
    components: {
        SnbcForm,
        SnbcFormItem
    },
    props: {
        mode: {
            type: String,
            default() {
                return 'add';
            }
        },
        form: {
            type: Object,
            default() {
                return {
                    ...form
                };
            }
        }
    },
    data() {
        return {
            infoForm: {
                ...form
            },
            formConfig: {
                elFormAttrs: {
                    rules,
                    'label-width': '140px'
                }
            },
            list: [],
            operations: [
                {
                    name: '删除',
                    type: 'primary',
                    handleClick: this.handleDelete
                }
            ],
            elTableColumns: [
                {
                    label: '资产编码',
                    prop: 'assetCode',
                    renderMode: 'input',
                    minWidth: 180
                },
                {
                    label: '产品名称',
                    prop: 'productName',
                    minWidth: 200
                },
                {
                    label: '出入库时间',
                    prop: 'entryExitTime',
                    renderMode: 'datetime',
                    minWidth: 220
                }
            ],
            warehouseMap: {}
        };
    },
    computed: {
        formItems() {
            const filterItem = (arr) => {
                return arr.filter(
                    (item) =>
                        this.infoForm.applySource !== '日常工作' ||
                        item.modelKey !== 'associatedTaskCode'
                );
            };
            if (this.mode === 'add') {
                return this.formItemsHandler(filterItem(addDialogConfigItems));
            }
            if (['audit', 'detail'].includes(this.mode)) {
                return this.formItemsHandler(
                    filterItem(examineDialogConfigItems)
                );
            }
            return this.formItemsHandler(filterItem(editDialogConfigItems));
        }
    },
    watch: {
        form: {
            handler(newVal) {
                if (!newVal) return;
                Object.assign(this.infoForm, newVal);
                newVal.sundryEntryExitInfoDomainList.forEach((item) => {
                    this.list.push({ ...item });
                });
            },
            deep: true
        }
    },
    methods: {
        // form表单选项处理
        formItemsHandler(formItems) {
            return formItems.map((item) => {
                return {
                    modelObj: this.infoForm,
                    elFormItemAttrs: {
                        label: item.name,
                        ...(item.elFormItemAttrs || {})
                    },
                    ...item
                };
            });
        },
        // 通过资产编码获取产品名称
        async getProductNameByAssetCode(assetCode) {
            if (!assetCode) return;
            const {
                code,
                result: { assetInfoPO },
                message
            } = await this.$service.warehouse.assets.getOperationRecordDetails(
                assetCode
            );
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
            }
            if (!assetInfoPO) {
                this.$tools.message.err('未获取到产品名称');
            }
            return assetInfoPO;
        },
        async getFormValidate() {
            if (
                !this.list.every((item) => item.entryExitTime && item.assetCode)
            ) {
                this.$tools.message.err('存在资产编码和出入库时间未填写');
                return Promise.reject(new Error('fail'));
            }
            return await this.$refs.formRef.getFormRef().validate();
        },
        async handleBlur(row) {
            const assetObj = await this.getProductNameByAssetCode(
                row.assetCode
            );
            Object.assign(row, assetObj);
        },
        // 获取 form
        getForm() {
            if (this.infoForm.applySource === '日常工作') {
                this.infoForm.associatedTaskCode = null;
            }
            return {
                ...this.infoForm,
                sundryEntryExitInfoDomainList: this.list
            };
        },
        resetForm() {
            this.$refs.container.scrollTo(0, 0);
            this.$refs.formRef.getFormRef().resetFields();
            this.list.splice(0, Number.MAX_SAFE_INTEGER);
        },
        handleClickAdd() {
            this.list.push({
                assetCode: '',
                productName: '',
                entryExitTime: ''
            });
        },
        handleDelete(index) {
            this.list.splice(index, 1);
        }
    }
};
</script>
<style lang="scss" scoped>
.dialog-container {
    max-height: 400px;
    overflow-y: auto;
    padding-right: 16px;
    .task-button {
        display: flex;
        flex-direction: row-reverse;
        flex-wrap: nowrap;
        margin-bottom: 16px;
    }
}
</style>
