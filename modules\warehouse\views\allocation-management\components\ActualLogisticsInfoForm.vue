<template>
    <div>
        <logistics-source-form
            ref="logisticsSourceFormRef"
            :form="form"
            :isDetails="isDetails"
            :mileage="mileage"
        />
        <snbc-form
            ref="actualLogisticsFormRef"
            :form="actualLogisticsForm"
            :config="actualLogisticsFormConfig"
        >
            <template #form-body>
                <div class="form-body">
                    <snbc-form-item
                        v-for="item in actualLogisticsFormItems"
                        :key="item.modelKey"
                        :config="item"
                    />
                </div>
            </template>
        </snbc-form>
    </div>
</template>
<script>
import LogisticsSourceForm from './LogisticsSourceForm.vue';
import SnbcForm from 'warehouse/components/snbc-form/SnbcForm.vue';
import SnbcFormItem from 'warehouse/components/snbc-form/SnbcFormItem.vue';
import FormRules from 'warehouse/common/form-rules/index.js';
import { settlementParty, settlementPrice } from '../formItems';

const { selectRequired } = FormRules;

const rules = {
    settlementParty: [selectRequired('结算方')]
};

const form = {
    allocateTaskCode: '',
    transportMode: '',
    logisticsName: '',
    logisticsCode: '',
    logisticsTel: '',
    logisticsPerson: '',
    mileage: '',
    carType: '',
    cubicNumber: '',
    trunkFee: 0,
    unitPrice: 0,
    deliveryFee: 0,
    packFee: 0,
    totalFee: 0,
    feeDesc: 0,
    handingFee: 0,
    otherFee: 0,
    settlementParty: '',
    settlementPrice: ''
};

export default {
    name: 'ActualL',
    components: {
        SnbcForm,
        SnbcFormItem,
        LogisticsSourceForm
    },
    props: {
        isDetails: {
            type: Boolean,
            default() {
                return false;
            }
        },
        form: {
            type: Object,
            default() {
                return {
                    ...form
                };
            }
        },
        mileage: {
            type: Number,
            default() {
                return 0;
            }
        }
    },
    data() {
        return {
            actualLogisticsForm: {
                settlementParty: '',
                settlementPrice: ''
            },
            actualLogisticsFormConfig: Object.freeze({
                elFormAttrs: {
                    'label-width': '140px',
                    rules
                }
            }),
            actualLogisticsFormItems: []
        };
    },
    watch: {
        form: {
            handler(newVal, oldVal) {
                Object.keys(newVal).forEach((key) => {
                    if (Object.hasOwn(this.actualLogisticsForm, key)) {
                        this.actualLogisticsForm[key] = newVal[key];
                    }
                });
            },
            deep: true,
            immediate: true
        },
        isDetails: {
            handler: 'init',
            immediate: true
        }
    },
    methods: {
        // 表单初始化
        init() {
            const attrsDetails = {
                elInputAttrs: {
                    disabled: true
                },
                elSelectAttrs: {
                    disabled: true
                },
                elInputNumberAttrs: {
                    disabled: true,
                    controls: false
                }
            };
            const attrsEdit = {
                elInputNumberAttrs: {
                    min: 0,
                    controls: false,
                    precision: 2,
                    max: Math.pow(10, 6) - 0.01
                }
            };
            const attrs = this.isDetails ? attrsDetails : attrsEdit;
            this.actualLogisticsFormItems = this.actualFormItemsHandler(attrs);
        },
        // 实际物流信息 重置表单
        resetForm() {
            this.$refs.logisticsSourceFormRef.resetForm();
            this.$refs.actualLogisticsFormRef.getFormRef().resetFields();
        },
        // 实际物流信息 校验
        async validateForm() {
            await Promise.all([
                this.$refs.actualLogisticsFormRef.getFormRef().validate(),
                this.$refs.logisticsSourceFormRef.getFormValidate()
            ]);
        },
        // 获取 form 对象
        getForm() {
            if (!this.actualLogisticsForm.settlementPrice) {
                this.actualLogisticsForm.settlementPrice = 0;
            }
            return Object.assign(
                {},
                this.$refs.logisticsSourceFormRef.getForm(),
                this.actualLogisticsForm
            );
        },
        // 实际物流信息表单处理
        actualFormItemsHandler(attrs) {
            return [settlementParty, settlementPrice].map((item) => {
                return {
                    modelObj: this.actualLogisticsForm,
                    elFormItemAttrs: {
                        label: item.name,
                        ...(item.elFormItemAttrs || {})
                    },
                    ...attrs,
                    ...item
                };
            });
        }
    }
};
</script>
<style lang="scss" scoped>
@import '../../../styles/mixins.scss';
.form-body {
    display: flex;
    flex-wrap: wrap;
    div {
        width: 50%;
    }
    @include form-item-width;
}
</style>
