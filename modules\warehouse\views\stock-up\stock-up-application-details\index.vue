<template>
    <div class="view">
        <div class="content">
            <!-- 备货基础信息 -->
            <snbc-card title="备货基础信息" buttonName="返回" @action="goBack">
                <template #card-body>
                    <snbc-descriptions :items="basicInfo" />
                </template>
            </snbc-card>
            <!-- 备货详细信息 -->
            <snbc-card title="备货详情信息">
                <template #card-body>
                    <snbc-table-list :list="list" :config="tableConfig" />
                </template>
            </snbc-card>
        </div>
    </div>
</template>
<script>
import functions from 'frame/mixins/functions.js';
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import SnbcDescriptions from 'warehouse/components/snbc-descriptions/SnbcDescriptions.vue';
import SnbcTableList from 'warehouse/components/snbc-table/SnbcTableList.vue';

export default {
    name: 'StockUpApplicationDetails',
    components: {
        SnbcCard,
        SnbcDescriptions,
        SnbcTableList
    },
    mixins: [functions],
    data() {
        return {
            // 备货基础信息
            basicInfo: [
                { label: '备货申请任务编号', prop: 'stockRequestCode', value: '' },
                { label: '备货申请任务名称', prop: 'stockRequestName', value: '' },
                { label: '申请时间', prop: 'createTime', value: '' },
                { label: '申请人', prop: 'createUserName', value: '' },
                { label: '目标区仓', prop: 'directionWarehouseName', value: '' },
                { label: '备注', prop: 'remark', value: '' }
            ],
            // 详细信息列表
            list: [],
            tableConfig: {
                elTableColumns: [
                    {
                        label: '序号',
                        prop: 'index',
                        show: true,
                        minWidth: 80
                    },
                    {
                        label: '客户名称',
                        prop: 'customerName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '申请数量',
                        prop: 'requestNumber',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '核定数量',
                        prop: 'checkNumber',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '到货计划',
                        prop: 'expectedArrivalDate',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '申请原因',
                        prop: 'requestCause',
                        show: true,
                        minWidth: 120
                    }
                ]
            }
        };
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        // 查询详情
        this.queryDetails();
    },
    methods: {
        // 查询详情
        async queryDetails() {
            const params = this.$tools.cloneDeep(this.$route.query);
            try {
                const res = await this.$service.warehouse.stockUp.getDetails(params);
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                // 获取备货基本信息
                const basicInfo = result.stockRequest;
                this.basicInfo.map((item) => {
                    item.value = basicInfo[item.prop];
                    return item;
                });
                // 详情信息
                const list = result.stockRequestDetailList;
                // 添加序号
                list.map((item, index) => {
                    item.index = index + 1;
                    return item;
                });
                this.list = list;
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        goBack() {
            this.$router.go(-1);
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .el-descriptions-item__content {
    max-width: 500px;
}
</style>
