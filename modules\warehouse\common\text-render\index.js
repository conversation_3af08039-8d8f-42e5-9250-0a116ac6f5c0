import moment from 'moment';
import isNil from 'lodash/isNil';
import isNaN from 'lodash/isNaN';
import trim from 'lodash/trim';

/**
 * 判断值是否是空，是的话返回占位符
 *
 * @param {String|Number} value 值
 * @param {String} placeholder 占位符
 * @returns {String|Number} 值或占位符
 */
function usePlacehoder(value, placeholder = '-') {
    if (isNaN(value) || isNil(value) || trim(value) === '') {
        return placeholder;
    }
    return value;
}

/**
 * 增加单位展示
 * @param {String|Number} value 值
 * @param {String} unit 单位
 * @param {String} placeholder 占位符
 * @returns {String} 追加单位字符串
 */
function unitRender(value, unit, placeholder) {
    return `${usePlacehoder(value, placeholder)}${unit || ''}`;
}

/**
 * 格式化日期
 * @param {*} value 日期
 * @param {String} format 格式
 * @returns {String} 格式化日期，非法时返回原值
 */
function dateRender(value, format = 'YYYY-MM-DD') {
    const date = moment(value);
    return date.isValid() ? date.format(format) : value;
}

export default {
    unitRender,
    dateRender
};
