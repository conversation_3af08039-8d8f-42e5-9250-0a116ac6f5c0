import WtfCoreVue from '../src/main.js'
// import admincraftShowcase from 'admincraft-showcase'
// import admincraftUI from 'admincraft-ui'
// Admincraft.add(admincraftUI)
// Admincraft.add(admincraftShowcase)
const el = document.createElement('div')
document.body.appendChild(el)
const wtfCoreVue = new WtfCoreVue({
  title: 'wtf-core-vue',
  logo: {
    text: 'wtf-core-vue'
  },
  http: {
    config: {},
    interceptor: context => {
      return {
        response: {
          success: response => {
            return response.data
          }
        }
      }
    }
  },
  router: {
    config: {},
    guards: context => {
      return {
        beforeResolve: (to, from, next) => {
          next()
        }
      }
    }
  }
})
wtfCoreVue.$mount(el)
