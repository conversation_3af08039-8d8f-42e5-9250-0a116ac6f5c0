<template>
    <el-form-item v-bind="elFormItemAttrs" class="distance-input">
        <el-input-number
            v-model="config.modelObj[config.modelKeyList[0]]"
            v-bind="elInputAttrs"
            @keyup.enter.native="handleEnter"
            :max="getMaxMinNumber(config.modelKeyList[1])"
            :placeholder="config.placeholder[0]"
        />
        <span class="separator">~</span>
        <el-input-number
            v-model="config.modelObj[config.modelKeyList[1]]"
            v-bind="elInputAttrs"
            @keyup.enter.native="handleEnter"
            :min="getMaxMinNumber(config.modelKeyList[0])"
            :placeholder="config.placeholder[1]"
        />
    </el-form-item>
</template>
<script>
export default {
    name: 'SnbcFormDistanceInput',
    props: {
        /**
         * SnbcFormDistanceInput组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    modelKeyList: [],
                    placeholder: [],
                    elFormItemAttrs: {},
                    elInputAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-input组件默认属性设置
            defaultElInputAttrs: {
                clearable: true,
                controls: false
            }
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-input组件应用属性
        elInputAttrs() {
            return {
                ...this.defaultElInputAttrs,
                ...(this.config.elInputAttrs || {})
            };
        },
        getMaxMinNumber() {
            return (key) => {
                return this.config.modelObj[key] || undefined;
            };
        }
    },
    methods: {
        // el-input组件回车操作
        handleEnter() {
            this.config.enterHandler && this.config.enterHandler();
        }
    }
};
</script>
<style lang="scss" scoped>
.distance-input {
    ::v-deep .separator {
        display: inline-block;
        margin: 0 10px;
    }
    ::v-deep .el-input__inner {
        text-align: start;
    }
}
</style>
