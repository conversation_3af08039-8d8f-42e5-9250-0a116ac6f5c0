/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        warehouseEntryAndExitRules: {
            /**
             * 新增
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            add(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/warehouse/entry_and_exit_rules/save/0',
                    method: 'post',
                    data
                });
            },
            /**
             * 编辑
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            edit(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/warehouse/entry_and_exit_rules/save/1',
                    method: 'post',
                    data
                });
            },
            /**
             * 启用或停用
             * @param {String} id id
             * @param {String} ruleState 规则状态
             * @returns {Promise} http请求
             */
            editState(id, ruleState) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/warehouse/entry_and_exit_rules/start_or_stop`,
                    method: 'get',
                    params: {
                        id,
                        ruleState
                    }
                });
            },
            /**
             * 列表查询
             * @param {Object} data 参数对象
             * @returns {Promise} http请求
             */
            list(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/warehouse/entry_and_exit_rules/query_entry_and_exit_rules_list',
                    method: 'post',
                    data
                });
            },
            /**
             * 查询某区仓下有准入产品的客户的列表
             * @param {String} warehouseCode 区仓编码
             * @returns {Promise} http请求
             */
            getAllAccessCustomer(warehouseCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/warehouse/entry_and_exit_rules/query_all_access_customer`,
                    method: 'get',
                    params: {
                        warehouseCode
                    }
                });
            },
            /**
             * 查询某区仓下有准入产品的客户的列表
             * @param {String} warehouseCode 区仓编码
             * @param {String} customerName 客户名称
             * @returns {Promise} http请求
             */
            getAllAccessProductName(warehouseCode, customerName) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/warehouse/entry_and_exit_rules/query_all_access_product_name`,
                    method: 'get',
                    params: {
                        warehouseCode,
                        customerName
                    }
                });
            }
        }
    };

    return service;
};
