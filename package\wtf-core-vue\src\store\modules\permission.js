import router, { constantRoutes, asyncRoutes, filterAsyncRoutes, getRouter, mergeRouters } from 'wtf-core-vue/src/router';

const state = {
    routes: [],
    addRoutes: [],
    btnDatas: []
};

const mutations = {
    SET_ROUTES: (state, params) => {
        state.addRoutes = params.routes;
		if (params.permissionSettings) {
			const temp = [];
			mergeRouters(constantRoutes.concat(params.routes), temp);
			state.routes = temp;
			
		} else {
			state.routes = params.routes;
		}
    },
    UPDATE_ROUTE: (state, params) => {
        if (params.permissionSettings) {
            // 取得传进来的route对象
            const newRoute = params.route;
            // 取得settings对象
            const settings = params.permissionSettings;
            // 取得当前更新的路由对象
            const updateRoute = getRouter(params.route[settings.permissionKey]);
            if (!newRoute || !settings || !updateRoute) {
                return;
            }
            // 处理排序
            if (settings.permissionOrderKey && newRoute[settings.permissionOrderKey]) {
                updateRoute['orderBy'] = +newRoute[settings.permissionOrderKey];
            }
            // 处理图标
            if (settings.permissionIconKey && newRoute[settings.permissionIconKey]) {
                if (updateRoute.meta) {
                    updateRoute.meta['icon'] = newRoute[settings.permissionIconKey];
                } else {
                    updateRoute['meta']['icon'] = newRoute[settings.permissionIconKey];
                }
            }
        }
    },
    SET_BTNPERMISSION_DATA: (state, params) => {
        // 存储权限按钮数据
        state.btnDatas = params;
    }
};

const actions = {
    addRoutes({ commit }, accessedRoutes) {
        commit('SET_ROUTES', accessedRoutes);
    },
    updateRoute({ commit, rootState }, route) {
        commit('UPDATE_ROUTE', {
            'route': route,
            'permissionSettings': rootState.settings.permissionSettings
        });
    },
    generateRoutes({ commit, rootState }, permissionData) {
        // 如果是超级用户，则不判断权限，可以在此处增加处理。此处处理权限过滤，生成menu数据和路由数据
        let permissionRouterList = [];
		// 如果是前端做国际化，则根据是否设置权限进行路由组装，否则直接组装数据即可
		if(rootState.settings.isWebI18n){
			if (rootState.settings.permissionSettings) {
				permissionRouterList = filterAsyncRoutes(permissionData, rootState.settings);
				router.addRoutes(permissionRouterList);
				
			} else {
				permissionRouterList = asyncRoutes;
			}
			commit('SET_ROUTES', {
				'routes': permissionRouterList,
				'permissionSettings': rootState.settings.permissionSettings
			});
		}else{
			permissionRouterList = filterAsyncRoutes(permissionData, rootState.settings);
			commit('SET_ROUTES', {
				'routes': permissionRouterList,
				'permissionSettings': rootState.settings.permissionSettings
			});
		}
		
        // router.addRoutes(accessedRoutes)
    },
    btnPermissionData({ commit }, btnDatas) {
        // 设置权限数据
        commit('SET_BTNPERMISSION_DATA', btnDatas);
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
