# wtf-core-vue

wtf-core-vue是一个支持模块化分布部署和加载的vue admin。包含了业务项目中必备的核心功能和辅助功能。

## 安装

```
npm install wtf-core-vue -save
```

## 快速开始

``` javascript

// 导入wtf-core-vue构造函数
import WtfCoreVue from 'wtf-core-vue'
// 创建挂载DOM
let appElement = document.createElement('div')
document.body.appendChild(appElement)
// 实例化
let wtfCoreVue = new WtfCoreVue()
wtfCoreVue.$mount(appElement)

```

## 相关文档

- [开发手册/指南](https://dev-docs.newbeiyang.cn/)

## 变更日志

### v2.1.0(bbpf2.0.0版本发布后变更)

- 新增`font-awesome`图标支持
- 新增框架的navbar(右上角图标功能区)支持自定义功能
- 优化打包文件大小，包大小缩进2.5M左右