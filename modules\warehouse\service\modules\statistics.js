/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    const { formatStartDate, formatEndDate } = Vue.prototype.$tools;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        statistics: {
            /**
             * 区仓每日统计信息列表查询
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getDailyList(data) {
                data.startStatisticsDate = formatStartDate(data.requestDate[0]);
                data.endStatisticsDate = formatEndDate(data.requestDate[1]);
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/statistics/asset/daily_list',
                    method: 'post',
                    data
                });
            },
            /**
             * 区仓每日统计信息导出
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            exportDailyStatistics(data) {
                data.startStatisticsDate = formatStartDate(data.requestDate[0]);
                data.endStatisticsDate = formatEndDate(data.requestDate[1]);
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/statistics/asset/export_daily_list',
                    method: 'post',
                    responseType: 'blob',
                    data
                });
            },
            /**
             * 区仓每月统计信息列表查询
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getMonthlyList(data) {
                data.startStatisticsDate = data.requestDate[0];
                data.endStatisticsDate = data.requestDate[1];
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/statistics/asset/monthly_list',
                    method: 'post',
                    data
                });
            },
            /**
             * 区仓每月统计信息导出
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            exportMonthlyStatistics(data) {
                data.startStatisticsDate = data.requestDate[0];
                data.endStatisticsDate = data.requestDate[1];
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/statistics/asset/export_monthly_list',
                    method: 'post',
                    responseType: 'blob',
                    data
                });
            },
            /**
             * 区仓每月统计信息汇总
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getMonthlyStatistics(data) {
                data.startStatisticsDate = data.requestDate[0];
                data.endStatisticsDate = data.requestDate[1];
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/statistics/asset/monthly_statistics',
                    method: 'post',
                    data
                });
            },
            /**
             * 核心指标历史趋势-区仓信息查询
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getTendencyMessage(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/statistics/asset/tendency_message',
                    method: 'post',
                    data
                });
            },
            /**
             * 核心指标历史趋势-指标对比
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getIndexComparison(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/statistics/asset/index_comparison',
                    method: 'post',
                    data
                });
            },
            /**
             * 核心指标历史趋势-货账相符率
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getAccuracyRate(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/statistics/asset/accuracyRate',
                    method: 'post',
                    data
                });
            },
            /**
             * 核心指标历史趋势-区仓面积和流量
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getAreaAndFlow(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/statistics/asset/area_and_flow',
                    method: 'post',
                    data
                });
            },
            /**
             * 核心指标历史趋势-面积利用率
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getAreaUtilizationRate(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/statistics/asset/area_utilization_rate',
                    method: 'post',
                    data
                });
            },
            /**
             * 核心指标历史趋势-出入库及时率
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getBusinessComplianceRate(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/statistics/asset/businessComplianceRate',
                    method: 'post',
                    data
                });
            },
            /**
             * 区仓指标查询-查询列表
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getIndicatorQueryList(data) {
                data.startStatisticsDate = data.requestDate[0];
                data.endStatisticsDate = data.requestDate[1];
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/statistics/asset/warehouse_statistics_List',
                    method: 'post',
                    data
                });
            },
            /**
             * 区仓指标查询-数据导出
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            exportIndicatorQueryList(data) {
                data.startStatisticsDate = data.requestDate[0];
                data.endStatisticsDate = data.requestDate[1];
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/statistics/asset/export_warehouse_statistics',
                    method: 'post',
                    responseType: 'blob',
                    data
                });
            },
            /**
             * 区仓指标查询-统计
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getIndicatorQueryStatistics(data) {
                data.startStatisticsDate = data.requestDate[0];
                data.endStatisticsDate = data.requestDate[1];
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/statistics/asset/warehouse_statistics',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
