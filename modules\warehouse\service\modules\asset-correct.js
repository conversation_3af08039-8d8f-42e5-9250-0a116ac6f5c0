import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();
    const service = {
        assetCorrect: {
            create(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/asset_correct/create_commit',
                    method: 'post',
                    data
                });
            },
            examine(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/asset_correct/confirm',
                    method: 'post',
                    data
                });
            },
            remove(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/asset_correct/remove`,
                    method: 'post',
                    data,
                    params: {
                        id: data.id
                    }
                });
            },
            edit(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/asset_correct/update',
                    method: 'post',
                    data
                });
            },
            detail(applyCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/asset_correct/detail',
                    method: 'post',
                    data: { applyCode }
                });
            },
            getInOutRecord(associatedTaskCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/asset_correct/in_out_record',
                    method: 'get',
                    params: { associatedTaskCode }
                });
            },
            list(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/asset_correct/search_todo_list',
                    method: 'post',
                    data
                });
            },
            listSearch(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/asset_correct/search_show_list',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
