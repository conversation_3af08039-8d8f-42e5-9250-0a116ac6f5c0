<template>
    <snbc-base-dialog ref="snbcBaseDialogRef" :config="dialogConfig" @confirm="handleSubmit">
        <template slot="dialog-body">
            <snbc-form ref="snbcFormRef" :form="form" :config="formConfig">
                <template slot="form-body">
                    <snbc-form-item
                        v-for="(item, index) in formItems"
                        :key="index"
                        :config="item"
                        @validate="handleValidate"
                    />
                </template>
            </snbc-form>
        </template>
    </snbc-base-dialog>
</template>
<script>
import SnbcBaseDialog from 'warehouse/components/snbc-dialog/SnbcBaseDialog.vue';
import SnbcForm from 'warehouse/components/snbc-form/SnbcForm.vue';
import SnbcFormItem from 'warehouse/components/snbc-form/SnbcFormItem.vue';

export default {
    name: 'FeeFunctionAliasDialog',
    components: {
        SnbcBaseDialog,
        SnbcForm,
        SnbcFormItem
    },
    props: {
        /**
         * FeeFunctionAliasDialog组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    rules: [],
                    items: []
                };
            }
        }
    },
    data() {
        return {
            // 表单对象
            form: {},
            // add or edit
            mode: '',
            // 弹窗属性
            elDialogAttrs: {}
        };
    },
    computed: {
        // 表单项数组
        formItems() {
            return (this.config.items || []).map((item) => {
                // 扩展表单项的配置
                return this.expandItem(item);
            });
        },
        // 表单配置
        formConfig() {
            return {
                elFormAttrs: {
                    'label-width': this.config['label-width'] || '120px',
                    'rules': this.config.rules
                }
            };
        },
        // 弹窗配置
        dialogConfig() {
            return {
                elDialogAttrs: this.elDialogAttrs
            };
        }
    },
    methods: {
        // 基本新增弹窗
        baseAddDialog(data, title) {
            this.openDialog('add', data, { title });
        },
        // 基本编辑弹窗
        baseEditDialog(data, title) {
            this.openDialog('edit', data, { title });
        },
        // 弹窗打开
        openDialog(mode, form, elDialogAttrs) {
            this.mode = mode;
            this.form = form;
            this.elDialogAttrs = elDialogAttrs || {};
            this.$nextTick(() => {
                // 重置校验及提示
                const { snbcFormRef } = this.$refs;
                if (snbcFormRef) {
                    const formRef = snbcFormRef.getFormRef();
                    formRef.clearValidate();
                }
            });
            this.$refs.snbcBaseDialogRef.openDialog();
        },
        hideDialog() {
            this.$refs.snbcBaseDialogRef.hideDialog();
        },
        // 提交表单操作
        async handleSubmit() {
            await this.$tools.validateForm(this.$refs.snbcFormRef.getFormRef());
            this.$emit('submit', {
                mode: this.mode,
                form: this.form
            });
        },
        // 表单校验
        handleValidate(prop) {
            const formRef = this.$refs.snbcFormRef.getFormRef();
            if (prop) {
                this.$tools.validateField(formRef, prop);
            } else {
                this.$tools.validateForm(formRef);
            }
        },
        // 不同表单项执行不同的配置扩展
        expandItem(item) {
            switch (item.component) {
                default:
                    return this.expandSnbcFormCommon(item);
            }
        },
        // 扩展组件属性
        expandSnbcFormCommon(item) {
            return {
                modelObj: this.form,
                elFormItemAttrs: {
                    label: item.name,
                    ...(item.elFormItemAttrs || {})
                },
                ...item
            };
        }
    }
};
</script>
<style lang="scss" scoped></style>
