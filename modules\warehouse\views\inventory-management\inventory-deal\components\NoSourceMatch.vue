<template>
    <el-dialog
        class="custom-dialog query-label-line2"
        :visible.sync="dialogVisible"
        v-bind="elDialogAttrs"
        width="1200px"
        @close="hideDialog"
    >
        <template>
            <div>
                <span style="font-size: 20px; font-weight: bold"
                    >产品名称：{{ productNameShow }}</span
                >
                <br />
                <span style="font-size: 20px; font-weight: bold"
                    >产品序列号：{{ assetCodeShow }}</span
                >
            </div>
            <el-divider></el-divider>

            <snbc-card
                style="
                    max-height: 500px;
                    overflow-y: auto;
                    display: block;
                    width: 98%;
                "
                :title="'图片显示'"
                ><template #card-body
                    ><el-image
                        style="width: 300px; height: 300px; padding-right: 8px"
                        v-for="url in urls"
                        :src="url"
                        :key="url"
                    >
                    </el-image
                ></template>
            </snbc-card>
            <el-divider></el-divider>
            <div>
                <snbc-card
                    style="
                        max-height: 500px;
                        overflow-y: auto;
                        display: block;
                        width: 98%;
                    "
                    :title="'匹配列表'"
                >
                    <template #card-body>
                        <el-select
                            v-model="value"
                            :clearable="true"
                            filterable
                            size="medium"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                        <span
                            style="
                                color: red;
                                font-size: 25px;
                                padding-left: 8px;
                                vertical-align: middle;
                            "
                            >*</span
                        >
                    </template>
                </snbc-card>
            </div>
            <el-divider></el-divider>
            <div>
                <p style="font-size: 20px; font-weight: bold">备注信息：</p>
                <el-input
                    type="textarea"
                    v-model="input"
                    placeholder="请输入备注信息"
                    maxlength="255"
                    show-word-limit
                    style="width: 98%"
                ></el-input>
                <span
                    style="
                        color: red;
                        font-size: 25px;
                        padding-left: 8px;
                        vertical-align: middle;
                    "
                    >*</span
                >
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="hideDialog">取 消</el-button>
                <el-button type="primary" @click="handleSave">确 定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script>
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import elAttrs from 'warehouse/common/el-attrs/index.js';

export default {
    name: 'BatchQuery',
    components: {
        SnbcCard
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 统一UI风格
            elAttrs,
            elDialogAttrs: elAttrs.elDialogAttrs,

            urls: [],
            input: '',
            assetCodeShow: '',
            productNameShow: '',
            options: [],
            warehouseCode: '',
            value: '',
            planCode: '',
            tastCode: '',
            // 弹窗显示
            dialogVisible: false,

            // 缺少物料信息表格配置
            batchTableConfig: {
                elTableColumns: [
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '实际所在区仓',
                        prop: 'actualWarehouseName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '系统所在区仓',
                        prop: 'systemWarehouseName',
                        show: true,
                        minWidth: 100
                    },

                    {
                        label: '产品序列号',
                        prop: 'assetCode',
                        show: true,
                        minWidth: 200
                    },
                    {
                        label: '调拨名称',
                        prop: 'allocateName',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '出入库类型',
                        prop: 'type',
                        show: true,
                        minWidth: 100
                    }
                ],
                operations: [
                    {
                        name: '删除',
                        type: 'danger',
                        width: 150,
                        handleClick: this.handleDelete
                    }
                ]
            }
        };
    },

    methods: {
        // 展示弹窗
        openDialog(row, result) {
            this.elDialogAttrs = {
                ...elAttrs.elDialogAttrs,
                title: '匹配无源合并'
            };
            this.urls = row.notFoundImageList;

            this.options = result.map((item) => {
                return {
                    ...item,
                    label: item.assetCode,
                    value: item.assetCode
                };
            });
            this.assetCodeShow = row.assetCode;
            this.productNameShow = row.productName;
            this.planCode = row.planCode;
            this.taskCode = row.taskCode;
            this.warehouseCode = row.warehouseCode;
            this.dialogVisible = true;
        },

        // 隐藏弹窗
        hideDialog() {
            this.value = '';
            this.input = '';
            this.dialogVisible = false;
        },
        async handleSave() {
            if (this.input.length < 4) {
                this.$tools.message.err('备注需要输入超过4个字符');
                return;
            }
            if (this.input.length > 255) {
                this.$tools.message.err('备注最多输入255个字符');
                return;
            }
            if (!this.value) {
                this.$tools.message.err('请选择匹配列表');
                return;
            }
            try {
                const { message, code } =
                    await this.$service.warehouse.inventoryManagement.bindNotFoundAsset(
                        {
                            newAssetCode: this.assetCodeShow,
                            oldAssetCode: this.value,
                            remark: this.input,
                            planCode: this.planCode,
                            taskCode: this.taskCode,
                            warehouseCode: this.warehouseCode
                        }
                    );
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.hideDialog();
                this.$emit('fresh');
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        handleDelete(row) {
            this.result = this.result.filter((item) => {
                return item.assetCode !== row.assetCode;
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
