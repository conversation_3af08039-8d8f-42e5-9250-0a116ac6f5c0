<template>
  <div id="tags-view-container" class="tags-view-container">
    <div ref="scrollContainer" class="scroll-container tags-view-wrapper">
      <div class="btn btn-left" @click="moveToRight">
        <i class="el-icon-arrow-left" />
      </div>
      <div ref="tagsViewBox" class="tags-view--item-box">
        <div ref="tagsViewScroll" class="tags-view-item-scroll">
          <router-link
            v-for="tag in visitedViews"
            ref="tag"
            :key="tag.path"
            :class="isActive(tag)?'active':''"
            :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
            tag="span"
            class="tags-view-item"
            @click.native="handleClickTag(tag)"
            @click.middle.native="!isAffix(tag)?closeSelectedTag(tag):''"
            @contextmenu.prevent.native="openMenu(tag,$event)"
          >

            <i
              class="sub-el-icon"
              :class="{'circle':tag.status === 0,'el-icon-refresh-left':tag.status === 1,'el-icon-loading':tag.status === 2}"
              @click.stop="tag.status === 1 && refreshSelectedTag(tag)"
              @mouseenter="enterStatus(tag)"
              @mouseleave="leaveStatus(tag)"
            />
            <span>{{ generateTitle(tag.title) }}</span>
            <span v-if="!isAffix(tag)" class="el-icon-close" @click.prevent.stop="closeSelectedTag(tag)" />
          </router-link>
        </div>
      </div>
      <div class="btn btn-right" @click="moveToLeft">
        <i class="el-icon-arrow-right" />
      </div>
    </div>
    <ul v-show="visible" :style="{left:left+'px',top:top+'px'}" class="contextmenu">
      <!-- <li @click="refreshSelectedTag(selectedTag)">{{ $t('tagsView.refresh') }}</li> -->
      <!-- <li v-if="!isAffix(selectedTag)" @click="closeSelectedTag(selectedTag)">{{ $t('tagsView.close') }}</li> -->
      <li @click="closeOthersTags">{{ $t('tagsView.closeOthers') }}</li>
      <li @click="closeAllTags(selectedTag)">{{ $t('tagsView.closeAll') }}</li>
    </ul>
  </div>
</template>

<script>
import path from 'path';

// 记录当前非Error页面的路由地址
let currentRoute;

export default {
    data() {
        return {
            visible: false,
            top: 0,
            left: 0,
            selectedTag: {},
            affixTags: [],
            moveLeftIndex: 1

        };
    },
    computed: {
        visitedViews() {
            const visitedViews = this.$store.state.tagsView.visitedViews;
            visitedViews.forEach(item => {
                this.$set(item, 'status', 0);
            });
            return this.$store.state.tagsView.visitedViews;
        },
        routes() {
            return this.$store.state.permission.routes;
        }
    },
    watch: {
        $route() {
            if (!this.isErrorPage()) {
                currentRoute = this.$route;
            }
            this.addTags();
            this.moveToCurrentTag();
        },
        visible(value) {
            if (value) {
                document.body.addEventListener('click', this.closeMenu);
            } else {
                document.body.removeEventListener('click', this.closeMenu);
            }
        }
    },
    mounted() {
        this.initTags();
        this.addTags();
    },
    methods: {
        isActive(route) {
            const prevPath = this.isErrorPage() ? currentRoute.path : this.$route.path;
            return route.path === prevPath;
        },
        isAffix(tag) {
            return tag.meta && tag.meta.affix;
        },
        /**
         * 判断当前页是否为错误页面
         */
        isErrorPage(route) {
            return (route || this.$route).path.indexOf('/error/') >= 0;
        },
        filterAffixTags(routes, basePath = '/') {
            let tags = [];
            routes.forEach(route => {
                if (route.meta && route.meta.affix) {
                    const tagPath = path.resolve(basePath, route.path);
                    tags.push({
                        fullPath: tagPath,
                        path: tagPath,
                        name: route.name,
                        meta: { ...route.meta }
                    });
                }
                if (route.children) {
                    const tempTags = this.filterAffixTags(route.children, route.path);
                    if (tempTags.length >= 1) {
                        tags = [...tags, ...tempTags];
                    }
                }
            });
            return tags;
        },
        initTags() {
            const affixTags = this.affixTags = this.filterAffixTags(this.routes);
            for (const tag of affixTags) {
                // Must have tag name
                if (tag.name) {
                    this.$store.dispatch('tagsView/addVisitedView', tag);
                }
            }
        },
        addTags() {
            const { name } = this.$route;
            const isErrorPage = this.isErrorPage();
            // 当名称不为空，并且是非隐藏路由
			if(this.$route.name){
				//  除去当刷新的是当前路由的情况
				let index = this.$route.name.indexOf('redirect');
				if (index < 0 && !isErrorPage) {
					this.$store.dispatch('tagsView/addView', this.$route);
				}
			}
			
            return false;
        },
        moveToCurrentTag() {
            const tags = this.$refs.tag;
            this.$nextTick(() => {
                for (const tag of tags) {
                    if (tag.to.path === this.$route.path) {
                        // when query is different then update
                        if (tag.to.fullPath !== this.$route.fullPath) {
                            this.$store.dispatch('tagsView/updateVisitedView', this.$route);
                        }
                        break;
                    }
                }
            });
        },
		/**
		 * 点击刷新按钮操作事件
		 */
        refreshSelectedTag(tag) {
            tag.status = 2;
			//  从缓存池中将当前刷新的路由删掉
			this.$store.dispatch('tagsView/delCachedView', tag);
			// 判断当刷新的是当前路由时，组件需要重新加载
			if(tag.path === this.$route.path) {
				if(this.$route.query){
					this.$router.push({
						path: '/redirect' + tag.path,
						query:this.$route.query
					})
				}else{
					this.$router.push({
						path: '/redirect' + tag.path
					})
				}
			}else {
				this.$router.push({path: tag.path});
			}
			
            setTimeout(() => {
                tag.status = 0;
				//  将当前刷新的路由放入缓存池中
				this.$store.dispatch('tagsView/addView', tag);
            }, 1000);
        },
        closeSelectedTag(view) {
            this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {
                if (this.isActive(view) || this.isErrorPage()) {
                    this.toLastView(visitedViews, view);
                }
            });
        },
        closeOthersTags() {
            this.handleClickTag(this.selectedTag);
            this.$router.push(this.selectedTag);
            this.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {
                this.moveToCurrentTag();
            });
        },
        closeAllTags(view) {
            this.$store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {
                if (this.affixTags.some(tag => tag.path === view.path)) {
                    return;
                }
                this.toLastView(visitedViews, view);
            });
        },
        toLastView(visitedViews, view) {
            let latestView = visitedViews.slice(-1)[0];
            // 如果是error界面，则再次去最后一个
            if (this.isErrorPage(view)) {
                latestView = visitedViews.slice(-2)[1];
            }
			
            if (latestView) {
                this.handleClickTag(latestView);
                this.$router.push(latestView.fullPath);
            } else {
                // now the default is to redirect to the home page if there is no tags-view,
                // you can adjust it according to your needs.
                if (view.name === 'dashboard-index') {
                    // to reload home page
                    this.$router.replace({ path: '/redirect' + view.fullPath });
                } else {
					// 如果点击到最后一个了，则展示首页
					if(visitedViews.length === 0){
						this.$router.push({path:'/app/dashboard-index'});
					}else{
						this.$router.push('/');
					}
                    
                }
            }
			
			
        },
        openMenu(tag, e) {
            const menuMinWidth = 105;
            const offsetLeft = this.$el.getBoundingClientRect().left; // container margin left
            const offsetWidth = this.$el.offsetWidth; // container width
            const maxLeft = offsetWidth - menuMinWidth; // left boundary
            const left = e.clientX - offsetLeft + 15; // 15: margin right

            if (left > maxLeft) {
                this.left = maxLeft;
            } else {
                this.left = left;
            }

            this.top = e.clientY;
            this.visible = true;
            this.selectedTag = tag;
        },
        closeMenu() {
            this.visible = false;
        },
        moveToLeft() {
            // 当前滑动显示区宽度
            const stepWidth = this.$refs.tagsViewBox.clientWidth;
            // 当前tag页签总宽度
            const currentWidth = this.$refs.tagsViewScroll.clientWidth;
            //  获取当前marginLeft的大小
            let currentLeft = this.$refs.tagsViewScroll.style.marginLeft;
            if (currentLeft) {
                const index = currentLeft.indexOf('px');
                currentLeft = currentLeft.slice(0, index);
            } else {
                currentLeft = 0;
            }
            //  计算剩余长度 currentWidth -  currentLeft - stepWidth
            const diff = currentWidth - Math.abs(currentLeft) - stepWidth;
            // 如果剩余长度stepWidth，则向左滑动stepWidth，否则滑动剩余长度即可
            if (diff > 0) {
                if (diff >= stepWidth) {
                    this.$refs.tagsViewScroll.style.marginLeft = (currentLeft - stepWidth) + 'px';
                } else {
                    this.$refs.tagsViewScroll.style.marginLeft = (currentLeft - diff) + 'px';
                }
            }
        },
        moveToRight() {
            // 当前滑动显示区宽度
            const stepWidth = this.$refs.tagsViewBox.clientWidth;
            //  获取当前marginLeft的大小
            let currentLeft = this.$refs.tagsViewScroll.style.marginLeft;
            if (currentLeft) {
                const index = currentLeft.indexOf('px');
                currentLeft = currentLeft.slice(0, index);
            } else {
                currentLeft = 0;
            }
            const diff = Math.abs(currentLeft) - stepWidth;
            if (diff > 0) {
                this.$refs.tagsViewScroll.style.marginLeft = (Number(currentLeft) + Number(stepWidth)) + 'px';
            } else {
                this.$refs.tagsViewScroll.style.marginLeft = 0 + 'px';
            }
        },
        enterStatus(tag) {
            if (tag.status === 0) {
                tag.status = 1;
            }
        },
        leaveStatus(tag) {
            if (tag.status === 1) {
                tag.status = 0;
            }
        },
        // 通过tag切换打开
        handleClickTag(tag) {
            this.$store.commit('tagsView/TOGGLE_VIEW', tag);
        }
    }
};
</script>

<style lang="scss" scoped>
.tags-view-container {
  height: 45px;
  width: 100%;
  display: flex;
  align-items: center;
  background: #fff;
  box-sizing: border-box;
  border-bottom: 1px solid #d8dce5;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);
  .tags-view-wrapper {
	  display: flex;
	.tags-view--item-box{
		flex:1;
		display: flex;
		align-items: center;
		overflow: hidden;
		.tags-view-item-scroll{
			transition: all 1.5s;
			.tags-view-item {
				display: inline-block;
				position: relative;
				cursor: pointer;
				height: 32px;
				line-height: 32px;
				border: 1px solid #F2F2F2;
				border-radius: 8px;
				color: #495060;
				background: #fff;
				padding: 0 8px;
				font-size: 12px;
				margin-left: 10px;
				&.active {
					background-color: #F2F2F2;
					color: #1E222D;
					border-color: transparent;
					.circle {
						background: #09c;
						display: inline-block;
						width: 12px;
						height: 12px;
						border-radius: 50%;
						position: relative;
						margin-right: 2px;
						top:2px;
					}
					.el-icon-close {
						background-color: #8290a3;
					}
				}
				.circle {
					background: #c7c7c7;
					display: inline-block;
					width: 12px;
					height: 12px;
					border-radius: 50%;
					position: relative;
					margin-right: 2px;
					top:2px;

				}
				.sub-el-icon{
					font-size:14px;
					color:#09c;
				}
			}
		}

	}
	.btn{
		width:45px;
		height:45px;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		background: #ffffff;
		z-index:2;
		box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);
	}
	.btn-right{
		box-shadow: -2px 2px 3px 0 rgba(0, 0, 0, .3);
	}
  }
  .contextmenu {
    margin: 0;
    background: #fff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);
    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;
      &:hover {
        background: #eee;
      }
    }
  }
}
</style>

<style lang="scss">
//reset element css of el-icon-close
.scroll-container {
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  width: 100%;
  ::v-deep {
    .el-scrollbar__bar {
      bottom: 0px;
    }
    .el-scrollbar__wrap {
      height: 49px;
    }
  }
}
.tags-view-wrapper {
  .tags-view-item {
    .el-icon-close {
      width: 15px;
      height: 15px;
      border-radius: 50%;
	  margin-left:3px;
	  background-color: #c7c7c7;
      color: #fff;
	  vertical-align: 2px;
      text-align: center;
      transition: all .3s cubic-bezier(.645, .045, .355, 1);
      transform-origin: 100% 50%;
      &:before {
        display: inline-block;
        vertical-align: -2px;
      }
    }
  }
}
</style>
