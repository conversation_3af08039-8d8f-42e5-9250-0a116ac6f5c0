<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig" />
            <snbc-base-form-dialog ref="dialogRef" :config="dialogConfig" @submit="handleSubmit" />
        </div>
    </div>
</template>

<script>
import Vue from 'vue';
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcBaseFormDialog from 'warehouse/components/snbc-dialog/SnbcBaseFormDialog.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import FormRules from 'warehouse/common/form-rules/index.js';

const { cloneDeep } = Vue.prototype.$tools;
const {
    warehouseCode,
    warehouseName,
    warehouseState,
    warehouseDescription,
    totalArea,
    region,
    detailAddress,
    onRoadMaxDay,
    cityRange,
    repositoryType,
    structureType,
    buildingSharingFactor,
    firefightingSharingFactor,
    serviceName,
    serviceContact,
    serviceContactTel
} = FormItems;

const { selectRequired, inputRequired, maxLength, regionRequired, inputRequiredOnBlur, cityRequired } = FormRules;

// 弹窗校验规则
const rules = {
    warehouseName: [inputRequired('区仓名称'), maxLength(32)],
    warehouseState: [selectRequired('区仓状态')],
    repositoryType: [selectRequired('仓库类型')],
    structureType: [selectRequired('结构类型')],
    totalArea: [inputRequired('总面积')],
    serviceName: [selectRequired('服务商名称')],
    buildingSharingFactor: [inputRequired('建筑公摊系数')],
    firefightingSharingFactor: [inputRequired('消防公摊系数')],
    region: [regionRequired(3), inputRequired()],
    detailAddress: [inputRequired('详细地址'), maxLength(255)],
    onRoadMaxDay: [inputRequiredOnBlur('在途最大天数')],
    warehouseDescription: [maxLength(255)],
    coverCity: [cityRequired(), inputRequired()],
    serviceContactTel: [
        {
            pattern: /^1[3456789]\d{9}$/,
            message: '请填写正确的手机号码',
            trigger: 'blur'
        }
    ]
};

// 查询参数
const queryParams = {
    warehouseName: '',
    warehouseState: ''
};
// 支持内存缓存的页面参数
const allParams = {
    queryParams: cloneDeep(queryParams)
};
const coverCity = {
    ...cityRange,
    modelKey: 'coverCity',
    name: '覆盖城市'
};

// 查询区域配置项
const queryConfigItems = [warehouseName, warehouseState];

// 编辑弹窗配置
const editDialogConfigItems = [
    {
        ...warehouseCode,
        elInputAttrs: {
            disabled: true
        }
    },
    warehouseName,
    {
        ...warehouseState,
        elSelectAttrs: {
            disabled: true
        }
    },
    repositoryType,
    structureType,
    warehouseDescription,
    totalArea,
    region,
    buildingSharingFactor,
    firefightingSharingFactor,
    serviceName,
    serviceContact,
    serviceContactTel,
    detailAddress,
    onRoadMaxDay,
    coverCity
];

// 新增弹窗配置
const addDialogConfigItems = [
    warehouseName,
    {
        ...warehouseState,
        elSelectAttrs: {
            disabled: true
        }
    },
    repositoryType,
    structureType,
    warehouseDescription,
    totalArea,
    region,
    buildingSharingFactor,
    firefightingSharingFactor,
    serviceName,
    serviceContact,
    serviceContactTel,
    detailAddress,
    onRoadMaxDay,
    coverCity
];

export default {
    name: 'WarehouseBaseInformation',
    components: {
        SnbcBaseTable,
        SnbcBaseFormDialog
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        const {
            list: listApi,
            remove: removeApi,
            add: addApi,
            edit: editApi,
            editState: editStateApi
        } = this.$service.warehouse.warehouseBaseInformation;
        return {
            listApi,
            addApi,
            editApi,
            removeApi,
            editStateApi,
            tableConfig: {
                queryParams: allParams.queryParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: listApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '区仓编码',
                        prop: 'warehouseCode',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '区仓状态',
                        prop: 'warehouseState',
                        show: true,
                        minWidth: 100,
                        renderMode: 'tag',
                        elTagAttrsFn(row) {
                            return {
                                type: { 启用: 'success', 停用: 'warning' }[row.warehouseState]
                            };
                        }
                    },
                    {
                        label: '仓库类型',
                        prop: 'repositoryType',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '结构类型',
                        prop: 'structureType',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '在途最大天数',
                        prop: 'onRoadMaxDay',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '建筑公摊系数',
                        prop: 'buildingSharingFactor',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '消防公摊系数',
                        prop: 'firefightingSharingFactor',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '总面积',
                        prop: 'totalArea',
                        show: true,
                        minWidth: 110
                    },
                    {
                        label: '建筑公摊面积',
                        prop: 'buildingSharingArea',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '消防公摊面积',
                        prop: 'firefightingSharingArea',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '创建时间',
                        prop: 'createTime',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '服务商名称',
                        prop: 'serviceName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '服务商联系人',
                        prop: 'serviceContact',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '服务商联系电话',
                        prop: 'serviceContactTel',
                        show: true,
                        minWidth: 160
                    }
                ],
                operations: [
                    {
                        name: '启用',
                        type: 'success',
                        handleClick: this.handleEnable,
                        handleShow(row) {
                            return ['停用', '草稿'].includes(row.warehouseState);
                        }
                    },
                    {
                        name: '编辑',
                        type: 'primary',
                        handleClick: this.handleEdit
                    }
                ],
                headerButtons: [
                    {
                        name: '新增',
                        type: 'primary',
                        handleClick: this.handleAdd
                    }
                ]
            },
            // 弹窗配置
            dialogConfig: {
                rules,
                items: editDialogConfigItems
            }
        };
    },
    computed: {},
    beforeCreate() {
        if (!this.$store.getters.toggleView) {
            allParams.queryParams = cloneDeep(queryParams);
        }
    },
    created() {
        // 非tag切换进入，重置页面参数至默认状态
        if (!this.$store.getters.toggleView) {
            this.$tools.setQueryParams.call(this);
        }
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.handleQuery();
    },
    methods: {
        // 编辑操作
        handleEdit(row) {
            const data = this.$tools.cloneDeep(row);
            data.region = [data.province, data.city, data.district];
            this.dialogConfig.items = editDialogConfigItems;
            this.$refs.dialogRef.baseEditDialog(data, '编辑区仓');
        },
        // 启用操作
        async handleEnable(item) {
            await this.$tools.confirm('确认启用？');
            try {
                const res = await this.editStateApi(item.warehouseCode, '启用');
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('启用成功');
                this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 停用操作
        async handleDisable(item) {
            await this.$tools.confirm('确认停用？');
            try {
                const res = await this.editStateApi(item.warehouseCode, '停用');
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('停用成功');
                this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 删除操作
        async handleDelete(item) {
            await this.$tools.confirm('删除区仓后，与该区仓关联的规则将同时删除，确认删除？');
            try {
                const res = await this.removeApi(item.warehouseCode);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('删除成功');
                this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 新增操作
        handleAdd() {
            const data = {
                warehouseState: '草稿',
                onRoadMaxDay: 0,
                buildingSharingFactor: 0,
                firefightingSharingFactor: 0,
                totalArea: 0
            };
            this.dialogConfig.items = addDialogConfigItems;
            this.$refs.dialogRef.baseAddDialog(data, '新增区仓');
        },
        // 新增或编辑提交
        async handleSubmit({ mode, form }) {
            await this.$tools.confirm('确认提交？');
            const params = { ...form };
            params.province = form.region[0];
            params.city = form.region[1];
            params.district = form.region[2];
            let submitApi = this.addApi;
            if (mode === 'edit') {
                params.id = form.id;
                submitApi = this.editApi;
            }
            try {
                const res = await submitApi(params);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.$refs.dialogRef.hideDialog();
                this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        }
    }
};
</script>

<style lang="scss" scoped></style>
