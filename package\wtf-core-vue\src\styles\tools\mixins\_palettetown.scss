/// Try changing the $test-color variable in the CSS area and watch a new color palette generate!
/// @param {list} $color
/// @param {string} $type
/// @link https://github.com/jordiesaenz/palette-town
/// <AUTHOR>


@mixin palette-town($color, $type) {
    $color-primary: $color !global;
    $color-primary-darkest: desaturate(darken($color-primary, 45%), 15%) !global;
    $color-primary-darker: desaturate(darken($color-primary, 30%), 15%) !global;
    $color-primary-dark: desaturate(darken($color-primary, 15%), 15%) !global;
    $color-primary-light: saturate(lighten($color-primary, 15%), 15%) !global;
    $color-primary-lighter: saturate(lighten($color-primary, 30%), 15%) !global;
    $color-primary-lightest: saturate(lighten($color-primary, 45%), 15%) !global;
    @if $type==complement or complementary {
        $color-complement: mix($color-primary, complement($color), 15%) !global;
        $color-complement-darkest: desaturate(darken($color-complement, 45%), 15%) !global;
        $color-complement-darker: desaturate(darken($color-complement, 30%), 15%) !global;
        $color-complement-dark: desaturate(darken($color-complement, 15%), 15%) !global;
        $color-complement-light: saturate(lighten($color-complement, 15%), 15%) !global;
        $color-complement-lighter: saturate(lighten($color-complement, 30%), 15%) !global;
        $color-complement-lightest: saturate(lighten($color-complement, 45%), 15%) !global;
    }
    @if $type==triad {
        $color-triad-1: $color-primary !global;
        $color-triad-1-darkest: $color-primary-darkest !global;
        $color-triad-1-darker: $color-primary-darker !global;
        $color-triad-1-dark: $color-primary-dark !global;
        $color-triad-1-light: $color-primary-light !global;
        $color-triad-1-lighter: $color-primary-lighter !global;
        $color-triad-1-lightest: $color-primary-lightest !global;
        $color-triad-2: mix($color-primary, adjust-hue($color, 120deg), 15%) !global;
        $color-triad-2-darkest: desaturate(darken($color-triad-2, 45%), 15%) !global;
        $color-triad-2-darker: desaturate(darken($color-triad-2, 30%), 15%) !global;
        $color-triad-2-dark: desaturate(darken($color-triad-2, 15%), 15%) !global;
        $color-triad-2-light: saturate(lighten($color-triad-2, 15%), 15%) !global;
        $color-triad-2-lighter: saturate(lighten($color-triad-2, 30%), 15%) !global;
        $color-triad-2-lightest: saturate(lighten($color-triad-2, 45%), 15%) !global;
        $color-triad-3: mix($color-primary, adjust-hue($color, 240deg), 15%) !global;
        $color-triad-3-darkest: desaturate(darken($color-triad-3, 45%), 15%) !global;
        $color-triad-3-darker: desaturate(darken($color-triad-3, 30%), 15%) !global;
        $color-triad-3-dark: desaturate(darken($color-triad-3, 15%), 15%) !global;
        $color-triad-3-light: saturate(lighten($color-triad-3, 15%), 15%) !global;
        $color-triad-3-lighter: saturate(lighten($color-triad-3, 30%), 15%) !global;
        $color-triad-3-lightest: saturate(lighten($color-triad-3, 45%), 15%) !global;
    }
    @if $type==tetrad {
        $color-tetrad-1: $color-primary !global;
        $color-tetrad-1-darkest: $color-primary-darkest !global;
        $color-tetrad-1-darker: $color-primary-darker !global;
        $color-tetrad-1-dark: $color-primary-dark !global;
        $color-tetrad-1-light: $color-primary-light !global;
        $color-tetrad-1-lighter: $color-primary-lighter !global;
        $color-tetrad-1-lightest: $color-primary-lightest !global;
        $color-tetrad-2: mix($color-primary, adjust-hue($color, 60deg), 15%) !global;
        $color-tetrad-2-darkest: desaturate(darken($color-tetrad-2, 45%), 15%) !global;
        $color-tetrad-2-darker: desaturate(darken($color-tetrad-2, 30%), 15%) !global;
        $color-tetrad-2-dark: desaturate(darken($color-tetrad-2, 15%), 15%) !global;
        $color-tetrad-2-light: saturate(lighten($color-tetrad-2, 15%), 15%) !global;
        $color-tetrad-2-lighter: saturate(lighten($color-tetrad-2, 30%), 15%) !global;
        $color-tetrad-2-lightest: saturate(lighten($color-tetrad-2, 45%), 15%) !global;
        $color-complement: mix($color-primary, complement($color), 15%) !global;
        $color-complement-darkest: desaturate(darken($color-complement, 45%), 15%) !global;
        $color-complement-darker: desaturate(darken($color-complement, 30%), 15%) !global;
        $color-complement-dark: desaturate(darken($color-complement, 15%), 15%) !global;
        $color-complement-light: saturate(lighten($color-complement, 15%), 15%) !global;
        $color-complement-lighter: saturate(lighten($color-complement, 30%), 15%) !global;
        $color-complement-lightest: saturate(lighten($color-complement, 45%), 15%) !global;
        $color-tetrad-3: $color-complement !global;
        $color-tetrad-3-darkest: $color-complement-darkest !global;
        $color-tetrad-3-darker: $color-complement-darker !global;
        $color-tetrad-3-dark: $color-complement-dark !global;
        $color-tetrad-3-light: $color-complement-light !global;
        $color-tetrad-3-lighter: $color-complement-lighter !global;
        $color-tetrad-3-lightest: $color-complement-lightest !global;
        $color-tetrad-4: mix($color-primary, adjust-hue($color, 270deg), 15%) !global;
        $color-tetrad-4-darkest: desaturate(darken($color-tetrad-4, 45%), 15%) !global;
        $color-tetrad-4-darker: desaturate(darken($color-tetrad-4, 30%), 15%) !global;
        $color-tetrad-4-dark: desaturate(darken($color-tetrad-4, 15%), 15%) !global;
        $color-tetrad-4-light: saturate(lighten($color-tetrad-4, 15%), 15%) !global;
        $color-tetrad-4-lighter: saturate(lighten($color-tetrad-4, 30%), 15%) !global;
        $color-tetrad-4-lightest: saturate(lighten($color-tetrad-4, 45%), 15%) !global;
    }
    @if $type==split or split-compliment {
        $color-split-1: mix($color-primary, adjust-hue($color, 150deg), 15%) !global;
        $color-split-1-darkest: desaturate(darken($color-split-1, 45%), 15%) !global;
        $color-split-1-darker: desaturate(darken($color-split-1, 30%), 15%) !global;
        $color-split-1-dark: desaturate(darken($color-split-1, 15%), 15%) !global;
        $color-split-1-light: saturate(lighten($color-split-1, 15%), 15%) !global;
        $color-split-1-lighter: saturate(lighten($color-split-1, 30%), 15%) !global;
        $color-split-1-lightest: saturate(lighten($color-split-1, 45%), 15%) !global;
        $color-split-2: mix($color-primary, adjust-hue($color, 210deg), 15%) !global;
        $color-split-2-darkest: desaturate(darken($color-split-2, 45%), 15%) !global;
        $color-split-2-darker: desaturate(darken($color-split-2, 30%), 15%) !global;
        $color-split-2-dark: desaturate(darken($color-split-2, 15%), 15%) !global;
        $color-split-2-light: saturate(lighten($color-split-2, 15%), 15%) !global;
        $color-split-2-lighter: saturate(lighten($color-split-2, 30%), 15%) !global;
        $color-split-2-lightest: saturate(lighten($color-split-2, 45%), 15%) !global;
    }
    @if $type==shades or shade {
        $color-shade: desaturate($color-primary, 75%) !global;
        $color-shade-darkest: darken($color-shade, 45%) !global;
        $color-shade-darker: darken($color-shade, 30%) !global;
        $color-shade-dark: darken($color-shade, 15%) !global;
        $color-shade-light: lighten($color-shade, 15%) !global;
        $color-shade-lighter: lighten($color-shade, 30%) !global;
        $color-shade-lightest: lighten($color-shade, 45%) !global;
    }
    @if $type==all {
        @include palette-town($color, complement);
        @include palette-town($color, triad);
        @include palette-town($color, tetrad);
        @include palette-town($color, split);
        @include palette-town($color, shades);
    }
}
