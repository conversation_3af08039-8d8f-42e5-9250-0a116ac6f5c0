<template>
    <!-- 区仓支出和收入 -->
    <div ref="chartRef" class="chart-box" />
</template>
<script>
import moment from 'moment';
import echarts from 'echarts';
import resize from 'wtf-core-vue/src/components/Charts/mixins/resize';

export default {
    name: 'ChartFeeAndIncome',
    mixins: [resize],
    data() {
        return {
            // 图表实例
            chart: null
        };
    },
    mounted() {
        this.chart = echarts.init(this.$refs.chartRef);
    },
    beforeDestroy() {
        if (!this.chart) {
            return;
        }
        this.chart.dispose();
        this.chart = null;
    },
    methods: {
        // 清除图表
        clearChart() {
            this.chart.clear();
        },
        handleData(data) {
            return data.map((item) => {
                item.income = Number(item.income);
                item.cost = Number(item.cost);
                item.time = moment(item.time).format('YY年MM月');
                return item;
            });
        },
        // eslint-disable-next-line max-lines-per-function
        drawChart(result = []) {
            const data = this.handleData(result);
            this.chart.setOption({
                color: ['#26BAD5', '#7ED321'],
                title: {
                    left: 'center',
                    top: '10px',
                    text: '区仓支出和收入'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['支出(万)', '收入(万)'],
                    itemHeight: 10,
                    textStyle: {
                        color: '#000'
                    },
                    right: 10,
                    top: 10
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: 20,
                    top: 80,
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: data.map((item) => item.time),
                    axisTick: {
                        alignWithLabel: true
                    },
                    axisLabel: {
                        interval: 0,
                        // X轴三个字一换行
                        formatter: (value, idx) => {
                            return this.$tools.formatAxisLabel(value);
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '金额(万)',
                    splitLine: {
                        lineStyle: {
                            type: 'dashed',
                            color: '#303437'
                        }
                    }
                },
                series: [
                    {
                        type: 'bar',
                        barMaxWidth: 20,
                        data: data.map((item) => {
                            return {
                                name: item.time,
                                value: item.cost
                            };
                        }),
                        itemStyle: {
                            normal: {
                                label: {
                                    show: true,
                                    position: 'top',
                                    color: '#000000'
                                }
                            }
                        },
                        name: '支出(万)'
                    },
                    {
                        type: 'bar',
                        barMaxWidth: 20,
                        data: data.map((item) => {
                            return {
                                name: item.time,
                                value: item.income
                            };
                        }),
                        itemStyle: {
                            normal: {
                                label: {
                                    show: true,
                                    position: 'top',
                                    color: '#000000'
                                }
                            }
                        },
                        name: '收入(万)'
                    }
                ]
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.chart-box {
    width: 100%;
    height: 300px;
    border: 1px solid #dee5e7;
    background: #e6ebf5;
}
</style>
