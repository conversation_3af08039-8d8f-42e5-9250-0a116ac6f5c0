/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();
    const service = {
        stockUp: {
            /**
             * 备货申请列表查询
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/stockRequest/list',
                    method: 'post',
                    data
                });
            },
            /**
             * 备货申请待提交列表查询
             *
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getNotSubmittedList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/stockRequest/not_submitted_stock_request_list',
                    method: 'post',
                    data
                });
            },
            /**
             * 已审核申请列表查询
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getReviewedList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/stockRequest/reviewed_list',
                    method: 'post',
                    data
                });
            },
            /**
             * 新增备货申请任务
             * @param {Object} data 申请信息
             * @returns {Promise} http
             */
            add(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/stockRequest/add',
                    method: 'post',
                    data
                });
            },
            // 修改备货申请
            /**
             *
             * @param {Object} data 申请信息
             * @returns {Promise} http
             */
            update(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/stockRequest/edit',
                    method: 'post',
                    data
                });
            },
            /**
             * 草稿状态的备货申请删除
             * @param {String} stockRequestCode 申请编号
             * @returns {Promise} http
             */
            delete(stockRequestCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/stockRequest/del_stock_request`,
                    method: 'get',
                    params: {
                        stockRequestCode
                    }
                });
            },
            /**
             * 待审核备货申请撤回
             * @param {Object} data stockRequestCode申请编号
             * @returns {Promise} http
             */
            revoke(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/stockRequest/submit`,
                    method: 'post',
                    params: {
                        stockRequestCode: data
                    }
                });
            },
            /**
             * 备货申请详情查询
             * @param {Object} data 查询条件——申请编号、目标区仓
             * @returns {Promise} http
             */
            getDetails(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/stockRequest/detail_list',
                    method: 'post',
                    data
                });
            },
            /**
             * 备货申请审核通过
             * @param {Object} data 申请信息
             * @returns {Promise} http
             */
            updateByAudit(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/stockRequest/audit',
                    method: 'post',
                    data
                });
            },
            /**
             * 备货申请审核拒绝
             * @param {Object} data 申请编号、拒绝原因
             * @returns {Promise} http
             */
            updateByAuditReject(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/stockRequest/audit_rejection`,
                    method: 'post',
                    params: {
                        stockRequestCode: data.stockRequestCode,
                        reviewRemark: data.reviewRemark
                    }
                });
            },
            /**
             * 备货申请基本信息生成
             * @param {Object} data 分解任务信息
             * @returns {Promise} http
             */
            getBasicInfo(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/stockRequest/basic_information_stocking_application',
                    method: 'post',
                    data
                });
            },
            /**
             * 备货申请生成任务
             * @param {Object} data 任务信息
             * @returns {Promise} http
             */
            addTask(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/stockRequest/stocking_request_generation_task',
                    method: 'post',
                    data
                });
            },
            /**
             * 全国区仓库存查询
             * @param {Object} data 查询条件
             * @returns {Promise} http
             */
            getNationalInventoryList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/stockRequest/national_inventory_query_list',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
