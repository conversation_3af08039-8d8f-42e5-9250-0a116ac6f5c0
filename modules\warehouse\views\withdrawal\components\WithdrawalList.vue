<template>
    <div class="content">
        <snbc-base-table ref="tableRef" :table-config="tableConfig">
            <template #tabs>
                <snbc-table-tabs :tabs-config="tabsConfig" />
            </template>
        </snbc-base-table>
    </div>
</template>

<script>
import Vue from 'vue';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcTableTabs from 'warehouse/components/snbc-table/SnbcTableTabs.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import { mapState } from 'vuex';
import objectFactory from 'warehouse/common/object-factory/index.js';

const { cloneDeep } = Vue.prototype.$tools;

const { warehouseNameRegion, customerSelect, input, dateRange } = FormItems;

// 在库资产查询列表
const commonItems = [
    {
        ...input,
        name: '撤机入库任务编号',
        modelKey: 'taskCode'
    },
    {
        ...warehouseNameRegion,
        modelKey: 'targetWarehouseCode'
    },
    {
        ...input,
        name: '服务外包商',
        modelKey: 'orderProviderName'
    },
    {
        ...input,
        name: '关联订单编号',
        modelKey: 'orderCode'
    },
    {
        ...customerSelect,
        modelKey: 'warehouseCustomerCode'
    }
];

const queryParams = {
    taskCode: '',
    targetWarehouseCode: '',
    orderProviderName: '',
    orderCode: '',
    warehouseCustomerCode: '',
    dateRange: []
};

const tabsConfig = {
    activeName: '进行中',
    tabItems: ['进行中', '已完成']
};

const pageParams = objectFactory('pageParams');

const allParams = {
    queryParams: cloneDeep(queryParams),
    tabsConfig: cloneDeep(tabsConfig),
    pageParams: cloneDeep(pageParams)
};

export default {
    name: 'WarehouseWithdrawalManagement',
    components: {
        SnbcBaseTable,
        SnbcTableTabs
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        const { getList, downloadList } = this.$service.warehouse.withdrawal;
        return {
            getList,
            downloadList,
            tableConfig: {
                queryParams: allParams.queryParams,
                pageParams: allParams.pageParams,
                queryConfig: {
                    items: [
                        ...commonItems,
                        {
                            ...dateRange,
                            name: '结束时间'
                        }
                    ],
                    elFormAttrs: {
                        'label-width': '120px'
                    }
                },
                queryApi: getList,
                elTableColumns: [],
                headerButtons: [
                    {
                        name: '导出',
                        type: 'primary',
                        handleClick: this.export
                    }
                ],
                hooks: {
                    tableListHook: this.tableListHook,
                    queryParamsHook: this.queryParamsHook
                }
            },
            tabsConfig: allParams.tabsConfig
        };
    },
    computed: {
        ...mapState(['permission'])
    },
    watch: {
        'tabsConfig.activeName': {
            handler(val) {
                this.tableConfig.elTableColumns = this.columnsInit();
                this.$nextTick(() => {
                    if (val === '进行中') {
                        this.tableConfig.queryConfig.items = [
                            ...commonItems,
                            {
                                ...dateRange,
                                name: '开始时间'
                            }
                        ];
                    } else {
                        this.tableConfig.queryConfig.items = [
                            ...commonItems,
                            {
                                ...dateRange,
                                name: '结束时间'
                            }
                        ];
                    }
                    this.$refs.tableRef.handleReset();
                });
            }
        }
    },
    beforeCreate() {
        if (!this.$store.getters.toggleView) {
            // 支持内存缓存的页面参数
            allParams.queryParams = cloneDeep(queryParams);
            allParams.pageParams = cloneDeep(pageParams);
            allParams.tabsConfig = cloneDeep(tabsConfig);
        }
    },
    created() {
        // 非tag切换进入，重置页面参数至默认状态
        if (!this.$store.getters.toggleView) {
            this.$tools.setQueryParams.call(this);
        }
    },
    mounted() {
        this.tableConfig.elTableColumns = this.columnsInit();
        this.$refs.tableRef.queryList();
    },
    methods: {
        // 资产信息查看
        handleClickCell(row) {
            this.$emit('handleDetails', row);
        },
        // export withdrawal-info.xsxl
        async export() {
            const params = { ...this.tableConfig.queryParams };
            this.queryParamsHook(params);
            const stream = await this.downloadList(params);
            this.$tools.downloadExprotFile(stream, `撤机任务信息`, 'xlsx', 'xlsx').catch((e) => {
                this.$tools.message.err(e || '导出失败');
            });
        },
        // init columns
        columnsInit() {
            const commonColums = [
                { label: '序号', prop: 'index', show: true, minWidth: 80 },
                {
                    label: '撤机入库任务编号',
                    prop: 'taskCode',
                    show: true,
                    minWidth: 200,
                    renderMode: 'button',
                    elButtonAttrs: {
                        type: 'text'
                    },
                    handleClick: this.handleClickCell
                },
                {
                    label: '关联订单编号',
                    prop: 'orderCode',
                    show: true,
                    minWidth: 160
                },
                {
                    label: '区仓名称',
                    prop: 'targetWarehouseName',
                    show: true,
                    minWidth: 120
                },
                {
                    label: '入库进度',
                    prop: 'schedule',
                    show: true,
                    minWidth: 100,
                    renderMode: 'button',
                    elButtonAttrs: {
                        type: 'text'
                    },
                    handleClick: this.handleClickSchedule
                },
                {
                    label: '服务外包商',
                    prop: 'orderProviderName',
                    show: true,
                    minWidth: 120
                },
                {
                    label: '客户名称',
                    prop: 'warehouseCustomerName',
                    show: true,
                    minWidth: 120
                },
                {
                    label: '是否有货损',
                    prop: 'damageNumber',
                    show: true,
                    minWidth: 120
                },
                {
                    label: '状态',
                    prop: 'taskState',
                    show: true,
                    minWidth: 120
                },
                {
                    label: '开始时间',
                    prop: 'createTime',
                    show: true,
                    minWidth: 160
                }
            ];
            return this.tabsConfig.activeName === '进行中'
                ? [...commonColums]
                : [
                      ...commonColums,
                      {
                          label: '完成时间',
                          prop: 'finishTime',
                          show: true,
                          minWidth: 160
                      }
                  ];
        },

        // format list data
        tableListHook(list) {
            list.forEach((item) => {
                item.damageNumber = item.damageNumber ? '是' : '否';
                item.schedule = `${item.inboundNumber}/${item.returnCabinetNumber + item.returnCanopyNumber}`;
                item.taskState = this.tabsConfig.activeName;
            });
        },
        // format query params
        queryParamsHook(params) {
            Object.keys(params).forEach((key) => {
                if ((typeof params[key] === 'string' || Array.isArray(params[key])) && !params[key].length) {
                    delete params[key];
                }
            });
            if (this.tabsConfig.activeName === '进行中') {
                params.taskState = 0;
                params.orderBy = 'create';
                if (params.dateRange) {
                    [params.startTime, params.endTime] = params.dateRange;
                    delete params.dateRange;
                }
            } else {
                params.taskState = 1;
                params.orderBy = 'finish';
                if (params.dateRange) {
                    [params.startFinishTime, params.endFinishTime] = params.dateRange;
                    delete params.dateRange;
                }
            }
        },
        handleClickSchedule(item) {
            this.$router.push({
                path: '/app/assets/operate-log',
                query: {
                    associatedTaskCode: item.taskCode
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.content {
    ::v-deep .el-tabs__content {
        padding: 0;
    }
}
</style>
