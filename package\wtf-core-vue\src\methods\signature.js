
 var hexcase = 0;  /* hex output format. 0 - lowercase; 1 - uppercase        */
 var b64pad  = ""; /* base-64 pad character. "=" for strict RFC compliance   */
 var chrsz   = 8;  /* bits per input character. 8 - ASCII; 16 - Unicode      */
// 生成13位0-9 A-Z a-z的随机数
function randomStr(n) {
    const arr = [
        'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l',
        'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L',
        'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    let str = '';
    for (let i = 0; i < n; i++) {
        str = str + arr[Math.floor(Math.random() * 62)];
    }
    return str;
}
// 生成13位时间戳
function randomTime() {
    const time = new Date().getTime();
    return time;
}
 //处理JSON.stringify后的字符串
function parseHandler(text) {
	var at = 0
	var ch = ' '
	var escapee = {
		'"': '"',
		'\\': '\\',
		'/': '/',
		b: '\b',
		f: '\f',
		n: '\n',
		r: '\r',
		t: '\t'
	}

	var error = function (m) {
		throw {
			name: 'SyntaxError',
			message: m,
			at: at,
			text: text
		}
	}

	var next = function (c) {
		if (c && c !== ch) {
			error("Expected '" + c + "' instead of '" + ch + "'")
		}
		ch = text.charAt(at)
		at = at + 1

		return ch
	}

	var white = function () {
		while (ch && ch <= ' ') {
			next()
		}
	}

	var number = function () {
		var number
		var string = ''

		if (ch === '-') {
			string = '-'
			next('-')
		}

		while (ch >= '0' && ch <= '9') {
			string += ch
			next()
		}

		if (ch === '.') {
			string += '.'
			while (next() && ch >= '0' && ch <= 9) {
				string += ch
			}
		}

		if (ch === 'e' || ch === 'E') {
			string += ch
			next()
			if (ch === '-' || ch === '+') {
				string += ch
				next()
			}
			while (ch >= '0' && ch <= '9') {
				string += ch
				next()
			}
		}

		number = string - 0
		if (!isFinite(number)) {
			error('Bad number')
		} else {
			return number
		}
	}

	var string = function () {
		var hex
		var i
		var string = ''
		var uffff

		if (ch === '"') {
			while (next()) {
				if (ch === '"') {
					next()
					return string // ���ַ���
				}

				if (ch === '\\') {
					next()
					if (ch === 'u') {
						uffff = 0
						for (var i = 0; i < 4; i += 1) {
							hex = parseInt(next(), 16)
							if (!isFinite(hex)) {
								break
							}

							uffff = uffff * 16 + hex
						}
						string += String.fromCharCode(uffff)
					} else if (typeof escapee[ch] === 'string') {
						string += escapee[ch]
					} else {
						break
					}
				} else {
					string += ch
				}
			}
		}
		error('Bad string')
	}

	var word = function () {
		switch (ch) {
			case 't':
				next('t');
				next('r');
				next('u');
				next('e');
				return true;
			case 'f':
				next('f');
				next('a');
				next('l');
				next('s');
				next('e');
				return false;
			case 'n':
				next('n');
				next('u');
				next('l');
				next('l');
				return null;
		}

		error("Unexpected '" + ch + "'");
	}
	var value

	var array = function () {
		var arrayStr = '[';
		if (ch === '[') {
			next('[')
			white()

			if (ch === ']') {
				next(']')
				return '[]' // �������ַ���
			}

			while (ch) {
				arrayStr = arrayStr + value();
				white()
				if (ch === ']') {
					next(']')
					return arrayStr + ']';
				}
				next(',')
				arrayStr = arrayStr + ',';
				white()
			}
		}

		error('Bad array')
	}

	var object = function () {
		var object_key;
		var object_value;
		var objectStr = "{";
		if (ch === '{') {
			next('{')
			white()

			if (ch === '}') {
				next('}')
				return "{}" // �ն���
			}

			while (ch) {
				object_key = string();
				objectStr = objectStr + object_key;
				white()
				next(':')
				object_value = value();
				objectStr = objectStr + ":" + object_value;
				white()
				if (ch === '}') {
					next('}')
					return objectStr + "}";
					// return object
				}
				next(',')
				objectStr = objectStr + ",";
				white()
			}
		}

		error('Bad object')
	}

	value = function () {
		white()
		switch (ch) {
			case '{':
				return object()
			case '[':
				return array()
			case '"':
				return string()
			case '-':
				return number()
			default:
				return ch >= '0' && ch <= '9' ? number() : word()
		}
	}

	return value(text)
}

// 处理url参数
function urlParamsToObj(url) {
	var paraObj = {};
	if (url.indexOf("?") !== -1) {
		var nameValue;
		var paraString = url.substring(url.indexOf("?") + 1, url.length).split("&");
		for (var i = 0; i < paraString.length; i++) {
			var nameValue = paraString[i];
			if(!nameValue) {
				continue;
			}
			var name = nameValue.substring(0, nameValue.indexOf("="));
			var value = nameValue.substring(nameValue.indexOf("=") + 1, nameValue.length);
			if (value.indexOf("#") > -1) {
				value = value.split("#")[0];
			}
			paraObj[name] = decodeURIComponent(value);
		}
	}
	return paraObj;
}

// 对所有的object的value都进行json处理  flag=true说明是params的参数
function jsonHandler(obj, flag) {
	var jsonHandlerObj = null;
	if (obj) {
		jsonHandlerObj = {};
		if (obj.constructor === Object) {
			for (var key in obj) {
				if (obj.hasOwnProperty(key)) {
					if (flag && Array.isArray(obj[key])) {
						var jsonParamsArray = obj[key];
						if (jsonParamsArray.length > 0) {
							jsonHandlerObj[key] = jsonStringHandler(jsonParamsArray[jsonParamsArray.length - 1]);
						}
					} else if (obj[key] === undefined || obj[key] === null) {
						delete obj[key];
					} else {
						jsonHandlerObj[key] = jsonStringHandler(obj[key]);
					}
				}
			}
		} else if (Array.isArray(obj)) {
			if (flag && obj.length > 0) {
				var newJsonParamsString = "";
				obj.forEach(function (item, index) {
					if (Array.isArray(item)) {
						if (Array.isArray(item[item.length - 1])) {
							newJsonParamsString = newJsonParamsString + index + "=" + jsonStringHandler(item[item.length - 1]) + "&";
						} else if (item[item.length - 1].constructor === Object) {
							newJsonParamsString = newJsonParamsString + index + "=" + jsonStringHandler(item) + "&";
						} else {
							newJsonParamsString = newJsonParamsString + index + "=" + item + "&";
						}
					} else if (item.constructor === Object) {
						newJsonParamsString = newJsonParamsString + index + "=" + jsonStringHandler(item) + "&";
					} else {
						newJsonParamsString = newJsonParamsString + index + "=" + item + "&";
					}

				});
				jsonHandlerObj["params_array_string"] = newJsonParamsString.substring(0, newJsonParamsString.length - 1);
			} else {
				jsonHandlerObj["key_str"] = jsonStringHandler(obj);
			}
		} else if(!flag && isJSON(obj)) {
			// data中字符串类型对象，转化为对象处理
			var newObj = JSON.parse(obj);
			for (var key in newObj) {
				if (newObj.hasOwnProperty(key)) {
					if (flag && Array.isArray(newObj[key])) {
						var jsonParamsArray = newObj[key];
						if (jsonParamsArray.length > 0) {
							jsonHandlerObj[key] = jsonStringHandler(jsonParamsArray[jsonParamsArray.length - 1]);
						}
					} else if (newObj[key] === undefined || newObj[key] === null) {
						delete newObj[key];
					} else {
						jsonHandlerObj[key] = jsonStringHandler(newObj[key]);
					}
				}
			}
		} else {
			jsonHandlerObj["key_str"] = jsonStringHandler(obj);
		}
	}
	return jsonHandlerObj;
}

function jsonStringHandler(str) {
	if (str === null || str === undefined) {
		return "";
	} else if (typeof str === "string") {
		return str;
	} else {
		var tempJsonStr = JSON.stringify(str)
		// tempJsonStr = parseHandler(tempJsonStr);
		return tempJsonStr;
	}
}

function isJSON(str) {
	// 判断字符串是否是json格式
	if (typeof str == 'string') {
		try {
			var obj = JSON.parse(str);
			if (typeof obj == 'object' && obj && !Array.isArray(obj)) {
				return true;
			} else {
				return false;
			}

		} catch (e) {
			return false;
		}
	}
}

function getSignature() { // var nonce = hex_md5(getRandNumber()); //随机数
	var nonce = getRandNumber();
	var timestemp = new Date().getTime(); //时间戳
	var md5Str = hex_md5(nonce + '&' + timestemp + 'axeonmc'); //md5加密
	var source = nonce + timestemp + md5Str; //生成
	return source;
}
//生成参数的签名
function getDataSignature(params, data, url) {
	var nonce = getRandNumber(); //随机数
	var timestemp = new Date().getTime(); //时间戳
	var md5Str;
	var requestBody = null;
	var requestBody = "";
	
	if (params && params["params_array_string"]) {
		requestBody = params.params_array_string;
	} else {
		
		requestBody = objKeySort(mergeObject(params, data, url));
	}
	var source = nonce + '&' + timestemp + 'axeonmc&' + requestBody;
	
	md5Str = hex_md5(source);
	return nonce + timestemp + md5Str; //生成
}
/**
 * 生成固定长度的随机数
 *
 * @param int $length
 * @return string
 */
function getRandNumber() {
	var number = 13;
	var str = "",
		arr = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'g', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'];
	var pos = 0; //循环构建输出随机数字符串
	for (var i = 0; i < number; i++) {
		pos = Math.round(Math.random() * (arr.length - 1));
		str += arr[pos];
	}
	return str;
} //Object to String
function objtostring(data) {
	var str = '';
	for (var k in data) {
		if (typeof data[k] == 'string') { // str += k + '=' + encodeURIComponent(data[k]) + '&';
			str += k + '=' + data[k] + '&';
		} else { //str += k + '=' + encodeURIComponent(JSON.stringify(data[k])) + '&';
			str += k + '=' + JSON.stringify(data[k]) + '&';
		}
	}
	if (str) {
		str = str.substring(str, str.length - 1);
	}
	return str;
}

function objKeySort(obj) { //排序并拼接，返回字符串
	if (!obj) {
		return "";
	}
	var newkey = Object.keys(obj).sort();
	//先用Object内置类的keys方法获取要排序对象的属性名，再利用Array原型上的sort方法对获取的属性名进行排序，newkey是一个数组
	var newStr = ""; //字符串，用于拼接，规则：key + '=' + value + '&'，最后一位不拼接'&';
	for (var i = 0; i < newkey.length; i++) { //遍历newkey数组
		newStr = newStr + (newkey[i] + "=" + obj[newkey[i]] + "&");

	}
	newStr = newStr.substring(0, newStr.length - 1);
	return newStr; //返回拼接好的字符串
}
/**
 * @param url 请求的url
 * @returns {{}} 将url中请求参数组装成json对象(url的?后面的参数)
 */
function parseQueryString(url) {
	var urlReg = /^[^\?]+\?([\w\W]+)$/,
		paramReg = /([^&=]+)=([\w\W]*?)(&|$|#)/g,
		urlArray = urlReg.exec(url),
		result = {};
	if (urlArray && urlArray[1]) {
		var paramString = urlArray[1],
			paramResult;
		while ((paramResult = paramReg.exec(paramString)) != null) {
			result[paramResult[1]] = paramResult[2];
		}
	}
	return result;
}
/**
 * @returns {*} 将两个对象合并成一个
 */
function mergeObject(objectOne, objectTwo, objectThree) {
	let obj = {};
	Object.assign(obj, objectOne, objectTwo, objectThree);
	return obj;
}
function hex_md5 (s) {
	var utfs = Utf8Encode(s);
	return binl2hex(core_md5(str2binl(utfs), utfs.length * chrsz));
}
/*
 * Calculate the MD5 of an array of little-endian words, and a bit length
 */
function core_md5(x, len) {
  /* append padding */
  x[len >> 5] |= 0x80 << ((len) % 32);
  x[(((len + 64) >>> 9) << 4) + 14] = len;

  var a =  1732584193;
  var b = -271733879;
  var c = -1732584194;
  var d =  271733878;

  for(var i = 0; i < x.length; i += 16)
  {
	var olda = a;
	var oldb = b;
	var oldc = c;
	var oldd = d;

	a = md5_ff(a, b, c, d, x[i+ 0], 7 , -680876936);
	d = md5_ff(d, a, b, c, x[i+ 1], 12, -389564586);
	c = md5_ff(c, d, a, b, x[i+ 2], 17,  606105819);
	b = md5_ff(b, c, d, a, x[i+ 3], 22, -1044525330);
	a = md5_ff(a, b, c, d, x[i+ 4], 7 , -176418897);
	d = md5_ff(d, a, b, c, x[i+ 5], 12,  1200080426);
	c = md5_ff(c, d, a, b, x[i+ 6], 17, -1473231341);
	b = md5_ff(b, c, d, a, x[i+ 7], 22, -45705983);
	a = md5_ff(a, b, c, d, x[i+ 8], 7 ,  1770035416);
	d = md5_ff(d, a, b, c, x[i+ 9], 12, -1958414417);
	c = md5_ff(c, d, a, b, x[i+10], 17, -42063);
	b = md5_ff(b, c, d, a, x[i+11], 22, -1990404162);
	a = md5_ff(a, b, c, d, x[i+12], 7 ,  1804603682);
	d = md5_ff(d, a, b, c, x[i+13], 12, -40341101);
	c = md5_ff(c, d, a, b, x[i+14], 17, -1502002290);
	b = md5_ff(b, c, d, a, x[i+15], 22,  1236535329);

	a = md5_gg(a, b, c, d, x[i+ 1], 5 , -165796510);
	d = md5_gg(d, a, b, c, x[i+ 6], 9 , -1069501632);
	c = md5_gg(c, d, a, b, x[i+11], 14,  643717713);
	b = md5_gg(b, c, d, a, x[i+ 0], 20, -373897302);
	a = md5_gg(a, b, c, d, x[i+ 5], 5 , -701558691);
	d = md5_gg(d, a, b, c, x[i+10], 9 ,  38016083);
	c = md5_gg(c, d, a, b, x[i+15], 14, -660478335);
	b = md5_gg(b, c, d, a, x[i+ 4], 20, -405537848);
	a = md5_gg(a, b, c, d, x[i+ 9], 5 ,  568446438);
	d = md5_gg(d, a, b, c, x[i+14], 9 , -1019803690);
	c = md5_gg(c, d, a, b, x[i+ 3], 14, -187363961);
	b = md5_gg(b, c, d, a, x[i+ 8], 20,  1163531501);
	a = md5_gg(a, b, c, d, x[i+13], 5 , -1444681467);
	d = md5_gg(d, a, b, c, x[i+ 2], 9 , -51403784);
	c = md5_gg(c, d, a, b, x[i+ 7], 14,  1735328473);
	b = md5_gg(b, c, d, a, x[i+12], 20, -1926607734);

	a = md5_hh(a, b, c, d, x[i+ 5], 4 , -378558);
	d = md5_hh(d, a, b, c, x[i+ 8], 11, -2022574463);
	c = md5_hh(c, d, a, b, x[i+11], 16,  1839030562);
	b = md5_hh(b, c, d, a, x[i+14], 23, -35309556);
	a = md5_hh(a, b, c, d, x[i+ 1], 4 , -1530992060);
	d = md5_hh(d, a, b, c, x[i+ 4], 11,  1272893353);
	c = md5_hh(c, d, a, b, x[i+ 7], 16, -155497632);
	b = md5_hh(b, c, d, a, x[i+10], 23, -1094730640);
	a = md5_hh(a, b, c, d, x[i+13], 4 ,  681279174);
	d = md5_hh(d, a, b, c, x[i+ 0], 11, -358537222);
	c = md5_hh(c, d, a, b, x[i+ 3], 16, -722521979);
	b = md5_hh(b, c, d, a, x[i+ 6], 23,  76029189);
	a = md5_hh(a, b, c, d, x[i+ 9], 4 , -640364487);
	d = md5_hh(d, a, b, c, x[i+12], 11, -421815835);
	c = md5_hh(c, d, a, b, x[i+15], 16,  530742520);
	b = md5_hh(b, c, d, a, x[i+ 2], 23, -995338651);

	a = md5_ii(a, b, c, d, x[i+ 0], 6 , -198630844);
	d = md5_ii(d, a, b, c, x[i+ 7], 10,  1126891415);
	c = md5_ii(c, d, a, b, x[i+14], 15, -1416354905);
	b = md5_ii(b, c, d, a, x[i+ 5], 21, -57434055);
	a = md5_ii(a, b, c, d, x[i+12], 6 ,  1700485571);
	d = md5_ii(d, a, b, c, x[i+ 3], 10, -1894986606);
	c = md5_ii(c, d, a, b, x[i+10], 15, -1051523);
	b = md5_ii(b, c, d, a, x[i+ 1], 21, -2054922799);
	a = md5_ii(a, b, c, d, x[i+ 8], 6 ,  1873313359);
	d = md5_ii(d, a, b, c, x[i+15], 10, -30611744);
	c = md5_ii(c, d, a, b, x[i+ 6], 15, -1560198380);
	b = md5_ii(b, c, d, a, x[i+13], 21,  1309151649);
	a = md5_ii(a, b, c, d, x[i+ 4], 6 , -145523070);
	d = md5_ii(d, a, b, c, x[i+11], 10, -1120210379);
	c = md5_ii(c, d, a, b, x[i+ 2], 15,  718787259);
	b = md5_ii(b, c, d, a, x[i+ 9], 21, -343485551);

	a = safe_add(a, olda);
	b = safe_add(b, oldb);
	c = safe_add(c, oldc);
	d = safe_add(d, oldd);
  }
  return Array(a, b, c, d);
}

function md5_cmn(q, a, b, x, s, t) {
		return safe_add(bit_rol(safe_add(safe_add(a, q), safe_add(x, t)), s), b);
	}

function md5_ff(a, b, c, d, x, s, t) {
	return md5_cmn((b & c) | ((~b) & d), a, b, x, s, t);
}

function md5_gg(a, b, c, d, x, s, t) {
	return md5_cmn((b & d) | (c & (~d)), a, b, x, s, t);
}

function md5_hh(a, b, c, d, x, s, t) {
	return md5_cmn(b ^ c ^ d, a, b, x, s, t);
}

function md5_ii(a, b, c, d, x, s, t) {
	return md5_cmn(c ^ (b | (~d)), a, b, x, s, t);
}
/*
 * Add integers, wrapping at 2^32. This uses 16-bit operations internally
 * to work around bugs in some JS interpreters.
 */
function safe_add(x, y) {
	var lsw = (x & 0xFFFF) + (y & 0xFFFF);
	var msw = (x >> 16) + (y >> 16) + (lsw >> 16);
	return (msw << 16) | (lsw & 0xFFFF);
}
/*
 * Bitwise rotate a 32-bit number to the left.
 */
function bit_rol(num, cnt) {
	return (num << cnt) | (num >>> (32 - cnt));
}
/*
 * Convert a string to an array of little-endian words
 * If chrsz is ASCII, characters >255 have their hi-byte silently ignored.
 */
function str2binl(str) {
	var bin = Array();
	var mask = (1 << chrsz) - 1;
	for (var i = 0; i < str.length * chrsz; i += chrsz)
	bin[i >> 5] |= (str.charCodeAt(i / chrsz) & mask) << (i % 32);
	return bin;
}
/*
 * Convert an array of little-endian words to a hex string.
 */
function binl2hex(binarray) {
	var hex_tab = hexcase ? "0123456789ABCDEF" : "0123456789abcdef";
	var str = "";
	for (var i = 0; i < binarray.length * 4; i++) {
		str += hex_tab.charAt((binarray[i >> 2] >> ((i % 4) * 8 + 4)) & 0xF) + hex_tab.charAt((binarray[i >> 2] >> ((i % 4) * 8)) & 0xF);
	}
	return str;
}

function Utf8Encode(string) {
	var utftext = "";
	for (var n = 0; n < string.length; n++) {
		var c = string.charCodeAt(n);
		if (c < 128) {
			utftext += String.fromCharCode(c);
		} else if ((c > 127) && (c < 2048)) {
			utftext += String.fromCharCode((c >> 6) | 192);
			utftext += String.fromCharCode((c & 63) | 128);
		} else {
			utftext += String.fromCharCode((c >> 12) | 224);
			utftext += String.fromCharCode(((c >> 6) & 63) | 128);
			utftext += String.fromCharCode((c & 63) | 128);
		}
	}
	return utftext;
}
export {
	getSignature,getDataSignature,jsonHandler,parseHandler,urlParamsToObj,randomStr,randomTime
}