<template>
    <el-dialog
        class="custom-dialog"
        :visible.sync="visible"
        :title="'结算单详情'"
        width="80%"
        @close="handleCancel"
        :destroy-on-close="true"
    >
        <BasicInfo :detail="detail"></BasicInfo>
        <snbc-tabs :tabs="tabs" style="margin-top: 16px">
            <template #UsageDetails>
                <UsageDetails :usageList="usageList" :settlementMethod="settlementMethod"></UsageDetails>
            </template>
            <template #PaymentLog>
                <PaymentLog
                    :paymentList="paymentList"
                    :billCode="billCode"
                    :settlementPeriod="detail.settlementPeriod"
                    :settlementState="detail.settlementState"
                    :feeSettlement="detail.feeSettlement"
                ></PaymentLog>
            </template>
            <template #CredentialDetail>
                <CredentialDetail :detail="detail"></CredentialDetail>
            </template>
            <template #OperationLog>
                <OperationLog :logList="logList"></OperationLog>
            </template>
        </snbc-tabs>
        <span slot="footer" class="dialog-footer">
            <el-button type="danger" @click="handleCancel">关 闭</el-button>
        </span>
    </el-dialog>
</template>
<script>
import BasicInfo from './components/BasicInfo.vue';
import OperationLog from './components/OperationLog.vue';
import PaymentLog from './components/PaymentLog.vue';
import UsageDetails from './components/UsageDetails.vue';
import SnbcTabs from 'warehouse/components/snbc-tabs/SnbcTabs.vue';
import CredentialDetail from './components/CredentialDetail.vue';

export default {
    name: 'SettlementDetail',
    components: { BasicInfo, OperationLog, PaymentLog, UsageDetails, SnbcTabs, CredentialDetail },
    data() {
        return {
            visible: false,
            tabs: [
                {
                    label: '区仓使用明细',
                    id: 'UsageDetails'
                },
                {
                    label: '费用支付记录',
                    id: 'PaymentLog'
                },
                {
                    label: '凭证明细',
                    id: 'CredentialDetail'
                },
                {
                    label: '操作日志',
                    id: 'OperationLog'
                }
            ],
            logList: [],
            usageList: [],
            paymentList: [],
            settlementMethod: '',
            detail: {},
            billCode: ''
        };
    },
    methods: {
        showDialog(params) {
            this.detail = { ...params.detail };
            this.logList = params.logList;
            this.usageList = params.usageList.map((item, index) => {
                item.index = index + 1;
                return item;
            });
            this.paymentList = params.paymentList;
            this.settlementMethod = params.detail.settlementMethod;
            this.billCode = params.detail.billCode;
            this.$nextTick(() => {
                this.visible = true;
            });
        },
        // 取消
        handleCancel() {
            this.visible = false;
        }
    }
};
</script>
<style lang="scss" scoped>
.dialog-container {
    max-height: 60vh;
    overflow: auto;
}
</style>
