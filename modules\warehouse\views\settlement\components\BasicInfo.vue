<template>
    <div>
        <snbc-descriptions
            v-if="detail.settlementMethod === '面积'"
            :items="areaDescriptions"
            :config="config"
        />
        <snbc-descriptions
            v-else
            :items="trafficDescriptions"
            :config="config"
        />
    </div>
</template>
<script>
import SnbcDescriptions from 'warehouse/components/snbc-descriptions/SnbcDescriptions';
// 公共 descriptions
const common = [
    { label: '区仓', key: 'warehouseName', value: '' },
    { label: '服务站区仓负责人', key: 'warehouseMaster', value: '' },
    { label: '供应商', key: 'supplier', value: '' },
    { label: '地址', key: 'address', value: '' },
    { label: '联系人', key: 'contact', value: '' },
    { label: '联系电话', key: 'contactTel', value: '' },
    { label: '结算状态', key: 'settlementState', value: '' },
    { label: '结算期', key: 'settlementPeriod', value: '' },
    { label: '结算方式', key: 'settlementMethod', value: '' }
];

// 面积的 descriptions
const areaDescriptions = [
    ...common,
    { label: '合同面积', key: 'areaContract', value: '' },
    { label: '公摊面积', key: 'areaShared', value: '' },
    { label: '计费单价', key: 'unitPrice', value: '' },
    { label: '预计租赁面积', key: 'areaLease', value: '' },
    { label: '实际使用面积', key: 'areaUsage', value: '' },
    { label: '结算面积', key: 'areaSettlement', value: '' },
    { label: '预计费用', key: 'feePrediction', value: '' },
    { label: '实际费用', key: 'feeActual', value: '' },
    { label: '结算费用', key: 'feeSettlement', value: '' },
    { label: '预计面积使用率', key: 'areaUsageRatePrediction', value: '' },
    { label: '结算面积使用率', key: 'areaUsageRateSettlement', value: '' }
];

// 流量的 descriptions
const trafficDescriptions = [
    ...common,
    { label: '资产数量合计：', key: 'assetCount', value: '' },
    { label: '存放天数合计：', key: 'storageDays', value: '' },
    { label: '费用合计：', key: 'feeTotal', value: '' }
];
export default {
    name: 'AllocationLogisticsInfoDetails',
    components: {
        SnbcDescriptions
    },
    props: {
        detail: {
            type: Object,
            default() {
                return {};
            }
        }
    },
    data() {
        return {
            areaDescriptions: [...areaDescriptions],
            trafficDescriptions: [...trafficDescriptions],
            config: {
                elDescriptionsAttrs: {
                    column: 3,
                    labelStyle: {
                        width: '120px'
                    }
                }
            }
        };
    },
    watch: {
        detail: {
            handler: 'init',
            deep: true,
            immediate: true
        }
    },
    methods: {
        init() {
            if (this.detail.settlementMethod === '面积') {
                this.areaDescriptions.forEach((item) => {
                    item.value = this.detail[item.key];
                });
            } else {
                this.trafficDescriptions.forEach((item) => {
                    item.value = this.detail[item.key];
                });
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.no-info {
    line-height: 32px;
    text-align: center;
}
</style>
