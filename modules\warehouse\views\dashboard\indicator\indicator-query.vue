<!-- 区仓指标查询 -->
<template>
    <div class="view">
        <div class="content query-label-line2">
            <snbc-base-table ref="tableRef" :table-config="tableConfig" @query="handleQuery">
                <template #table-info-top>
                    <el-descriptions class="margin-bottom-10" border :column="8">
                        <el-descriptions-item
                            v-for="(item, index) in countList"
                            :key="index"
                            v-bind="$tools.elDescItemLabelAttrs(item.name)"
                        >
                            {{ item.value }}
                        </el-descriptions-item>
                    </el-descriptions>
                </template>
            </snbc-base-table>
        </div>
    </div>
</template>

<script>
import Vue from 'vue';
import functions from 'frame/mixins/functions.js';
import elAttrs from 'warehouse/common/el-attrs/index.js';
import textRender from 'warehouse/common/text-render/index.js';
import objectFactory from 'warehouse/common/object-factory/index.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import commonItems from 'warehouse/common/form-items/common-items.js';
import warehouseItems from 'warehouse/common/form-items/warehouse-items.js';
import pickerOptions from 'warehouse/common/picker-options/index.js';

const { cloneDeep } = Vue.prototype.$tools;

// 查询参数
const queryParams = {
    warehouseCode: '',
    requestDate: [],
    orderRule: 'desc',
    orderMetric: 'traffic'
};
const pageParams = objectFactory('pageParams');

// 支持内存缓存的页面参数
const allParams = {
    queryParams: cloneDeep(queryParams),
    // 分页参数
    pageParams: cloneDeep(pageParams)
};

// 统计周期
const requestDate = {
    ...commonItems.dateRange,
    name: '统计周期',
    modelKey: 'requestDate',
    elDatePickerAttrs: {
        'unlink-panels': true,
        'type': 'monthrange',
        'value-format': 'yyyy-MM',
        'picker-options': {
            shortcuts: [
                pickerOptions.lastMonthOption,
                pickerOptions.lastQuarterOption,
                pickerOptions.firstHalfYearOption,
                pickerOptions.secondHalfYearOption,
                pickerOptions.currentYearOption,
                pickerOptions.lastYearOption
            ]
        }
    }
};

// 查询区域配置项
const queryConfigItems = [
    warehouseItems.warehouseSelect,
    requestDate,
    warehouseItems.orderMetric,
    warehouseItems.orderRule
];
export default {
    name: 'IndicatorQuery',
    components: {
        SnbcBaseTable
    },
    mixins: [functions],
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            tableConfig: {
                queryParams: allParams.queryParams,
                pageParams: allParams.pageParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: this.$service.warehouse.statistics.getIndicatorQueryList,
                // 导出操作
                headerButtons: [
                    {
                        ...elAttrs.exportBtnAttrs,
                        permissionCode: 'WAREHOUSE_INDICATOR_QUERY_EXPORT',
                        handleClick: this.handleExport
                    }
                ],
                elTableColumns: [
                    {
                        label: '序号',
                        prop: 'index',
                        show: true,
                        minWidth: 60
                    },
                    {
                        label: '区仓名称',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 160,
                        renderMode: 'button',
                        elButtonAttrs: {
                            type: 'text'
                        },
                        handleClick: this.indicatorMonthly
                    },
                    {
                        label: '建仓时间',
                        prop: 'createTime',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '供应商',
                        prop: 'supplier',
                        show: true,
                        minWidth: 220
                    },
                    {
                        label: '服务站区仓负责人',
                        prop: 'warehouseMaster',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '当前面积（㎡）',
                        prop: 'warehouseArea',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '累积租赁面积（㎡）',
                        prop: 'warehouseRentArea',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '累积流量（台）',
                        prop: 'traffic',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '计费方式',
                        prop: 'billingMethod',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '单价（元）',
                        prop: 'unitPrice',
                        show: true,
                        minWidth: 120,
                        render(value, row) {
                            if (row.billingMethod === '面积') {
                                return textRender.unitRender(value, '');
                            } else if (row.billingMethod === '流量') {
                                return textRender.unitRender(value, '');
                            }
                            return value;
                        }
                    },
                    {
                        label: '累积支出（元）',
                        prop: 'monthlyCost',
                        show: true,
                        minWidth: 120,
                        render(value) {
                            return textRender.unitRender(value, '');
                        }
                    },
                    {
                        label: '已支付（元）',
                        prop: 'amountPaid',
                        show: true,
                        minWidth: 120,
                        render(value) {
                            return textRender.unitRender(value, '');
                        }
                    },
                    {
                        label: '累积收入（元）',
                        prop: 'monthlyIncome',
                        show: true,
                        minWidth: 120,
                        render(value) {
                            return textRender.unitRender(value, '');
                        }
                    },
                    {
                        label: '平均吞吐量（台次/月）',
                        prop: 'throughput',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '平均库龄（天）',
                        prop: 'averageAge',
                        show: true,
                        minWidth: 120,
                        cellClassName: (column, row) => {
                            return this.$tools.getClassByCompareValue(this.countData.averageAge, row.averageAge);
                        }
                    },
                    {
                        label: '新机平均库龄（天）',
                        prop: 'ageAverageNewAsset',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '撤机平均库龄（天）',
                        prop: 'ageAverageWithDrawAsset',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '平均面积利用率',
                        prop: 'actualAreaUtilizationRate',
                        show: true,
                        minWidth: 120,
                        cellClassName: (column, row) => {
                            return this.$tools.getClassByCompareValue(
                                row.actualAreaUtilizationRate,
                                this.countData.actualAreaUtilizationRate
                            );
                        },
                        render(value) {
                            return textRender.unitRender(value, '%');
                        }
                    },
                    {
                        label: '出入库及时率',
                        prop: 'timelinessRate',
                        show: true,
                        minWidth: 120,
                        cellClassName: (column, row) => {
                            return this.$tools.getClassByCompareValue(
                                row.timelinessRate,
                                this.countData.timelinessRate
                            );
                        },
                        render(value) {
                            return textRender.unitRender(value, '%');
                        }
                    },
                    {
                        label: '货账相符率',
                        prop: 'accuracyRate',
                        show: true,
                        minWidth: 120,
                        cellClassName: (column, row) => {
                            return this.$tools.getClassByCompareValue(row.accuracyRate, this.countData.accuracyRate);
                        },
                        render(value) {
                            return textRender.unitRender(value, '%');
                        }
                    }
                ],
                hooks: {
                    tableListHook: this.tableListHook
                }
            },
            countList: [
                { name: '支出', value: '-' },
                { name: '已支付', value: '-' },
                { name: '收入', value: '-' },
                { name: '货账相符率', value: '-' },
                { name: '面积利用率', value: '-' },
                { name: '出入库及时率', value: '-' },
                { name: '平均库龄', value: '-' },
                { name: '累计吞吐量', value: '-' }
            ],
            // 统计数据对象
            countData: {}
        };
    },
    beforeCreate() {
        if (!this.$store.getters.toggleView) {
            allParams.queryParams = cloneDeep(queryParams);
            allParams.pageParams = cloneDeep(pageParams);
        }
    },
    created() {
        // 非tag切换进入，重置页面参数至默认状态
        if (!this.$store.getters.toggleView) {
            this.$tools.setQueryParams.call(this);
        }
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.queryList();
        this.getStatistics();
    },
    methods: {
        // 列表查询操作
        handleQuery() {
            this.getStatistics();
        },
        // 列表数据处理
        tableListHook(list) {
            list.map((item) => {
                return item;
            });
        },
        // 导出操作
        async handleExport() {
            const { exportIndicatorQueryList } = this.$service.warehouse.statistics;
            const params = this.$tools.cloneDeep(this.tableConfig.queryParams);
            const stream = await exportIndicatorQueryList(params);
            this.$tools.downloadExprotFile(stream, '区仓指标', 'xlsx').catch((e) => {
                this.$tools.message.err(e || '导出失败');
            });
        },
        // 汇总数据
        async getStatistics() {
            const { unitRender } = textRender;
            this.countData = {};
            // 设置具体值
            this.$tools.setIndicator(this.countList, {
                支出: unitRender('', '元'),
                已支付: unitRender('', '元'),
                收入: unitRender('', '元'),
                货账相符率: unitRender('', '%'),
                面积利用率: unitRender('', '%'),
                出入库及时率: unitRender('', '%'),
                平均库龄: unitRender('', '天'),
                累计吞吐量: unitRender('', '台次')
            });
            const { getIndicatorQueryStatistics } = this.$service.warehouse.statistics;
            try {
                const res = await getIndicatorQueryStatistics(this.tableConfig.queryParams);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.countData = res.result || {};
                const {
                    monthlyCost,
                    monthlyIncome,
                    amountPaid,
                    accuracyRate,
                    actualAreaUtilizationRate,
                    timelinessRate,
                    averageAge,
                    monthlyThroughput
                } = this.countData;
                // 设置具体值
                this.$tools.setIndicator(this.countList, {
                    支出: unitRender(monthlyCost, '元'),
                    已支付: unitRender(amountPaid, '元'),
                    收入: unitRender(monthlyIncome, '元'),
                    货账相符率: unitRender(accuracyRate, '%'),
                    面积利用率: unitRender(actualAreaUtilizationRate, '%'),
                    出入库及时率: unitRender(timelinessRate, '%'),
                    平均库龄: unitRender(averageAge, '天'),
                    累计吞吐量: unitRender(monthlyThroughput, '台次')
                });
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 跳转月指标明细页面
        indicatorMonthly(item) {
            this.$router.push({
                path: '/app/dashboard/indicator-monthly',
                query: {
                    warehouseCode: item.warehouseCode,
                    requestDate: queryParams.requestDate
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
