<template>
    <el-dialog class="custom-dialog" :visible.sync="dialogVisible" v-bind="elDialogAttrs">
        <el-table
            :data="rulesData"
            class="snbc-table indicator-calculation-rules-table"
            v-bind="elTableAttrs"
            :span-method="mergeRows"
        >
            <el-table-column
                prop="name"
                label="指标名称"
                width="200"
                align="center"
                header-align="center"
            ></el-table-column>
            <el-table-column prop="rule" label="指标计算规则" header-align="center">
                <template slot-scope="scope">
                    <template v-if="scope.row.type === 'word-cell'">
                        <div v-for="(item, index) in scope.row.rule" :key="index">{{ item }}</div>
                    </template>
                    <span v-if="!scope.row.type">{{ scope.row.rule }}</span>
                </template>
            </el-table-column>
        </el-table>
    </el-dialog>
</template>
<script>
import ElAttrs from 'warehouse/common/el-attrs/index.js';

const { elDialogAttrs, elTableAttrs } = ElAttrs;
export default {
    name: 'IndicatorCalculationRules',
    components: {},
    data() {
        return {
            elTableAttrs,
            dialogVisible: false,
            elDialogAttrs: {
                ...elDialogAttrs,
                width: '950px',
                title: '指标计算规则'
            },
            rulesData: [
                {
                    name: '货账相符率',
                    rule: '∑统计周期内所有区仓每次盘点的正常资产数量÷（∑统计周期内所有区仓每次盘点的正常数量+∑统计周期内每次盘点盘亏数量+∑统计周期内每次盘点盘赢数量）',
                    rowspan: 1
                },
                {
                    name: '面积利用率',
                    rule: [
                        '∑统计周期内每个区仓每个月占用面积平均值÷∑统计周期内每个区仓的租赁面积',
                        '（区仓租赁面积=区仓面积-消防公摊面积-建筑公摊面积，其中区仓面积为区仓基本信息中维护的面积数据，消防公摊面积=区仓面积*消防公摊系数，建筑公摊面积=区仓面积*建筑公摊系数）'
                    ],
                    type: 'word-cell',
                    rowspan: 1
                },
                {
                    name: '出入库及时率',
                    rule: '统计数据平均值：（∑统计周期内每个区仓每月吞吐量-∑统计周期内每个区仓异常出入库数量）÷∑统计周期内每个区仓每月吞吐量（异常出入库：系统自动生成的出入库数据）',
                    rowspan: 2
                },
                {
                    name: '出入库及时率',
                    rule: '年度实时数据：（本年度实时出入库数量-本年度实时异常出入库数量）÷本年度实时出入库数量',
                    rowspan: 0
                },
                {
                    name: '平均库龄',
                    rule: '统计数据平均值：∑统计周期内每个区仓的库龄合计÷∑统计周期内每个区仓的资产数量',
                    rowspan: 2
                },
                {
                    name: '平均库龄',
                    rule: '年度实时数据：（∑当前在库资产库龄+∑本年度出库资产库龄）÷（实时在库资产数量+本年度出库资产数量）',
                    rowspan: 0
                }
            ]
        };
    },
    methods: {
        // 打开弹窗
        openDialog() {
            this.dialogVisible = true;
        },
        // 合并行方法
        mergeRows({ row, column, rowIndex, columnIndex }) {
            if (columnIndex === 0) {
                const _row = row.rowspan;
                const _col = _row > 0 ? 1 : 0;
                return {
                    rowspan: _row,
                    colspan: _col
                };
            }
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .indicator-calculation-rules-table {
    border: 1px solid #dddddd;
    th,
    td {
        border: 1px solid #dddddd;
    }
}
</style>
