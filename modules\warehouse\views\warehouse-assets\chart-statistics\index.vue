<template>
    <div class="view">
        <div class="content">
            <snbc-table-query
                :query-params="tableConfig.queryParams"
                :query-config="tableConfig.queryConfig"
                @query="handleQuery"
                @reset="handleReset"
            />
            <div ref="chartRef" class="chart-box" />
        </div>
    </div>
</template>

<script>
import echarts from 'echarts';
import resize from 'wtf-core-vue/src/components/Charts/mixins/resize';
import functions from 'frame/mixins/functions.js';
import SnbcTableQuery from 'warehouse/components/snbc-table/SnbcTableQuery.vue';
import FormItems from 'warehouse/common/form-items/index.js';

const { warehouseMultiSelect, dateRange } = FormItems;

// 统计周期
const statisticalCycle = {
    name: '统计周期',
    component: 'SnbcFormSelect',
    modelKey: 'statisticalCycle',
    elOptions: [
        { label: '按天', value: '1' },
        { label: '按周', value: '2' },
        { label: '按月', value: '3' }
    ]
};
// 统计指标
const statisticalIndicators = {
    name: '统计指标',
    component: 'SnbcFormSelect',
    modelKey: 'statisticalIndicator',
    elOptions: [
        { label: '指标1', value: '1' },
        { label: '指标2', value: '2' }
    ]
};

// 默认查询条件
const defaultQueryParams = {
    warehouseSelect: [],
    statisticalCycle: '',
    dateRange: [],
    statisticalIndicator: ''
};

export default {
    name: 'WarehouseSummaryStatistics',
    components: {
        SnbcTableQuery
    },
    mixins: [functions, resize],
    data() {
        return {
            // 图表实例
            chart: null, 
            tableConfig: {
                queryParams: {
                    ...defaultQueryParams
                },
                queryConfig: {
                    items: [warehouseMultiSelect, statisticalCycle, dateRange, statisticalIndicators]
                }
            }
        };
    },
    computed: {},
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.initChart();
    },
    beforeDestroy() {
        if (!this.chart) {
            return;
        }
        this.chart.dispose();
        this.chart = null;
    },
    methods: {
        // 查询操作
        handleQuery() {
            this.initChart();
        },
        // 重置操作
        handleReset() {
            this.tableConfig.queryParams = {
                ...defaultQueryParams
            };
        },
        initChart() {
            this.chart = echarts.init(this.$refs.chartRef);
            this.chart.setOption({
                title: {
                    text: '折线图堆叠'
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['邮件营销', '联盟广告', '视频广告', '直接访问', '搜索引擎']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '邮件营销',
                        type: 'line',
                        stack: '总量',
                        data: [120, 132, 101, 134, 90, 230, 210]
                    },
                    {
                        name: '联盟广告',
                        type: 'line',
                        stack: '总量',
                        data: [220, 182, 191, 234, 290, 330, 310]
                    },
                    {
                        name: '视频广告',
                        type: 'line',
                        stack: '总量',
                        data: [150, 232, 201, 154, 190, 330, 410]
                    },
                    {
                        name: '直接访问',
                        type: 'line',
                        stack: '总量',
                        data: [320, 332, 301, 334, 390, 330, 320]
                    },
                    {
                        name: '搜索引擎',
                        type: 'line',
                        stack: '总量',
                        data: [820, 932, 901, 934, 1290, 1330, 1320]
                    }
                ]
            });
        }
    }
};
</script>

<style lang="scss" scoped>
    .chart-box {
        width: 100%;
        height: 400px;
    }
</style>
