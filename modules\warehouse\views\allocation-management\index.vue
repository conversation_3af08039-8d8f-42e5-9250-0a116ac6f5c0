<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig">
                <template #tabs>
                    <snbc-table-tabs :tabs-config="tabsConfig" />
                </template>
            </snbc-base-table>
        </div>
        <LogisticsSourcingDialog ref="logisticsSourcingRef"></LogisticsSourcingDialog>
        <ActualLogisticsInfoDialog ref="logisticsInfoRef"></ActualLogisticsInfoDialog>
        <TaskAdditionalForm ref="taskAdditionalFormRef"> </TaskAdditionalForm>
    </div>
</template>
<script>
import Vue from 'vue';
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import { queryItems } from './formItems';
import LogisticsSourcingDialog from './components/LogisticsSourcingDialog.vue';
import ActualLogisticsInfoDialog from './components/ActualLogisticsInfoDialog.vue';
import TaskAdditionalForm from 'warehouse/views/warehouse-error-deal/task-additional/components/TaskAdditionalForm';
import SnbcTableTabs from 'warehouse/components/snbc-table/SnbcTableTabs.vue';
import objectFactory from 'warehouse/common/object-factory/index.js';
import { mapState } from 'vuex';

const { cloneDeep } = Vue.prototype.$tools;

const queryParams = {
    allocateTaskName: '',
    allocateTaskCode: '',
    sourceWarehouseCode: '',
    targetWarehouseCode: '',
    taskSource: '',
    stockRequestCode: '',
    taskState: [],
    expectedDateRange: [],
    createDateRange: [],
    tabSelected: ''
};

const pageParams = objectFactory('pageParams');

const tabsConfig = {
    activeName: '准备中',
    tabItems: ['准备中', '调拨中', '结算中', '已完成']
};

// 支持内存缓存的页面参数
const allParams = {
    queryParams: cloneDeep(queryParams),
    pageParams: cloneDeep(pageParams),
    tabsConfig: cloneDeep(tabsConfig)
};

export default {
    name: 'AllocationManager',
    components: {
        SnbcBaseTable,
        LogisticsSourcingDialog,
        ActualLogisticsInfoDialog,
        TaskAdditionalForm,
        SnbcTableTabs
    },
    mixins: [functions],
    data() {
        const { queryAllocateTask: listApi } = this.$service.warehouse.allocation;
        return {
            listApi,
            tableConfig: {
                queryParams: allParams.queryParams,
                pageParams: allParams.pageParams,
                queryConfig: {
                    items: [...queryItems],
                    elFormAttrs: {
                        'label-width': '120px'
                    },
                    seniorShow: true
                },
                queryApi: listApi,
                elTableColumns: [],
                operations: [],
                headerButtons: [
                    {
                        name: '+ 新建指定资产调拨',
                        type: 'primary',
                        handleClick: this.handleClickAdd,
                        permissionCode: 'WGBJHY'
                    }
                ],
                operationColumnWidth: 320,
                hooks: {
                    tableListHook: this.tableListHook,
                    queryParamsHook: this.queryParamsHook
                }
            },
            isDetails: false,
            isAlive: false,
            tabsConfig: {
                handleTabClick: this.handleTabClick
            }
        };
    },
    computed: {
        ...mapState(['permission'])
    },
    beforeCreate() {
        if (!this.$store.getters.toggleView) {
            // 支持内存缓存的页面参数
            allParams.queryParams = cloneDeep(queryParams);
            allParams.pageParams = cloneDeep(pageParams);
            allParams.tabsConfig = cloneDeep(tabsConfig);
        }
    },
    created() {
        // 非tag切换进入，重置页面参数至默认状态
        if (!this.$store.getters.toggleView) {
            this.$tools.setQueryParams.call(this);
        }
        this.init();
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.queryList();
    },
    methods: {
        // 页面初始化
        init() {
            this.tabsConfig = Object.assign(allParams.tabsConfig, {
                handleTabClick: this.handleTabClick
            });
            this.tableConfig.queryParams.tabSelected = this.tabsConfig.activeName;
            this.tableButtonInit();
            this.tableColumnInit();
            if (this.$route.query.stockRequestCode) {
                this.tableConfig.queryParams.stockRequestCode = this.$route.query.stockRequestCode;
            }
        },
        // 初始化 table 的 button
        // eslint-disable-next-line max-lines-per-function
        tableButtonInit() {
            const operations = [
                {
                    name: '编辑',
                    type: 'primary',
                    handleClick: this.handleEdit,
                    handleShow: (item) =>
                        ['草稿', '下发退回'].includes(item.taskState) && this.checkPermission('WGBJHY')
                },
                {
                    name: '下发',
                    type: 'success',
                    handleClick: this.handleAllots,
                    handleShow: (item) =>
                        ['草稿', '下发退回'].includes(item.taskState) && this.checkPermission('WGBJHY')
                },
                {
                    name: '审核',
                    type: 'success',
                    handleClick: this.handleAudit,
                    handleShow: (item) => item.taskState === '待审核' && this.checkPermission('WGBFZR')
                },
                {
                    name: '寻源结果录入',
                    type: 'warning',
                    handleClick: this.handleInput,
                    handleShow: (item) =>
                        ['寻源退回', '待寻源'].includes(item.taskState) && this.checkPermission('WGBWLJL')
                },
                {
                    name: '录入结算结果',
                    type: 'primary',
                    handleClick: this.handleSettle,
                    handleShow: (item) =>
                        ['结算退回', '待结算'].includes(item.taskState) && this.checkPermission('WGBWLJL')
                },
                {
                    name: '物流信息确认',
                    type: 'success',
                    handleClick: this.handleConfirm,
                    handleShow: (item) => item.taskState === '待确认' && this.checkPermission('WGBFZR')
                },
                {
                    name: '强制完成',
                    type: 'primary',
                    handleClick: this.handleConfirmInAndOut,
                    handleShow: (item) => item.taskState === '调拨中' && this.checkPermission('WGBJHY')
                },
                {
                    name: '出库补录',
                    type: 'warning',
                    handleClick: this.handleSupplementOut,
                    handleShow: (item) => item.canOutboundSupplement && this.checkPermission('FWZQCZRR')
                },
                {
                    name: '入库补录',
                    type: 'warning',
                    handleClick: this.handleSupplementIn,
                    handleShow: (item) => item.canInboundSupplement && this.checkPermission('FWZQCZRR')
                },
                {
                    name: '删除',
                    type: 'danger',
                    handleClick: this.handleDelete,
                    handleShow: (item) => item.taskState === '草稿' && this.checkPermission('WGBJHY')
                },
                {
                    name: '终止',
                    type: 'danger',
                    handleClick: this.handleTerminate,
                    handleShow: (item) =>
                        item.taskState !== '草稿' &&
                        !item.inboundNumber &&
                        !item.outboundNumber &&
                        item.taskState !== '已终止' &&
                        this.checkPermission('WGBJHY')
                }
            ];
            this.tableConfig.operations = operations;
        },
        // 初始化table 的 列
        // eslint-disable-next-line max-lines-per-function
        tableColumnInit() {
            const columns = [
                { label: '序号', prop: 'index', show: true, minWidth: 80 },
                {
                    label: '调拨任务编号',
                    prop: 'allocateTaskCode',
                    show: true,
                    minWidth: 200,
                    renderMode: 'button',
                    handleClick: this.handleDetail,
                    elButtonAttrs: {
                        type: 'text'
                    }
                },
                {
                    label: '调拨任务名称',
                    prop: 'allocateTaskName',
                    show: true,
                    minWidth: 240
                },
                {
                    label: '发货进度',
                    prop: 'outboundProgress',
                    show: true,
                    minWidth: 120,
                    renderMode: 'button',
                    handleClick: this.handleShipmentsProgress,
                    elButtonAttrs: {
                        type: 'text'
                    }
                },
                {
                    label: '到货进度',
                    prop: 'inboundProgress',
                    show: true,
                    minWidth: 120,
                    renderMode: 'button',
                    handleClick: this.handleArrivalPrograss,
                    elButtonAttrs: {
                        type: 'text'
                    }
                },
                {
                    label: '任务状态',
                    prop: 'taskState',
                    show: true,
                    minWidth: 120
                },
                {
                    label: '来源区仓',
                    prop: 'sourceWarehouseName',
                    show: true,
                    minWidth: 160
                },
                {
                    label: '目标区仓',
                    prop: 'targetWarehouseName',
                    show: true,
                    minWidth: 160
                },

                {
                    label: '任务来源',
                    prop: 'taskSource',
                    show: true,
                    minWidth: 120
                },
                {
                    label: '备货申请任务编号',
                    prop: 'stockRequestCode',
                    show: true,
                    minWidth: 200
                },
                {
                    label: '期望到货日期',
                    prop: 'expectedDate',
                    show: true,
                    minWidth: 120
                },
                {
                    label: '创建时间',
                    prop: 'createTime',
                    show: true,
                    minWidth: 160
                }
            ];
            this.tableConfig.elTableColumns = columns;
        },
        // 下发
        async handleAllots(item) {
            try {
                await this.$tools.confirm('下发后调拨任务进入物流寻源环节，请确认是否下发？');
                const { result } = await this.$service.warehouse.allocation.issueAllocateTask(item.allocateTaskCode);
                if (result) {
                    this.$tools.message.suc('调拨任务下发成功');
                    this.$refs.tableRef.handleQuery();
                } else {
                    this.$tools.message.err('调拨任务下发失败，请刷新页面重试');
                    return;
                }
            } catch (error) {
                return error.message;
            }
        },
        // 删除
        async handleDelete(item) {
            try {
                await this.$tools.confirm('删除后不可恢复，请确认是否删除？', '删除');
                const { result } = await this.$service.warehouse.allocation.deleteAllocateTask(item.allocateTaskCode);
                if (result) {
                    this.$tools.message.suc('调拨任务删除成功');
                    this.$refs.tableRef.handleQuery();
                } else {
                    this.$tools.message.err('调拨任务删除失败，请刷新页面重试');
                    return;
                }
            } catch (error) {
                return error.message;
            }
        },
        // 详情 调拨任务编码和是否指定资产
        handleDetail(item) {
            this.$router.push({
                path: '/app/allocation/allocation-details',
                query: {
                    allocateTaskCode: item.allocateTaskCode,
                    whetherSpecifyAsset: item.whetherSpecifyAsset,
                    type: 'details'
                }
            });
        },
        // 编辑
        handleEdit(item) {
            this.$router.push({
                path: '/app/allocation/allocation-edit',
                query: {
                    allocateTaskCode: item.allocateTaskCode,
                    whetherSpecifyAsset: item.whetherSpecifyAsset,
                    type: 'edit'
                }
            });
        },
        // 审核
        handleAudit(item) {
            this.$router.push({
                path: '/app/allocation/allocation-details',
                query: {
                    type: 'audit',
                    allocateTaskCode: item.allocateTaskCode,
                    whetherSpecifyAsset: item.whetherSpecifyAsset
                }
            });
        },
        // 获取历程
        async getMileage(item) {
            const { code, result, message } = await this.$service.warehouse.allocation.getMileage({
                awarehouseCode: item.sourceWarehouseCode,
                bwarehouseCode: item.targetWarehouseCode
            });
            if (code !== '000000') {
                this.$tools.message.err(message || '获取里程失败，请刷新重试');
                return;
            }
            if (!result.list.length) {
                this.$tools.message.err('获取里程失败，请刷新重试');
                return;
            }
            const mileage = result.list.pop().mileageNumber;
            if (!mileage) {
                this.$tools.message.err('获取里程失败，请刷新重试');
                return;
            }
            return mileage;
        },
        // 寻源结果录入
        async handleInput(item) {
            const mileage = await this.getMileage(item);
            const { result: form } = await this.$service.warehouse.allocation.queryLogisticsSourceResult(
                item.allocateTaskCode
            );
            for (const [key, value] of Object.entries(form || {})) {
                if (typeof value === 'number' && !['mileage', 'cubicNumber'].includes(key)) {
                    form[key] = value / 100;
                }
            }
            if (!mileage) return;
            this.$refs.logisticsSourcingRef.showDialog(item.allocateTaskCode, mileage, form);
        },
        // 录入结算结果,实际物流信息录入弹窗
        async handleSettle(item) {
            const mileage = await this.getMileage(item);
            if (!mileage) return;
            this.$refs.logisticsInfoRef.showDialog({
                isDetails: false,
                allocateTaskCode: item.allocateTaskCode,
                mileage
            });
        },
        // 物流信息确认
        handleConfirm(item) {
            this.$refs.logisticsInfoRef.showDialog({
                isDetails: true,
                allocateTaskCode: item.allocateTaskCode
            });
        },
        formatAdditionObj(item, taskAdditionalType) {
            return [
                {
                    baseInfo: {
                        ...item,
                        associatedTaskCode: item.allocateTaskCode,
                        taskAdditionalType,
                        associatedTaskName: item.allocateTaskName,
                        warehouseCode: item.targetWarehouseCode,
                        warehouseName: item.targetWarehouseName,
                        srcWarehouseName: item.sourceWarehouseName,
                        srcWarehouseCode: item.sourceWarehouseCode,
                        source: '人工补录',
                        associatedTaskType: '调拨'
                    },
                    additionalRecords: [],
                    additionalLogs: []
                },
                'add'
            ];
        },
        // 出库补录
        handleSupplementOut(item) {
            this.$refs.taskAdditionalFormRef.openDialog(...this.formatAdditionObj(item, '出库'));
        },

        // 入库补录
        handleSupplementIn(item) {
            this.$refs.taskAdditionalFormRef.openDialog(...this.formatAdditionObj(item, '入库'));
        },
        // 终止
        async handleTerminate(item) {
            try {
                await this.$tools.confirm('终止后无法恢复，请确认是否终止？', '终止');
                const { code, message } = await this.$service.warehouse.allocation.stopAllocateTask(
                    item.allocateTaskCode
                );
                if (code !== '000000') {
                    this.$tools.message.err(message || '调拨任务终止失败，请刷新页面重试');
                    return;
                }
                this.$tools.message.suc('调拨任务终止成功');
                this.$refs.tableRef.handleQuery();
            } catch (error) {
                return false;
            }
            this.$refs.tableRef.handleQuery();
        },
        // 发货进度
        handleShipmentsProgress(item) {
            this.$router.push({
                path: '/app/assets/operate-log',
                query: {
                    associatedTaskCode: item.allocateTaskCode,
                    operationType: '出库'
                }
            });
        },
        // 到货进度
        handleArrivalPrograss(item) {
            this.$router.push({
                path: '/app/assets/operate-log',
                query: {
                    associatedTaskCode: item.allocateTaskCode,
                    operationType: '入库'
                }
            });
        },
        // 新建指定资产调拨
        handleClickAdd() {
            this.$router.push({
                path: '/app/allocation/allocation-edit',
                query: {
                    allocateTaskCode: null,
                    whetherSpecifyAsset: 1,
                    type: 'add'
                }
            });
        },
        // 确认出入库完成
        async handleConfirmInAndOut(item) {
            try {
                const remark = await this.$tools.prompt('请填写强制完成原因', '调拨任务强制完成', {
                    inputValidator: (msg) => {
                        return !!(msg && msg.length <= 255);
                    },
                    inputErrorMessage: '强制完成原因为必填,且最多为255字符',
                    inputType: 'textarea'
                });
                const { code, message } = await this.$service.warehouse.allocation.confirmCompleteOutboundAndInbound({
                    allocateTaskCode: item.allocateTaskCode,
                    remark
                });
                if (code !== '000000') {
                    this.$tools.message.err(message || '强制完成失败，请确认调拨任务处于调拨中状态，并比对出入库记录');
                    return;
                }
                this.$tools.message.suc('调拨任务强制完成成功');
                this.$refs.tableRef.handleQuery();
            } catch (error) {
                return false;
            }
            this.$refs.tableRef.handleQuery();
        },
        // 重新搜索
        handleQuery() {
            this.$refs.tableRef.handleQuery();
        },
        // 格式化表单显示
        tableListHook(list) {
            list.map((item) => {
                item.inboundProgress = `${item.inboundNumber || 0}/${item.allocateTotalNumber}`;
                item.outboundProgress = `${item.outboundNumber || 0}/${item.allocateTotalNumber}`;
                return item;
            });
        },
        // 格式化查询，根据标签来传值
        queryParamsHook(params) {
            this.tableConfig.queryParams.tabSelected = this.tabsConfig.activeName;
            const stateMap = {
                准备中: [
                    'draft',
                    'rejected_pending_issue',
                    'pending_sourcing',
                    'rejected_pending_sourcing',
                    'pending_approval'
                ],
                调拨中: ['allocating'],
                结算中: ['pending_settlement', 'rejected_pending_settlement', 'pending_confirmation'],
                已完成: ['completed', 'terminated']
            };
            if (!params.taskState.length) {
                params.taskState = stateMap[this.tableConfig.queryParams.tabSelected];
            }
        },
        // 点击tab标签
        handleTabClick() {
            this.tableConfig.queryParams.tabSelected = this.tabsConfig.activeName;
            queueMicrotask(() => {
                this.handleQuery();
            });
        }
    }
};
</script>
<style lang="scss" scoped></style>
