<template>
    <div class="view">
        <div class="content">
            <snbc-base-table ref="tableRef" :table-config="tableConfig" />
            <snbc-base-dialog ref="dialogRef" :config="dialogConfig" @confirm="handleSubmit">
                <template slot="dialog-body">
                    <snbc-form ref="snbcFormRef" :form="form" :config="formConfig">
                        <template #form-body>
                            <snbc-form-item v-for="(item, index) in formItems" :key="index" :config="item" />
                            <snbc-form-item v-if="mode === 'add'" :config="ruleType"></snbc-form-item>
                            <snbc-form-item v-if="hide === 'false'" ref="forgiveDayRef"
                                :config="forgiveDay"></snbc-form-item>
                            <snbc-form-item :config="ruleState"></snbc-form-item>
                        </template>
                    </snbc-form>
                </template>
            </snbc-base-dialog>
        </div>
    </div>
</template>

<script>
import functions from 'frame/mixins/functions.js';
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcBaseDialog from 'warehouse/components/snbc-dialog/SnbcBaseDialog.vue';
import SnbcForm from 'warehouse/components/snbc-form/SnbcForm.vue';
import SnbcFormItem from 'warehouse/components/snbc-form/SnbcFormItem.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import FormRules from 'warehouse/common/form-rules/index.js';

const {
    warehouseName,
    productSelect,
    warehouseSelect,
    warehouseMultiSelect,
    productMultiSelect,
    productName,
    ruleState,
    ruleType,
    forgiveDay
} = FormItems;

const {
    selectRequired,
    inputRequired,
    inputRequiredOnBlur
} = FormRules;

// 弹窗校验规则
const rules = {
    warehouseCodeList: [selectRequired('区仓名称')],
    warehouseName: [inputRequired('区仓名称')],
    productName: [inputRequired('产品名称')],
    productIdList: [selectRequired('产品名称')],
    ruleType: [selectRequired('规则类型')],
    forgiveDay: [inputRequiredOnBlur('宽恕天数')],
    ruleState: [selectRequired('规则状态')]
};

// 查询参数
const queryParams = {
    ruleType: '',
    ruleState: ''
};

// 查询区域配置项
const queryConfigItems = [
    warehouseSelect,
    productSelect,
    ruleType,
    ruleState
];

// 新增弹窗配置
const addDialogConfigItems = [
    warehouseMultiSelect,
    productMultiSelect
];

// 编辑弹窗配置
const editDialogConfigItems = [
    {
        ...warehouseName,
        elInputAttrs: {
            disabled: true
        }
    },
    {
        ...productName,
        elInputAttrs: {
            disabled: true
        }
    },
    {
        ...ruleType,
        elSelectAttrs: {
            disabled: true
        }
    }
];

const form = {
    productIdList: [],
    warehouseCodeList: [],
    productName: '',
    warehouseName: '',
    freeDay: '',
    ruleType: '',
    ruleState: '',
    forgiveDay: undefined
}

export default {
    name: 'WarehouseEntryAndExitRules',
    components: {
        SnbcBaseTable,
        SnbcBaseDialog,
        SnbcForm,
        SnbcFormItem
    },
    mixins: [functions],
    data() {
        const {
            list: listApi,
            add: addApi,
            edit: editApi,
            editState: editStateApi
        } = this.$service.warehouse.warehouseEntryAndExitRules;
        return {
            listApi,
            addApi,
            editApi,
            editStateApi,
            tableConfig: {
                queryParams,
                queryConfig: {
                    items: queryConfigItems
                },
                queryApi: listApi,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    { label: '区仓名称', prop: 'warehouseName', show: true, minWidth: 120 },
                    { label: '产品名称', prop: 'productName', show: true, minWidth: 120 },
                    { label: '规则类型', prop: 'ruleType', show: true, minWidth: 120 },
                    {
                        label: '规则状态', prop: 'ruleState', show: true, minWidth: 100,
                        renderMode: 'tag',
                        elTagAttrsFn(row) {
                            return {
                                type: { '启用': 'success', '停用': 'warning' }[row.ruleState]
                            };
                        }
                    },
                    { label: '宽恕时间', prop: 'forgiveDay', show: true, minWidth: 100 },
                    { label: '创建时间', prop: 'createTime', show: true, minWidth: 160 }
                ],
                operations: [
                    {
                        name: '启用', type: 'success', handleClick: this.handleEnable,
                        handleShow(row) {
                            return row.ruleState === '停用';
                        }
                    },
                    {
                        name: '停用', type: 'warning', handleClick: this.handleDisable,
                        handleShow(row) {
                            return row.ruleState === '启用';
                        }
                    },
                    { name: '编辑', type: 'primary', handleClick: this.handleEdit },
                ],
                headerButtons: [
                    { name: '新增', type: 'primary', handleClick: this.handleAdd }
                ]
            },
            form: {
                ...form
            },
            formConfig: {
                elFormAttrs: {
                    'label-width': '120px',
                    rules
                }
            },
            // 弹窗配置
            dialogConfig: {
                rules,
                items: editDialogConfigItems,
                elDialogAttrs: {
                    title: ''
                }
            },
            mode: 'add',
            hide: 'true'
        };
    },
    computed: {
        ruleType() {
            return {
                modelObj: this.form,
                elFormItemAttrs: {
                    label: ruleType.name,
                    ...(ruleType.elFormItemAttrs || {})
                },
                ...ruleType
            }
        },
        forgiveDay() {
            return {
                modelObj: this.form,
                elFormItemAttrs: {
                    label: forgiveDay.name,
                    ...(forgiveDay.elFormItemAttrs || {})
                },
                ...forgiveDay
            }
        },
        ruleState() {
            return {
                modelObj: this.form,
                elFormItemAttrs: {
                    label: ruleState.name,
                    ...(ruleState.elFormItemAttrs || {})
                },
                ...ruleState
            }
        },
        formItems() {
            const addItems = addDialogConfigItems.map((item) => {
                return {
                    modelObj: this.form,
                    elFormItemAttrs: {
                        label: item.name,
                        ...(item.elFormItemAttrs || {})
                    },
                    ...item
                };
            });
            const editItems = editDialogConfigItems.map((item) => {
                return {
                    modelObj: this.form,
                    elFormItemAttrs: {
                        label: item.name,
                        ...(item.elFormItemAttrs || {})
                    },
                    ...item
                };
            });
            return this.mode === 'add' ? addItems : editItems
        }
    },
    watch: {
        'form.ruleType': function (newVal, oldVal) {
            if (newVal === '先入先出') {
                this.hide = 'false';
            } else {
                this.hide = 'true';
                this.forgiveDay.modelObj.forgiveDay = '';
            }
        }
    },
    mounted() {
        this.isHasMenu();
        this.initNativeDrop();
        this.$refs.tableRef.handleQuery();
    },
    methods: {
        // 弹窗打开
        openDialog(mode) {
            this.mode = mode;
            this.$refs.dialogRef.openDialog();
        },
        // 编辑操作
        handleEdit(row) {
            this.openDialog('edit');
            Object.assign(this.form, row);
            if (this.form.ruleType === '先入先出') {
                this.hide = 'false';
            } else {
                this.hide = 'true';
            }
            this.dialogConfig.elDialogAttrs.title = '区仓资产出入规则编辑'
        },
        // 新增操作
        handleAdd() {
            this.openDialog('add');
            // 移除校验结果并重置数据
            this.$refs.snbcFormRef && this.$refs.snbcFormRef.getFormRef().resetFields();
            Object.assign(this.form, form);
            this.dialogConfig.elDialogAttrs.title = '区仓资产出入规则新增'
        },
        // 启用操作
        async handleEnable(item) {
            await this.$tools.confirm('确认启用？');
            try {
                const res = await this.editStateApi(item.id, '启用');
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('启用成功');
                await this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 禁用操作
        async handleDisable(item) {
            await this.$tools.confirm('确认停用？');
            try {
                const res = await this.editStateApi(item.id, '停用');
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('停用成功');
                await this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 新增或编辑提交
        async handleSubmit() {
            try {
                // 表单校验
                await this.$refs.snbcFormRef.getFormRef().validate();
            } catch (e) {
                return e.message
            }
            await this.$tools.confirm('确认提交？');
            const params = {};
            let submitApi = this.addApi;
            if (this.mode === 'edit') {
                params.id = this.form.id;
                params.ruleType = this.form.ruleType;
                params.warehouseCodeList = [this.form.warehouseCode];
                params.productIdList = [this.form.productId];
                params.ruleState = this.form.ruleState;
                params.forgiveDay = this.form.forgiveDay;
                submitApi = this.editApi;
            } else {
                params.ruleType = this.form.ruleType;
                params.warehouseCodeList = this.form.warehouseCodeList;
                params.productIdList = this.form.productIdList;
                params.ruleState = this.form.ruleState;
                params.forgiveDay = this.form.forgiveDay;
            }
            try {
                const res = await submitApi(params);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc('操作成功');
                this.$refs.dialogRef.hideDialog();
                await this.$refs.tableRef.queryList();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        }
    }
};
</script>

<style lang="scss" scoped></style>
