<template>
    <el-dialog
        class="custom-dialog"
        :visible.sync="visible"
        :title="'支付记录新增'"
        width="40%"
        @close="handleCancel"
        :destroy-on-close="true"
        :modal="false"
    >
        <snbc-form ref="snbcFormRef" :form="form" :config="formConfig">
            <template slot="form-body">
                <snbc-form-item v-for="(item, index) in formItems" :key="index" :config="item" />
            </template>
        </snbc-form>
        <snbc-table-list :config="tableConfig" :list="paymentList" />
        <span slot="footer" class="dialog-footer">
            <template>
                <el-button type="danger" @click="handleCancel">关 闭</el-button>
                <el-button type="primary" @click="handleSure">确 定</el-button>
            </template>
        </span>
    </el-dialog>
</template>
<script>
import SnbcForm from 'warehouse/components/snbc-form/SnbcForm.vue';
import SnbcFormItem from 'warehouse/components/snbc-form/SnbcFormItem.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import { inputRequired, dateRequired, numberRequired } from 'warehouse/common/form-rules/index.js';
import SnbcTableList from 'warehouse/components/snbc-table/SnbcTableList.vue';

const { date, number } = FormItems;

const rules = {
    paymentTime: [dateRequired('支付时间')],
    paymentAmount: [inputRequired('支付金额'), numberRequired('支付金额')]
};

const formItems = [
    {
        ...date,
        modelKey: 'paymentTime',
        name: '支付时间',
        elDatePickerAttrs: {
            'value-format': 'yyyy-MM-dd',
            'pickerOptions': {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            }
        }
    },
    {
        ...number,
        modelKey: 'paymentAmount',
        name: '支付金额',
        max: Math.pow(10, 7) - 1
    }
];

export default {
    name: 'PaymentLogAddDialog',
    components: { SnbcForm, SnbcFormItem, SnbcTableList },
    data() {
        return {
            visible: false,
            paymentList: [],

            form: {
                paymentTime: '',
                paymentAmount: ''
            },
            formConfig: {
                elFormAttrs: {
                    rules,
                    'label-width': '140px'
                }
            },
            billCode: '',
            tableConfig: {
                elTableColumns: [
                    {
                        label: '序号',
                        show: true,
                        prop: 'index',
                        elTableColumnAttrs: {
                            width: 80
                        }
                    },
                    {
                        label: '支付时间',
                        prop: 'paymentTime',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '支付金额',
                        prop: 'paymentAmount',
                        show: true,
                        minWidth: 120
                    }
                ]
            },
            settlementPeriod: '',
            feeSettlement: 0
        };
    },
    computed: {
        formItems() {
            return this.formItemsHandler(formItems);
        }
    },
    methods: {
        showDialog({ paymentList, billCode, settlementPeriod, feeSettlement }) {
            this.paymentList = paymentList;
            this.billCode = billCode;
            this.settlementPeriod = settlementPeriod;
            this.feeSettlement = Number(feeSettlement);
            this.$nextTick(() => {
                this.visible = true;
            });
        },
        // 取消
        handleCancel() {
            this.$refs.snbcFormRef.getFormRef().resetFields();
            this.$nextTick(() => {
                this.visible = false;
            });
        },
        async handleSure() {
            this.$refs.snbcFormRef.getFormRef().validate((valid) => {
                if (valid) {
                    this.$emit('add', {
                        form: {
                            ...this.form,
                            billCode: this.billCode,
                            settlementPeriod: this.settlementPeriod
                        }
                    });
                    this.handleCancel();
                }
            });
        },
        // form表单选项处理
        formItemsHandler(items) {
            return items.map((item) => {
                return {
                    modelObj: this.form,
                    elFormItemAttrs: {
                        label: item.name,
                        ...(item.elFormItemAttrs || {})
                    },
                    ...item,
                    elInputNumberAttrs: {
                        min: 0,
                        controls: false,
                        precision: 2
                    }
                };
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.dialog-container {
    max-height: 60vh;
    overflow: auto;
}
</style>
