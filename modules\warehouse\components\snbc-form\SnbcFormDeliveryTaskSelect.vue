<template>
    <el-form-item v-bind="elFormItemAttrs">
        <el-select
            v-model="config.modelObj[config.modelKey]"
            v-bind="elSelectAttrs"
            :loading="loading"
            @change="handleChange"
        >
            <el-option
                v-for="(option, index) in elOptions"
                :key="index"
                :label="option.label"
                :value="option.value"
            />
        </el-select>
    </el-form-item>
</template>
<script>
export default {
    name: 'SnbcFormDeliveryTaskSelect',
    props: {
        /**
         * 组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elFormItemAttrs: {},
                    elSelectAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // 是否正在从远程获取数据
            loading: false,
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-select组件默认属性设置
            defaultElSelectAttrs: {
                clearable: true,
                filterable: true,
                remote: true,
                remoteMethod: this.remoteMethod
            },
            // 下拉数据
            elOptions: []
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-select组件应用属性
        elSelectAttrs() {
            return {
                ...this.defaultElSelectAttrs,
                placeholder: '请输入关键词搜索选择',
                ...(this.config.elSelectAttrs || {})
            };
        }
    },
    mounted() {
        this.init();
    },
    methods: {
        // 初始化时有默认值则需要查询
        init() {
            const { modelObj, modelKey } = this.config;
            if (modelObj[modelKey]) {
                this.remoteMethod(modelObj[modelKey]);
            }
        },
        // 远程调用方法
        async remoteMethod(query) {
            if (query === '') {
                this.elOptions = [];
                return;
            }
            this.loading = true;
            try {
                const res =
                    await this.$service.warehouse.comDeliveryTask.getAll();
                this.elOptions = res.result.map((item) => {
                    return {
                        ...item,
                        label: item.warehouseName,
                        value: item.warehouseName
                    };
                });
            } catch (error) {
                console.error(error);
            } finally {
                this.loading = false;
            }
        },
        // 下拉数据切换操作
        handleChange() {
            this.config.changeHandler && this.config.changeHandler();
        }
    }
};
</script>
