<template>
    <snbc-form
        class="form"
        ref="formRef"
        :form="logisticsRootForm"
        :config="logisticsRootFormConfig"
    >
        <template #form-body>
            <div class="form-body">
                <snbc-form-item
                    v-for="item in logisticsRootFormItems"
                    :key="item.modelKey"
                    :config="item"
                >
                    <template
                        v-if="
                            item.modelKey === 'mileage' &&
                            !isDetails &&
                            originMileageMsg
                        "
                        slot="append"
                        ><span style="color: red">{{
                            originMileageMsg
                        }}</span></template
                    >
                    <template
                        v-if="
                            item.modelKey === 'unitPrice' &&
                            !isDetails &&
                            originUnitPriceMsg
                        "
                        slot="append"
                        ><span style="color: red">{{
                            originUnitPriceMsg
                        }}</span></template
                    ></snbc-form-item
                >
            </div>
        </template>
    </snbc-form>
</template>
<script>
import SnbcForm from 'warehouse/components/snbc-form/SnbcForm';
import { logisticsFTLItems, logisticsLTLItems } from '../formItems';
import SnbcFormItem from 'warehouse/components/snbc-form/SnbcFormItem.vue';
import {
    inputRequired,
    selectRequired,
    maxLength
} from 'warehouse/common/form-rules/index.js';

const rules = {
    transportMode: [selectRequired('运输方式')],
    logisticsCode: [selectRequired('承运物流商')],
    carType: [selectRequired('车型')],
    // trunkFee: [inputRequired('调拨干线费')],
    // deliveryFee: [inputRequired('提送费')],
    // packFee: [inputRequired('包装费')],
    // handingFee: [inputRequired('装卸费')],
    // otherFee: [inputRequired('其他费用')],
    feeDesc: [inputRequired('费用说明'), maxLength(255)],
    logisticsTel: [
        {
            pattern: /^1[3456789]\d{9}$/,
            message: '请填写正确的手机号码',
            trigger: 'blur'
        }
    ]
};

const defaultKeys = [
    'cubicNumber',
    'trunkFee',
    'unitPrice',
    'deliveryFee',
    'packFee',
    'handingFee',
    'otherFee',
    'totalFee'
];

const form = {
    allocateTaskCode: '',
    transportMode: '',
    logisticsName: '',
    logisticsCode: '',
    logisticsTel: '',
    logisticsPerson: '',
    mileage: 0,
    carType: '',
    cubicNumber: 0,
    trunkFee: 0,
    unitPrice: 0,
    deliveryFee: 0,
    packFee: 0,
    handingFee: 0,
    otherFee: 0,
    totalFee: 0,
    feeDesc: ''
};

export default {
    name: 'LogisticsSourceForm',
    components: {
        SnbcForm,
        SnbcFormItem
    },
    props: {
        isDetails: {
            type: Boolean,
            default() {
                return false;
            }
        },
        form: {
            type: Object,
            default() {
                return {
                    ...form
                };
            }
        },
        mileage: {
            type: Number,
            default() {
                return 0;
            }
        }
    },
    data() {
        return {
            logisticsRootForm: {
                ...form
            },
            logisticsRootFormConfig: {
                elFormAttrs: {
                    rules,
                    'label-width': '140px'
                }
            },
            attrs: {},
            originMileage: 0,
            originUnitPrice: 0
        };
    },
    computed: {
        logisticsRootFormItems() {
            if (this.logisticsRootForm.transportMode === '零担') {
                return this.formItemsHandler(logisticsLTLItems);
            }
            return this.formItemsHandler(logisticsFTLItems);
        },
        totalFee() {
            return (
                Number(this.logisticsRootForm.trunkFee) +
                    Number(this.logisticsRootForm.deliveryFee) +
                    Number(this.logisticsRootForm.packFee) +
                    Number(this.logisticsRootForm.handingFee) +
                    Number(this.logisticsRootForm.otherFee) || 0
            );
        },
        unitPrice() {
            if (this.logisticsRootForm.transportMode === '零担') {
                if (
                    !this.logisticsRootForm.cubicNumber ||
                    !this.logisticsRootForm.trunkFee
                ) {
                    return 0;
                }
                return parseFloat(
                    (
                        Number(this.logisticsRootForm.trunkFee) /
                        Number(this.logisticsRootForm.cubicNumber)
                    ).toFixed(2)
                );
            }
            if (
                !this.logisticsRootForm.mileage ||
                !this.logisticsRootForm.trunkFee
            ) {
                return 0;
            }
            return parseFloat(
                (
                    Number(this.logisticsRootForm.trunkFee) /
                    Number(this.logisticsRootForm.mileage)
                ).toFixed(2)
            );
        },
        originMileageMsg() {
            if (this.mileage !== this.originMileage && this.originMileage) {
                return `原里程:${this.originMileage}`;
            }
            return '';
        },
        originUnitPriceMsg() {
            if (
                this.logisticsRootForm.unitPrice !== this.originUnitPrice &&
                this.originUnitPrice
            ) {
                return `原单价为:${this.originUnitPrice}`;
            }
            return '';
        }
    },
    watch: {
        'totalFee': function (newVal, oldVal) {
            this.logisticsRootForm.totalFee = newVal;
        },
        'logisticsRootForm.transportMode': function (newVal, oldVal) {
            if (newVal === '零担') {
                this.logisticsRootFormConfig.elFormAttrs.rules.carType = [];
            } else {
                this.logisticsRootFormConfig.elFormAttrs.rules.carType = [
                    selectRequired('车型')
                ];
            }
        },
        'logisticsRootForm.logisticsCode': function (newVal, oldVal) {
            if (
                this.form.logisticsCode ===
                    this.logisticsRootForm.logisticsCode &&
                !oldVal
            ) {
                return;
            }
            this.getSupplierInfoByCode(newVal);
        },
        'unitPrice': function (newVal, oldVal) {
            this.logisticsRootForm.unitPrice = newVal;
        },
        'form': {
            handler(newVal, OldVal) {
                this.originMileage = newVal?.mileage;
                this.originUnitPrice = newVal?.unitPrice;
                Object.assign(this.logisticsRootForm, newVal);
                if (!this.isDetails) {
                    this.$nextTick(() => {
                        this.logisticsRootForm.mileage = this.mileage;
                    });
                }
            },
            deep: true,
            immediate: true
        },
        'isDetails': {
            handler: 'init',
            immediate: true
        }
    },
    methods: {
        // 初始化
        init() {
            const attrsDetails = {
                elInputAttrs: {
                    disabled: true
                },
                elSelectAttrs: {
                    disabled: true
                },
                elInputNumberAttrs: {
                    disabled: true,
                    controls: false
                }
            };
            if (this.isDetails) {
                this.logisticsRootFormConfig.elFormAttrs.rules = {};
            }
            this.attrs = this.isDetails ? attrsDetails : {};
        },
        // form表单选项处理
        formItemsHandler(formItems) {
            return formItems.map((item) => {
                return {
                    modelObj: this.logisticsRootForm,
                    elFormItemAttrs: {
                        label: item.name,
                        ...(item.elFormItemAttrs || {})
                    },
                    ...item,
                    ...this.attrs
                };
            });
        },
        // 获取 form
        getForm() {
            for (const [key, value] of Object.entries(this.logisticsRootForm)) {
                if (defaultKeys.includes(key)) {
                    this.logisticsRootForm[key] = value ?? 0;
                }
            }
            return this.logisticsRootForm;
        },
        // 获取表单校验
        async getFormValidate() {
            return await this.$refs.formRef.getFormRef().validate();
        },
        // 表单清楚内容和校验
        resetForm() {
            this.$refs.formRef.getFormRef().resetFields();
        },
        async getSupplierInfoByCode(supplierCode) {
            const { code, result, message } =
                await this.$service.warehouse.allocation.getSupplierInfoByCode(
                    supplierCode
                );
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
                return;
            }
            if (!result || this.isDetails) return;
            this.logisticsRootForm.logisticsTel = result.supplierContactsPhone;
            this.logisticsRootForm.logisticsPerson =
                result.supplierContactsName;
            this.logisticsRootForm.logisticsName = result.supplierName;
        }
    }
};
</script>
<style lang="scss" scoped>
@import '../../../styles/mixins.scss';
.form {
    @include form-textarea(0);
}
</style>
