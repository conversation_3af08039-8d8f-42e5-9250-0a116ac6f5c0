{"_args": [["xlsx@0.14.1", "C:\\Users\\<USER>\\Desktop\\si-warehouse-page"]], "_from": "xlsx@0.14.1", "_id": "xlsx@0.14.1", "_inBundle": false, "_integrity": "sha512-7hjB5YuyJo1fuuzXQjwuxD8LSUzE4Rxu5ToC3fB5JSunZxGjLcgKg69bEFG9GYoxeVDx5GL0k1dUodlvaQNRQw==", "_location": "/wtf-core-vue/xlsx", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "xlsx@0.14.1", "name": "xlsx", "escapedName": "xlsx", "rawSpec": "0.14.1", "saveSpec": null, "fetchSpec": "0.14.1"}, "_requiredBy": ["/wtf-core-vue"], "_resolved": "http://maven.xtjc.net/repository/npm-all/xlsx/-/xlsx-0.14.1.tgz", "_spec": "0.14.1", "_where": "C:\\Users\\<USER>\\Desktop\\si-warehouse-page", "alex": {"allow": ["crash", "wtf", "holes"]}, "author": {"name": "sheetjs"}, "bin": {"xlsx": "bin/xlsx.njs"}, "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.0", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.10.2"}, "description": "SheetJS Spreadsheet data parser and writer", "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "engines": {"node": ">=0.8"}, "homepage": "http://sheetjs.com/opensource", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "license": "Apache-2.0", "main": "./xlsx", "name": "xlsx", "repository": {"type": "git", "url": "git://github.com/SheetJS/js-xlsx.git"}, "scripts": {"build": "make", "dtslint": "dtslint types", "lint": "make fullint", "pretest": "git submodule init && git submodule update", "test": "make travis"}, "types": "types", "version": "0.14.1"}