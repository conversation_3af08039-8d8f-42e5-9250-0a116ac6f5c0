<template>
    <snbc-table-list :config="tableConfig" :list="list" />
</template>
<script>
import SnbcTableList from 'warehouse/components/snbc-table/SnbcTableList.vue';

export default {
    name: 'AllocationApplicationDetails',
    components: {
        SnbcTableList
    },
    props: {
        list: {
            type: Array,
            default() {
                return [];
            }
        }
    },
    data() {
        return {
            tableConfig: {
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '客户名称',
                        prop: 'customerName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '申请数量',
                        prop: 'applyNum',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '未计划数量',
                        prop: 'unPlanNum',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '来源仓在库数量',
                        prop: 'availableNum',
                        show: true,
                        minWidth: 160
                    }
                ]
            }
        };
    }
};
</script>
<style lang="scss" scoped></style>
