import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();
    const service = {
        damageTask: {
            add(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/damage_task/add',
                    method: 'post',
                    data
                });
            },
            examine(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/damage_task/examine',
                    method: 'post',
                    data
                });
            },
            remove(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/damage_task/remove`,
                    method: 'post',
                    data,
                    params: {
                        id: data.id
                    }
                });
            },
            edit(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/damage_task/edit',
                    method: 'post',
                    data
                });
            },
            info(damageTaskCode) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/damage_task/info',
                    method: 'get',
                    params: { damageTaskCode }
                });
            },
            list(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/damage_task/list',
                    method: 'post',
                    data
                });
            },
            listSearch(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: '/damage_task/list_search',
                    method: 'post',
                    data
                });
            },
            // 货损丢失导出
            exportList(data) {
                return http({
                    baseDomain: basePath.warehouseApi.base,
                    url: `/damage_task/download_list_search`,
                    method: 'post',
                    responseType: 'blob',
                    data
                });
            }
        }
    };

    return service;
};
