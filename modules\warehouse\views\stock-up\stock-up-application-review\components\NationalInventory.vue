<template>
    <el-dialog title="公司库存" class="custom-dialog" :visible.sync="dialogVisible" v-bind="elDialogAttrs">
        <snbc-base-table ref="inventoryTableRef" :table-config="tableConfig">
            <template #table-info-top>
                <el-alert title="注意：搬运订单需选择物料，销售订单无需选择。" type="error" :closable="false" />
            </template>
        </snbc-base-table>
    </el-dialog>
</template>
<script>
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import FormItems from 'warehouse/common/form-items/index.js';
import ElAttrs from 'warehouse/common/el-attrs/index.js';

const { input } = FormItems;
const { elDialogAttrs } = ElAttrs;
// 产品名称
const productName = {
    ...input,
    name: '产品名称',
    modelKey: 'productName',
    elInputAttrs: {
        disabled: true
    }
};
// 物料编码
const materialCode = {
    ...input,
    name: '物料编码',
    modelKey: 'materialCode'
};
// 物料描述
const materialDesc = {
    ...input,
    name: '物料描述',
    modelKey: 'materialDesc'
};

// 页面Table配置
const commonTableConfig = {
    queryParams: {
        productName: '',
        materialCode: '',
        materialDesc: ''
    },
    queryConfig: {
        items: [productName, materialCode, materialDesc],
        elFormAttrs: {
            'label-width': '70px'
        }
    },
    elTableColumns: [
        { label: '序号', prop: 'index', show: true, minWidth: 50 },
        { label: '物料编码', prop: 'materialCode', show: true, minWidth: 120 },
        { label: '物料描述', prop: 'materialDesc', show: true, minWidth: 300 },
        { label: '公司仓库', prop: 'subInventoryName', show: true, minWidth: 120 },
        { label: '数量', prop: 'number', show: true, minWidth: 80 }
    ],
    selectionAble: true
};
export default {
    name: 'NationalInventory',
    components: {
        SnbcBaseTable
    },
    data() {
        const hooks = {
            queryParamsHook: this.queryParamsHook,
            tableListHook: this.tableListHook
        };
        return {
            elDialogAttrs: {
                ...elDialogAttrs,
                width: '1200px'
            },
            dialogVisible: false,
            tableConfig: {
                headerTitle: '',
                ...this.$tools.cloneDeep(commonTableConfig),
                queryApi: this.$service.warehouse.basedata.searchMaterialInfoByProduct,
                headerButtons: [
                    {
                        name: '搬运订单',
                        needSelections: true,
                        type: 'primary',
                        handleClick: this.handleRemovalOrder,
                        // 添加指令，防止快速连续点击
                        vPreventReClick: true
                    },
                    {
                        name: '销售订单',
                        type: 'primary',
                        handleClick: this.handleSaleOrder,
                        // 添加指令，防止快速连续点击
                        vPreventReClick: true
                    }
                ],
                hooks,
                selectable: this.selectable,
                // 无分页
                hasPage: false
            },
            // 原选择
            originSelectionList: [],
            // 基本信息
            info: {
                customerName: '',
                customerCode: '',
                productName: ''
            }
        };
    },
    methods: {
        openDialog(row, code) {
            // 获取当前产品名称、客户名称、客户编码信息
            Object.keys(this.info).forEach((key) => {
                this.info[key] = row[key];
            });
            // 赋值原已经选择的信息
            this.originSelectionList = row.sendOutGoodsList || [];
            // 产品名称回显
            this.tableConfig.queryParams = {
                ...this.$tools.cloneDeep(commonTableConfig.queryParams),
                productName: row.productName
            };
            // 目标区仓
            this.directionWarehouseCode = code;
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.$refs.inventoryTableRef.list = [];
                this.$refs.inventoryTableRef.handleQuery();
            });
        },
        hideDialog() {
            this.dialogVisible = false;
        },
        // 列表查询参数hook方法
        queryParamsHook(params) {
            this.tableConfig.queryParams.productName = this.info.productName;
            params.productName = this.info.productName;
            params.destinationBin = this.directionWarehouseCode;
        },
        tableListHook(list) {
            let count = 0;
            list.map((item, index) => {
                item.index = index + 1;
                count += item.number;
                return item;
            });
            this.$set(this.tableConfig, 'headerTitle', `${this.info.productName}：${count}台`);
        },
        // 搬运订单
        handleRemovalOrder() {
            const { selections } = this.$refs.inventoryTableRef;
            const list = selections.map((item) => {
                return {
                    fromWarehouseCode: 'QC-SNBC',
                    fromWarehouseName: '公司仓',
                    occupiedNumber: item.number,
                    warehouseNameAndNumber: `${item.subInventoryName}:${item.number}`,
                    type: '搬运订单',
                    orderType: 'carry',
                    ...item,
                    ...this.info
                };
            });
            this.$emit('select-warehouse', list);
            this.dialogVisible = false;
        },
        // 销售订单
        handleSaleOrder() {
            const hasSaleOrder = this.originSelectionList.some((item) => item.type === '销售订单');
            if (hasSaleOrder) {
                this.$tools.message.warning('明细中已包含销售订单，不要重复选择');
                return;
            }
            this.$emit('select-warehouse', [
                {
                    fromWarehouseCode: 'QC-SNBC',
                    fromWarehouseName: '公司仓',
                    occupiedNumber: '',
                    materialCode: '无需指定',
                    warehouseNameAndNumber: '~',
                    type: '销售订单',
                    orderType: 'sale',
                    ...this.info
                }
            ]);
            this.dialogVisible = false;
        },
        // 复选框是否可选择
        selectable(row) {
            const repeatList = this.originSelectionList.filter((item) => {
                return item.materialCode === row.materialCode && item.subInventoryName === row.subInventoryName;
            });
            if (repeatList.length !== 0) {
                return false;
            }
            return true;
        }
    }
};
</script>
