<template>
    <div class="header table-header">
        <span class="header__title">{{ headerTitle }}</span>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <template v-if="headerButtons.length > 0">
                    <el-button
                        v-for="(button, index) in headerButtons"
                        :key="index"
                        :type="button.type"
                        :disabled="isDisabledBtn(button)"
                        v-bind="button.elButtonAttrs"
                        v-preventReClick:[button.vPreventReClick]="button.vPreventTime"
                        @click.native.prevent="button.handleClick"
                        >{{ button.name }}</el-button
                    >
                </template>
                <template v-if="importButtons.length > 0">
                    <el-upload
                        style="display: inline-block; margin-left: 10px"
                        v-for="button in importButtons"
                        :key="button.name"
                        action="demo"
                        ref="upload"
                        :accept="button.accept"
                        :show-file-list="false"
                        :auto-upload="false"
                        :multiple="false"
                        :on-change="button.handleChange"
                    >
                        <el-button type="primary" v-bind="button.elButtonAttrs">{{ button.name }}</el-button>
                    </el-upload>
                </template>
                <el-popover v-model="visible" :placement="placement" :append-to-body="false" class="popover">
                    <div v-for="(column, index) in columns" :key="index" class="item">
                        <span>{{ column.label }}</span>
                        <el-switch v-model="column.show" />
                    </div>
                    <el-button
                        slot="reference"
                        icon="el-icon-setting"
                        v-bind="defaultElButtonAttrs"
                        class="column-button"
                        >列可选</el-button
                    >
                </el-popover>
            </el-col>
        </el-row>
    </div>
</template>
<script>
export default {
    name: 'SnbcTableHeaderButtons',
    props: {
        config: {
            type: Object,
            default() {
                return {
                    headerButtons: [],
                    importButtons: [],
                    headerTitle: '查询列表'
                };
            }
        },
        // 多选选中项
        selections: {
            type: Array,
            default() {
                return [];
            }
        },
        // 列配置
        columns: {
            type: Array,
            default() {
                return [];
            }
        }
    },
    data() {
        return {
            // el-button组件默认属性设置
            defaultElButtonAttrs: {
                size: 'mini'
            },
            // popover显示控制
            visible: false,
            // popover出现位置
            placement: 'bottom-end'
        };
    },
    computed: {
        // 表头标题
        headerTitle() {
            return this.config.headerTitle || '查询列表';
        },
        // 按钮配置，默认按钮属性和自定义属性合并
        headerButtons() {
            return (
                (this.config.headerButtons || [])
                    // 按钮权限过滤
                    .filter((button) => {
                        return (
                            button.permissionCode === undefined ||
                            this.$store.state.permission.btnDatas.includes(button.permissionCode)
                        );
                    })
                    .map((button) => {
                        return {
                            ...button,
                            elButtonAttrs: {
                                ...this.defaultElButtonAttrs,
                                ...(button.elButtonAttrs || {})
                            }
                        };
                    })
            );
        },
        // 导入按钮配置，默认按钮属性和自定义属性合并
        importButtons() {
            return (
                (this.config.importButtons || [])
                    // 按钮权限过滤
                    .filter((button) => {
                        return (
                            button.permissionCode === undefined ||
                            this.$store.state.permission.btnDatas.includes(button.permissionCode)
                        );
                    })
                    .map((button) => {
                        return {
                            ...button,
                            elButtonAttrs: {
                                ...this.defaultElButtonAttrs,
                                ...(button.elButtonAttrs || {})
                            }
                        };
                    })
            );
        },
        // 按钮可用
        isDisabledBtn() {
            return (button) => {
                return button.needSelections && !this.selections.length;
            };
        }
    },
    methods: {}
};
</script>
<style lang="scss" scoped>
.mb8 {
    margin: 8px;
}
.header {
    display: flex;
    margin: 0;
    align-items: center;
}
.header__title {
    display: flex !important;
    align-items: center;
    flex: 1;
    &::before {
        display: inline-block;
        content: '';
        height: 14px;
        padding-left: 8px;
        border-left: 3px solid #09c;
    }
}
.column-button {
    margin-left: 8px;
}
.popover {
    ::v-deep .el-popover {
        max-height: calc(100vh - 320px);
        overflow-y: auto;
    }
}
.item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 3px;
}
</style>
