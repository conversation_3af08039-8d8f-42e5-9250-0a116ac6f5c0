<template>
    <div>
        <snbc-base-table ref="tableRef" :table-config="tableConfig">
            <template #tabs>
                <snbc-table-tabs :tabs-config="tabsConfig" />
            </template>
        </snbc-base-table>
        <snbc-base-form-dialog ref="dialogRef" :config="dialogConfig" @submit="handleSubmit" />
        <snbc-base-form-dialog ref="remarkDialogRef" :config="remarkDialogConfig" @submit="submitRemark" />
        <batch-query-dialog ref="batchQueryDialogRef" @fresh="handleQuery" />
        <no-source-match ref="noSourceMatchRef" @fresh="handleQuery" />
    </div>
</template>

<script>
import SnbcBaseTable from 'warehouse/components/snbc-table/SnbcBaseTable.vue';
import SnbcTableTabs from 'warehouse/components/snbc-table/SnbcTableTabs.vue';
import SnbcBaseFormDialog from 'warehouse/components/snbc-dialog/SnbcBaseFormDialog.vue';
import FormRules from 'warehouse/common/form-rules';
import BatchQueryDialog from './batchQuery.vue';
import NoSourceMatch from './NoSourceMatch.vue';
import commonItems from 'warehouse/common/form-items/common-items.js';

const type = {
    ...commonItems.select,
    name: '异常原因',
    modelKey: 'type',
    elOptions: [
        { label: '盘盈提案入库', value: 'IN' },
        { label: '盘亏提案丢失', value: 'OUT' },
        { label: '杂项出入库', value: 'OUTIN' },
        { label: '其他原因', value: 'OTHER' }
    ]
};
const assetCode = {
    ...commonItems.input,
    name: '产品序列号',
    modelKey: 'assetCode',
    elInputAttrs: {
        disabled: true
    }
};
const productName = {
    ...commonItems.input,
    name: '产品名称',
    modelKey: 'productName',
    elInputAttrs: {
        disabled: true
    }
};
const invResult = {
    ...commonItems.input,
    name: '盘点结果',
    modelKey: 'invResult',
    elInputAttrs: {
        disabled: true
    }
};
const remark = {
    ...commonItems.input,
    name: '备注信息',
    modelKey: 'remark'
};
const dialogConfigItems = [assetCode, productName, invResult, type];
const remarkDialogConfigItem = [remark];
const { selectRequired, inputRequired } = FormRules;

const rules = {
    type: [selectRequired('问题原因')],
    remark: [inputRequired('备注')]
};
export default {
    name: 'UndealTable',
    components: {
        SnbcBaseTable,
        SnbcTableTabs,
        SnbcBaseFormDialog,
        BatchQueryDialog,
        NoSourceMatch
    },
    props: {
        queryConfig: {
            type: Object,
            default() {
                return {};
            }
        },
        tabsConfig: {
            type: Object,
            default() {
                return {};
            }
        }
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            tableConfig: {
                queryApi: this.$service.warehouse.inventoryManagement.getExceptionData,
                elTableColumns: [
                    { label: '序号', prop: 'index', show: true, minWidth: 80 },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '产品序列号',
                        prop: 'assetCode',
                        show: true,
                        minWidth: 180
                    },
                    {
                        label: '区仓',
                        prop: 'warehouseName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '盘点结果',
                        prop: 'invResult',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '资产状态',
                        prop: 'assetState',
                        show: true,
                        minWidth: 140
                    }
                ],

                headerButtons: [
                    {
                        name: '自动匹配补录任务',
                        type: 'primary',
                        handleClick: this.matchAsset
                    }
                ],
                operations: [
                    {
                        name: '处理',
                        type: 'primary',
                        width: 150,
                        handleClick: this.handleConfirm
                    },
                    {
                        name: '匹配无源合并',
                        type: 'danger',
                        handleShow: (row) => row.notFoundFlag === true,
                        handleClick: this.noSourceMatch
                    }
                ],
                hooks: {
                    queryParamsHook: this.queryParamsHook
                }
            },
            dialogConfig: {
                rules,
                items: dialogConfigItems
            },
            remarkDialogConfig: {
                rules,
                items: remarkDialogConfigItem
            }
        };
    },

    created() {
        this.tableConfig = {
            ...this.queryConfig,
            ...this.tableConfig
        };
    },
    methods: {
        queryParamsHook(params) {
            params.planCode = this.$route.query.planCode;
            params.type = 'undeal';
            this.queryConfig.queryParamsHook && this.queryConfig.queryParamsHook(params);
        },
        handleQuery() {
            this.$refs.tableRef.queryList();
        },
        // 不带备注的处理
        handleConfirm(row) {
            this.$refs.dialogRef.baseAddDialog(row, '结果处理');
        },
        async handleSubmit({ form }) {
            const params = this.$tools.deepClone(form);
            if (params.type === 'OTHER') {
                this.$refs.remarkDialogRef.baseAddDialog(params, '备注信息');
                return;
            }
            const { message, code } = await this.$service.warehouse.inventoryManagement.dealExceptionData(params);
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
                return;
            }
            this.$tools.message.suc('操作成功');
            this.$refs.dialogRef.hideDialog();
            this.handleQuery();
        },
        // 带备注的处理
        async submitRemark({ form }) {
            const params = this.$tools.deepClone(form);
            const { message, code } = await this.$service.warehouse.inventoryManagement.dealExceptionData(params);
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
                return;
            }
            this.$tools.message.suc('操作成功');
            this.$refs.remarkDialogRef.hideDialog();
            this.$refs.dialogRef.hideDialog();
            this.handleQuery();
        },
        // 自动匹配补录任务
        async matchAsset() {
            const { result, message, code } = await this.$service.warehouse.inventoryManagement.getMatchAsset({
                ...this.queryConfig.queryParams
            });
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
                return;
            }
            this.$refs.batchQueryDialogRef.openDialog(result);
        },
        // 无源合并
        async noSourceMatch(row) {
            const { result, message, code } = await this.$service.warehouse.inventoryManagement.serchLostData({
                taskCode: row.taskCode,
                assetCode: row.assetCode
            });
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
                return;
            }
            this.$refs.noSourceMatchRef.openDialog(row, result);
        },
        // 批量处理
        async batchDeal() {
            try {
                const { message, code } = await this.$service.warehouse.inventoryManagement.handleMatchAsset(
                    this.dealChecks
                );
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                }
                this.handleQuery();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .el-table__fixed-right {
    height: 100% !important;
}
</style>
