{"_args": [["codepage@1.14.0", "C:\\Users\\<USER>\\Desktop\\si-warehouse-page"]], "_from": "codepage@1.14.0", "_id": "codepage@1.14.0", "_inBundle": false, "_integrity": "sha512-iz3zJLhlrg37/gYRWgEPkaFTtzmnEv1h+r7NgZum2lFElYQPi0/5bnmuDfODHxfp0INEfnRqyfyeIJDbb7ahRw==", "_location": "/wtf-core-vue/codepage", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "codepage@1.14.0", "name": "codepage", "escapedName": "codepage", "rawSpec": "1.14.0", "saveSpec": null, "fetchSpec": "1.14.0"}, "_requiredBy": ["/wtf-core-vue/xlsx"], "_resolved": "http://maven.xtjc.net/repository/npm-all/codepage/-/codepage-1.14.0.tgz", "_spec": "1.14.0", "_where": "C:\\Users\\<USER>\\Desktop\\si-warehouse-page", "alex": {"allow": ["chinese", "european", "german", "japanese", "latin"]}, "author": {"name": "SheetJS"}, "bin": {"codepage": "bin/codepage.njs"}, "browser": {"buffer": "false"}, "bugs": {"url": "https://github.com/SheetJS/js-codepage/issues"}, "config": {"blanket": {"pattern": "[cputils.js]"}}, "dependencies": {"commander": "~2.14.1", "exit-on-epipe": "~1.0.1"}, "description": "pure-JS library to handle codepages", "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/commander": "^2.12.0", "@types/node": "^8.0.7", "blanket": "~1.2.3", "dtslint": "^0.1.2", "mocha": "~2.5.3", "typescript": "2.2.0", "voc": "~1.1.0"}, "engines": {"node": ">=0.8"}, "files": ["LICENSE", "README.md", "bin", "bits/*.js", "types/index.d.ts", "types/*.json", "cptable.js", "cputils.js", "dist/sbcs.full.js", "dist/cpexcel.full.js"], "homepage": "http://sheetjs.com/opensource", "keywords": ["codepage", "iconv", "convert", "strings"], "license": "Apache-2.0", "main": "cputils.js", "name": "codepage", "repository": {"type": "git", "url": "git://github.com/SheetJS/js-codepage.git"}, "scripts": {"build": "make js", "dtslint": "dtslint types", "lint": "make fullint", "pretest": "git submodule init && git submodule update", "test": "make test"}, "types": "types", "version": "1.14.0"}