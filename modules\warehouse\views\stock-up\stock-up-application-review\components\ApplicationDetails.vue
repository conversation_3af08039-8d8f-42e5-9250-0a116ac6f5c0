<template>
    <div>
        <el-dialog class="custom-dialog" :visible.sync="dialogVisible" v-bind="elDialogAttrs">
            <snbc-card title="备货基础信息">
                <template #card-body>
                    <snbc-descriptions :items="basicInfo" />
                </template>
            </snbc-card>
            <snbc-card title="备货详细信息" class="margin-top-10">
                <template #card-body>
                    <el-table :data="details.stockRequestDetailList" v-bind="elTableAttrs">
                        <el-table-column
                            v-for="column in tableConfig.elTableColumns"
                            :key="column.prop"
                            :label="column.label"
                            :prop="column.prop"
                            :min-width="column.minWidth"
                            v-bind="elTableColumnAttrs"
                        >
                            <template slot-scope="scope">
                                <!-- 单元格内容渲染 -->
                                <template v-if="!column.renderMode">{{ scope.row[column.prop] }}</template>
                                <el-input-number
                                    v-if="column.renderMode === 'inputNumber'"
                                    v-model="scope.row[column.prop]"
                                    :min="1"
                                    controls-position="right"
                                ></el-input-number>
                                <el-button
                                    v-if="column.renderMode === 'button'"
                                    type="text"
                                    @click.native.prevent="column.handleClick(scope.row)"
                                >
                                    {{ scope.row[column.prop] }}
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </snbc-card>
            <span slot="footer" class="dialog-footer">
                <el-button type="danger" @click="handleRefuse">拒绝</el-button>
                <el-button type="primary" @click="handlePass">通过</el-button>
            </span>
        </el-dialog>
        <task-details ref="taskDetailsRef" />
        <material-inventory ref="materialInventoryRef" />
    </div>
</template>

<script>
import ElAttrs from 'warehouse/common/el-attrs/index.js';
import SnbcCard from 'warehouse/components/snbc-card/SnbcCard.vue';
import SnbcDescriptions from 'warehouse/components/snbc-descriptions/SnbcDescriptions.vue';
import TaskDetails from './TaskDetails.vue';
import MaterialInventory from './MaterialInventory.vue';

const { elDialogAttrs, elTableAttrs, elTableColumnAttrs } = ElAttrs;
export default {
    name: 'StockUpApplicationReviewOfApplicationDetails',
    components: {
        SnbcCard,
        SnbcDescriptions,
        TaskDetails,
        MaterialInventory
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            elTableAttrs,
            elTableColumnAttrs: {
                ...elTableColumnAttrs,
                sortable: false
            },
            elDialogAttrs: {
                ...elDialogAttrs,
                title: '审核备货申请任务',
                width: '1000px'
            },
            dialogVisible: false,
            // 基础信息
            basicInfo: [
                { label: '备货申请任务编号', prop: 'stockRequestCode', value: '' },
                { label: '备货申请任务名称', prop: 'stockRequestName', value: '' },
                { label: '申请时间', prop: 'createTime', value: '' },
                { label: '申请人', prop: 'createUserName', value: '' },
                { label: '目标区仓', prop: 'directionWarehouseName', value: '' },
                { label: '备注', prop: 'remark', value: '' }
            ],
            // 详情列表配置
            tableConfig: {
                elTableColumns: [
                    {
                        label: '序号',
                        prop: 'index',
                        show: true,
                        minWidth: 50
                    },
                    {
                        label: '客户名称',
                        prop: 'customerName',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '产品名称',
                        prop: 'productName',
                        show: true,
                        minWidth: 160
                    },
                    {
                        label: '申请数量',
                        prop: 'requestNumber',
                        show: true,
                        minWidth: 80
                    },
                    {
                        label: '本仓库存',
                        prop: 'ownNumber',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '公司库存',
                        prop: 'erpNumber',
                        show: true,
                        minWidth: 100,
                        renderMode: 'button',
                        handleClick: this.showMaterialInventory
                    },
                    {
                        label: '核定数量',
                        prop: 'checkNumber',
                        show: true,
                        minWidth: 120,
                        renderMode: 'inputNumber'
                    },
                    {
                        label: '到货计划',
                        prop: 'expectedArrivalDate',
                        show: true,
                        minWidth: 100
                    },
                    {
                        label: '申请原因',
                        prop: 'requestCause',
                        show: true,
                        minWidth: 120
                    }
                ]
            },
            // 详情列表
            details: {
                stockRequest: {},
                stockRequestDetailList: []
            },
            // 备货申请编码
            stockRequestCode: ''
        };
    },
    computed: {},
    methods: {
        // 查询详情
        async showDetailsDialog(data) {
            const params = {
                stockRequestCode: data
            };
            this.stockRequestCode = data;
            try {
                const res = await this.$service.warehouse.stockUp.getDetails(params);
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.details = result;
                // 获取备货基本信息
                const basicInfo = result.stockRequest;
                this.basicInfo.map((item) => {
                    item.value = basicInfo[item.prop];
                    return item;
                });
                // 详情信息
                const list = result.stockRequestDetailList;
                // 添加序号
                list.map((item, index) => {
                    item.index = index + 1;
                    // 核定数量默认为申请数量
                    item.checkNumber = item.requestNumber;
                    return item;
                });
                this.details.stockRequestDetailList = list;
                this.dialogVisible = true;
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        hideDialog() {
            this.dialogVisible = false;
        },
        // 通过
        async handlePass() {
            const { stockRequestDetailList } = this.details;
            // 考虑公司库存数量未知情况
            const check = stockRequestDetailList.filter((item) => {
                // erpNumber是string类型，值可能为“未知”
                return /^[0-9]*$/.test(item.erpNumber) && item.checkNumber > Number(item.erpNumber);
            });
            let msg = '确认通过？';
            if (check.length > 0) {
                const text = check.map((item) => item.productName).join('、');
                msg = `【${text}】产品核定数量大于公司可用库存，是否继续通过？`;
            }
            await this.$tools.confirm(msg);
            const params = this.$tools.cloneDeep(this.details);
            try {
                const res = await this.$service.warehouse.stockUp.updateByAudit(params);
                const { code, message } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc(message);
                this.hideDialog();
                this.$parent.$refs.tableRef1.handleQuery();
                // 分解任务——任务明细表单
                this.$refs.taskDetailsRef.showDetailsDialog(this.stockRequestCode);
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },

        // 拒绝操作
        async handleRefuse() {
            try {
                const content = await this.$tools.prompt('请填写拒绝申请原因', '填写拒绝原因', {
                    cancelButtonText: '关闭',
                    inputType: 'textarea',
                    inputValidator: (msg) => {
                        return !!(msg && msg.length <= 255);
                    },
                    inputErrorMessage: '拒绝原因为必填, 最多可输入255个字符'
                });
                // 调用拒绝接口
                this.auditReject(content);
            } catch (e) {
                return e.message;
            }
        },
        // 拒绝操作接口调用
        async auditReject(data) {
            const params = {
                stockRequestCode: this.stockRequestCode,
                reviewRemark: data
            };
            try {
                const res = await this.$service.warehouse.stockUp.updateByAuditReject(params);
                const { code, message } = res;

                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.$tools.message.suc(message);
                this.hideDialog();
                this.$parent.$refs.tableRef1.handleQuery();
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 查看物料库存
        showMaterialInventory(row) {
            this.$refs.materialInventoryRef.showDialog(row);
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .el-input-number--medium {
    width: auto;
}
::v-deep .el-descriptions-item__content {
    max-width: 500px;
}
</style>
