import store from '../../store';

/**
 * 校验按钮是否有权限
 * @param {*} el  当前绑定按钮
 * @param {*} binding 绑定属性  binding.arg 存在且为'disabled',代表按钮没有权限时，不可用，且置灰显示，反之无权限，按钮隐藏
 */

function checkPermission(el, binding) {
    const { value } = binding;
    const roles = store.getters && store.state.permission.btnDatas;

    if (value && Array.isArray(value)) {
        if (value.length > 0) {
            const permissionRoles = value;
            const hasPermission = permissionRoles.some(role => {
                return roles.includes(role);
            });

            if (!hasPermission) {
                if(binding.arg && binding.arg === 'disabled') {
                    el && el.setAttribute('disabled','disabled');
                    el && el.classList.add('is-disabled');
                } else {
                    el.parentNode && el.parentNode.removeChild(el);
                }
            }
        }
    } else {
        throw new Error(`需要配置权限按钮指令，值为权限代码，如： v-permission="['qxdm']"`);
    }
}

export default {
    inserted(el, binding) {
        checkPermission(el, binding);
    },
    update(el, binding) {
        checkPermission(el, binding);
    }
};
