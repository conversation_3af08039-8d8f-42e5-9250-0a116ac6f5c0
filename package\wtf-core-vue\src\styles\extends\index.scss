@import './variables';
// @import './modules_element-ui.scss';
// @import './base.scss';
// @import './layout.scss';
// @import './state.scss';

.el-drawer,
.view {
    .block {
        width: 100%;
        display: flex;
        justify-content: space-between;
        flex-direction: row;
        font-size: 14px;
        height: 40px;
        line-height: 40px;
        color: #606266;
        overflow: hidden;

        & > *{
            display: inline-block;
        }

        .block__text {
            flex-grow: 1;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            // 这里设置最小宽度，让flex布局进行放大
            //width: 0px;
        }
        .block__btns {
            flex-shrink: 0;
            // 图标按钮
            & > i,
            & > .el-button {
                color: #8290a3;
                margin: 0px;
                padding: 0px 7px;
                cursor: pointer;
            }
        }
    }

    .list {
        width: 100%;
        height: 100%;
        overflow: auto;
        margin-top: 10px;

        & > .block {
            color: #606266;
        }

        & > .el-checkbox-group > .el-checkbox {
            display: block;
            height: 36px;
            line-height: 36px;
            color: #676c6f;
        }
    }

    .header + .list {
        margin-top: 0px;
    }

    .filter {
        width: 100%;
        border-bottom: 1px solid #f4f5fa;
        margin-bottom: 20px;

        & > .content,
        & > .el-form {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            width: 100%;

            & > .filter__item,
            & > .el-form-item {
                width: 33.33%;
                flex-shrink: 0;

                & > .el-form-item__label {
                    width: 120px;
                }

                & > .el-input,
                & > .el-select,
                & > .el-range-editor {
                    width: 100%;
                }
            }

            & > .filter__btns {
                flex-grow: 1;
                text-align: right;
                // 这里解决如果按钮单行，filter下边线与按钮重合问题
                margin-bottom: 22px;
            }
        }
    }
}
.back{
	font-weight: 700;
	.fa-arrow-left{
		margin-right:5px;
	}
}