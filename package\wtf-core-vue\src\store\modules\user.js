import {
    getToken,
    setToken,
    removeToken
} from '../../utils/auth';
import router, {
    resetRouter
} from '../../router';

// localStorage存储
import storage from '../../methods/storage';

// 获取存储数据，用于解决数据解密异常，导致白屏问题
const getStorageData = (key) => {
    try {
        return storage.getLocalStorage(key);
    } catch (error) {
        console.error(`获取${key}数据出现异常`);
        console.error(error);
        return '';
    }
};

const STORAGE_KEY = {
    HADLOGIN: 'hadLogin',
    NAME: 'name',
    USERINFO: 'userInfo',
    AVATAR: 'avatar',
    INTRODUCTION: 'introduction',
    ROLES: 'roles',
    MODULESROUTER: 'modulesRouter'
};

const state = {
    token: getToken(),
    hadLogin: false,
    name: getStorageData(STORAGE_KEY.NAME),
    userInfo: getStorageData(STORAGE_KEY.userInfo),
    avatar: getStorageData(STORAGE_KEY.AVATAR),
    introduction: getStorageData(STORAGE_KEY.INTRODUCTION),
    roles: getStorageData(STORAGE_KEY.ROLES),
    modulesRouter: getStorageData(STORAGE_KEY.MODULESROUTER)
};

const mutations = {
    SET_TOKEN: (state, token) => {
        state.token = token;
    },
    SET_HADLOGIN: (state, hadLogin) => {
        state.hadLogin = hadLogin;
    },
    SET_INTRODUCTION: (state, introduction) => {
        state.introduction = introduction;
    },
    SET_USERINFO: (state, userInfo) => {
        state.userInfo = userInfo;
    },
    SET_NAME: (state, name) => {
        state.name = name;
    },
    SET_AVATAR: (state, avatar) => {
        state.avatar = avatar;
    },
    SET_ROLES: (state, roles) => {
        state.roles = roles;
    },
    SET_MODULES_ROUTER: (state, data) => {
        state.modulesRouter = data;
    }
};

const actions = {
    // user login
    login({ commit }, _token) {
        commit('SET_TOKEN', _token);
        setToken(_token);
    },
    setUserInfo({ commit, dispatch, state }, userInfo) {
        const {
            routerData,
            roles,
            avatar,
            introduction
        } = userInfo;

        commit('SET_MODULES_ROUTER', routerData);
        commit('SET_HADLOGIN', true);
        commit('SET_ROLES', roles);
        commit('SET_AVATAR', avatar);
        commit('SET_INTRODUCTION', introduction);
        commit('SET_USERINFO', userInfo);
        commit('SET_NAME', userInfo.userName);

        // 存储到对于的localstorage
        storage.setLocalStorage(STORAGE_KEY.MODULESROUTER, routerData || '');
        // storage.setLocalStorage(STORAGE_KEY.HADLOGIN,true);
        storage.setLocalStorage(STORAGE_KEY.ROLES, roles || '');
        storage.setLocalStorage(STORAGE_KEY.AVATAR, avatar || '');
        storage.setLocalStorage(STORAGE_KEY.INTRODUCTION, introduction || '');
        storage.setLocalStorage(STORAGE_KEY.USERINFO, userInfo || '');
        storage.setLocalStorage(STORAGE_KEY.NAME, userInfo.userName || '');
    },
    // 设置用户头像，用于头像更改
    setUserAvatar({ commit }, avatar) {
        commit('SET_AVATAR', avatar);

        storage.setLocalStorage(STORAGE_KEY.AVATAR, avatar || '');
    },
    // 修改租户名称
    setUserName({ commit }, name) {
        commit('SET_NAME', name);
        storage.setLocalStorage(STORAGE_KEY.NAME, name || '');
    },
    // user logout
    logout({
        commit,
        state,
        dispatch
    }) {
        commit('SET_TOKEN', '');
        commit('SET_ROLES', []);
        removeToken();
        resetRouter();

        console.log('dispatch/user/logout');

        storage.removeLocalStorage(STORAGE_KEY.MODULESROUTER);
        // removeLocalStorage(STORAGE_KEY.HADLOGIN);
        storage.removeLocalStorage(STORAGE_KEY.ROLES);
        storage.removeLocalStorage(STORAGE_KEY.AVATAR);
        storage.removeLocalStorage(STORAGE_KEY.INTRODUCTION);
        storage.removeLocalStorage(STORAGE_KEY.USERINFO);
        storage.removeLocalStorage(STORAGE_KEY.NAME);

        // reset visited views and cached views
        // to fixed https://github.com/PanJiaChen/vue-element-admin/issues/2485
        dispatch('tagsView/delAllViews', null, {
            root: true
        });
    },
    // remove token
    resetToken({
        commit
    }) {
        return new Promise(resolve => {
            commit('SET_TOKEN', '');
            commit('SET_ROLES', []);
            removeToken();
            resolve();
        });
    },
    // dynamically modify permissions
    async changeRoles({
        commit,
        dispatch, state
    }, role) {
        const token = role + '-token';

        commit('SET_TOKEN', token);
        setToken(token);

        resetRouter();

        // generate accessible routes map based on roles
        const accessRoutes = await dispatch('permission/generateRoutes', state.roles, {
            root: true
        });
        console.log(accessRoutes);
        // dynamically add accessible routes
        router.addRoutes(accessRoutes);

        // reset visited views and cached views
        dispatch('tagsView/delAllViews', null, {
            root: true
        });
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
