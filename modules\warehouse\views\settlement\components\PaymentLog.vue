<template>
    <div class="content">
        <div class="task-button">
            <el-button
                v-if="['支付中', '已确认'].includes(settlementState)"
                type="primary"
                @click="handleAdd"
                >+ 新增</el-button
            >
        </div>
        <snbc-table-list :config="tableConfig" :list="list" />
        <PaymentLogAddDialog
            ref="dialog"
            @add="handleList"
        ></PaymentLogAddDialog>
    </div>
</template>
<script>
import SnbcTableList from 'warehouse/components/snbc-table/SnbcTableList.vue';
import PaymentLogAddDialog from './PaymentLogAddDialog.vue';

export default {
    name: 'PaymentLog',
    components: {
        SnbcTableList,
        PaymentLogAddDialog
    },
    props: {
        paymentList: {
            type: Array,
            default() {
                return [];
            }
        },
        billCode: {
            type: String,
            default() {
                return '';
            }
        },
        settlementPeriod: {
            type: String,
            default() {
                return '';
            }
        },
        settlementState: {
            type: String,
            default() {
                return '';
            }
        },
        feeSettlement: {
            type: String,
            default() {
                return '0';
            }
        }
    },
    data() {
        const { addPayment } = this.$service.warehouse.settlement;
        return {
            addPayment,
            tableConfig: {
                elTableColumns: [
                    {
                        label: '序号',
                        show: true,
                        prop: 'index',
                        elTableColumnAttrs: {
                            width: 80
                        }
                    },
                    {
                        label: '支付时间',
                        prop: 'paymentTime',
                        show: true,
                        minWidth: 120
                    },
                    {
                        label: '支付金额',
                        prop: 'paymentAmount',
                        show: true,
                        minWidth: 120
                    }
                ]
            },
            list: []
        };
    },
    watch: {
        paymentList: {
            handler: 'init',
            deep: true,
            immediate: true
        }
    },
    methods: {
        // 新增
        handleAdd() {
            this.$refs.dialog.showDialog({
                paymentList: this.list,
                billCode: this.billCode,
                settlementPeriod: this.settlementPeriod,
                feeSettlement: this.feeSettlement
            });
        },
        // 新增支付记录处理
        async handleList({ form }) {
            const { code, message } = await this.addPayment(form);
            if (code !== '000000') {
                this.$tools.message.err(message || '系统异常');
                return;
            }
            this.list.push({ ...form, index: this.list.length + 1 });
        },
        init(newVal, oldVal) {
            this.list = [];
            if (!Array.isArray(newVal)) return;
            newVal.forEach((item, index) => {
                this.list.push({ ...item, index: index + 1 });
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.task-button {
    display: flex;
    flex-direction: row-reverse;
    flex-wrap: nowrap;
    margin-bottom: 16px;
}
</style>
